package domain

import "context"

type Marketer struct {
	ID   int64
	Name string
}

type MarketerRepository interface {
	List(ctx context.Context, companyID int64, sb SortByParam) (*ListMarketerResponse, error)
	ListByAdAccountIDs(ctx context.Context, companyID int64, adAccountIDs []int64) (*ListMarketerByAdAccountIDsResponse, error)
}

type ListMarketerResponse struct {
	Marketers []Marketer
	Total     int64
}

type AdAccountMarketerPair struct {
	AdAccountID int64
	MarketerID  int64
}

type ListMarketerByAdAccountIDsResponse struct {
	Pairs []AdAccountMarketerPair
}
