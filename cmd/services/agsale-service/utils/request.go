package utils

import (
	"github.com/Masterminds/squirrel"
	"gitlab.com/a7923/athena-go/proto/exmsg/services"
)

func GetLeadRequest(qb squirrel.SelectBuilder, request *services.LeadRequest) (qbo squirrel.SelectBuilder, hasArgs bool) {
	if request.Id > 0 {
		qb = qb.Where(squirrel.Eq{"id": request.Id})
		hasArgs = true
	}

	if request.OrderId > 0 {
		qb = qb.Where(squirrel.Eq{"order_id": request.OrderId})
		hasArgs = true
	}

	if request.LeadType > 0 {
		qb = qb.Where(squirrel.Eq{"lead_type": request.LeadType})
		hasArgs = true
	}

	if len(request.NotInStatus) > 0 {
		qb = qb.Where(squirrel.NotEq{"state": request.NotInStatus})
		hasArgs = true
	}
	return qb, hasArgs
}
