package adcrawl

import (
	"fmt"
	"net/http"
	"net/http/httptest"
	neturl "net/url"
	"os"
	"testing"
	"time"

	"github.com/goccy/go-json"
	"github.com/hibiken/asynq"
	"github.com/spf13/cast"
	"gitlab.com/a7923/athena-go/pkg/logger"

	http2 "gitlab.com/a7923/athena-go/pkg/http"

	"github.com/stretchr/testify/assert"
)

func TestFetchAdAccountIDs(t *testing.T) {
	testcases := []struct {
		name           string
		mockResponse   string
		expectedResult *AdAccountIDFetchingResponse
	}{
		{
			name: "success, no next page",
			mockResponse: `{
				"data": [
					{ "account_id": "****************", "id": "act_9876543210987654" },
					{ "account_id": "****************", "id": "act_8765432109876543" },
					{ "account_id": "****************", "id": "act_7654321098765432" },
					{ "account_id": "****************", "id": "act_6543210987654321" },
					{ "account_id": "****************", "id": "act_5432109876543210" }
				],
				"paging": {
					"cursors": {
						"before": "****************7890123456789012",
						"after": "09876543210987654321098765432109"
					}
				}
			}`,
			expectedResult: &AdAccountIDFetchingResponse{
				adAccountIDs: []string{"****************", "****************", "****************", "****************", "****************"},
				nextPage:     "",
			},
		},
		{
			name: "success, with next page",
			mockResponse: `{
				"data": [
					{ "account_id": "****************", "id": "act_9876543210987654" },
					{ "account_id": "****************", "id": "act_8765432109876543" },
					{ "account_id": "****************", "id": "act_7654321098765432" },
					{ "account_id": "****************", "id": "act_6543210987654321" },
					{ "account_id": "****************", "id": "act_5432109876543210" }
				],
				"paging": {
					"cursors": {
						"before": "****************7890123456789012",
						"after": "09876543210987654321098765432109"
					},
					"next": "https://graph.facebook.com/v22.0"
				}
			}`,
			expectedResult: &AdAccountIDFetchingResponse{
				adAccountIDs: []string{"****************", "****************", "****************", "****************", "****************"},
				nextPage:     "https://graph.facebook.com/v22.0",
			},
		},
	}

	for _, tc := range testcases {
		t.Run(tc.name, func(t *testing.T) {
			mockSrv := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				w.Header().Set("Content-Type", "application/json")
				w.Write([]byte(tc.mockResponse))
			}))
			defer mockSrv.Close()

			f := &Fetcher{
				httpClient: mockSrv.Client(),
			}
			res, err := f.FetchAdAccountIDs(mockSrv.URL)
			assert.NoError(t, err)
			assert.Equal(t, tc.expectedResult, res)
		})
	}
}

// func TestTemp(t *testing.T) {
// 	f := NewFetcher(FetcherConfig{
// 		RequestTimeout: 30 * time.Second,
// 	})
// 	url := fmt.Sprintf(fetchSpendingsEndpoint, "***************", os.Getenv("AD_ACCESS_TOKEN"))
// 	log.Println(url)
// 	resp, err := f.FetchSpendings(url)
// 	if err != nil {
// 		t.Fatalf("error: %v", err)
// 	}
// 	for _, sp := range resp.spInfos {
// 		t.Logf("sp: %+v", sp)
// 	}
// 	t.Logf("pages: %v", len(resp.nextPages))
// }

func TestGenerateURL(t *testing.T) {
	id := int64(120212810205980115)
	accessToken := ""
	url, b := GenerateURL(string(TypeFetchSpendings), accessToken, &id, nil)
	if b != nil {
		t.Fatal(b.Error())
	}
	t.Logf("url: %v", url)

	resp := make(map[string]interface{})
	err := http2.NewRequest(&http2.RequestOptions{
		URL: url,
	}, &resp)
	if err != nil {
		t.Fatalf("error: %v", err)
	}
	t.Logf("resp: %v", resp)
}

func TestFetchSpendings(t *testing.T) {
	url := os.Getenv("AD_SPENDING_TEST_URL")
	fetcher := NewFetcher(FetcherConfig{
		RequestTimeout:      30 * time.Second,
		MaxIdleConnsPerHost: 100,
	})
	payload := FetchAdRelatedObjectsPayload{
		URL: url,
	}
	resp, err := fetcher.FetchSpendings(payload)
	if err != nil {
		t.Fatalf("failed to fetch spendings: %v", err)
	}
	for _, sp := range resp.spInfos {
		t.Logf("spending: %+v", sp)
	}

}

func TestFetchCampaignsVer2(t *testing.T) {
	url := fmt.Sprintf(fetchCampaignsEndpointV2, ***************, os.Getenv("AD_ACCESS_TOKEN"))
	fetcher := NewFetcher(FetcherConfig{
		RequestTimeout:      30 * time.Second,
		MaxIdleConnsPerHost: 100,
	})
	t.Logf("url: %v", url)
	resp, err := fetcher.FetchCampaigns(url)
	if err != nil {
		t.Fatal(err)
	}

	t.Logf("Len Next pages: %v", len(resp.nextPages))
	for _, nextPage := range resp.nextPages {
		t.Logf("next page: %v", nextPage)
	}

	for i := range resp.campaignInfos {
		t.Logf("campaign: %+v", resp.campaignInfos[i])
	}
}

func TestFetchAdsetsVer2(t *testing.T) {
	url := fmt.Sprintf(fetchAdSetsEndpointV2, 4332681093461568, os.Getenv("AD_ACCESS_TOKEN"))
	fetcher := NewFetcher(FetcherConfig{
		RequestTimeout:      30 * time.Second,
		MaxIdleConnsPerHost: 100,
	})
	t.Logf("url: %v", url)
	resp, err := fetcher.FetchAdSets(url)
	if err != nil {
		t.Fatal(err)
	}
	t.Logf("Len Next pages: %v", len(resp.nextPages))
	for _, nextPage := range resp.nextPages {
		t.Logf("next page: %v", nextPage)
	}
	for i := range resp.adsetInfos {
		t.Logf("ad set: %+v", resp.adsetInfos[i])
	}
}

func TestFetchAdsVer2(t *testing.T) {
	url := fmt.Sprintf(fetchAdsEndpointV2, ***************, os.Getenv("AD_ACCESS_TOKEN"))
	fetcher := NewFetcher(FetcherConfig{
		RequestTimeout:      30 * time.Second,
		MaxIdleConnsPerHost: 100,
	})
	t.Logf("url: %v", url)
	resp, err := fetcher.FetchAds(url)
	if err != nil {
		t.Fatal(err)
	}
	t.Logf("Len Next pages: %v", len(resp.nextPages))
	for _, nextPage := range resp.nextPages {
		t.Logf("next page: %v", nextPage)
	}
	t.Logf("Len Ads: %v", len(resp.adInfos))
	// for i := range resp.adInfos {
	// 	t.Logf("ad: %+v", resp.adInfos[i])
	// }
}

func TestExtractCountryID(t *testing.T) {
	url := fmt.Sprintf(fetchAdAccountsEndpoint, os.Getenv("AD_ACCESS_TOKEN"))
	fetcher := NewFetcher(FetcherConfig{
		RequestTimeout:      30 * time.Second,
		MaxIdleConnsPerHost: 100,
	})
	t.Logf("url: %v", url)
	resp, err := fetcher.FetchAdAccounts(url)
	if err != nil {
		t.Fatal(err)
	}
	for i := range resp.adAccountInfos {
		t.Logf("ad account %d has country: %d", resp.adAccountInfos[i].ID, resp.adAccountInfos[i].CountryID)
	}
}

type AdDetail struct {
	AdAccountId        int64  `json:"adaccount_id"`
	Token              string `json:"token"`
	AnalyticsAccountId int64  `json:"analytic_account_id"`
}

func TestMigrateAdsAccountSpending(t *testing.T) {
	/**
	SELECT
		adaccount_id,
		"token",
		analytic_account_id
	FROM
		fb.spendings s
		LEFT JOIN fb.analytic_accounts a ON s.analytic_account_id = a.id
	WHERE xxx
	GROUP BY
		1,
		2,
		3;
	*/
	jsonFile := "ads.json"
	bytes, err := os.ReadFile(jsonFile)
	if err != nil {
		t.Fatal(err)
	}

	adsData := make([]AdDetail, 0)
	if err := json.Unmarshal(bytes, &adsData); err != nil {
		t.Fatal(err)
	}

	timeRange := fmt.Sprintf(`{"since":"%s","until":"%s"}`,
		"2025-06-01",
		"2025-06-12",
	)

	taskClient := asynq.NewClient(asynq.RedisClientOpt{Addr: "localhost:6666"})
	for _, d := range adsData {
		fetchURL := fmt.Sprintf(fetchSpendingsFromAdAccountEndpoint, d.AdAccountId, d.Token)
		fetchURL += "&time_range=" + neturl.QueryEscape(timeRange)
		extraParams := map[string]interface{}{
			"analytic_account_id": cast.ToString(d.AnalyticsAccountId),
		}
		fetchTask, err := NewFetchAdRelatedObjectsTask(string(TypeFetchSpendings), fetchURL, d.Token, extraParams)
		if err != nil {
			logger.AthenaLogger.Errorf("failed to create fetch spending task", "error", err.Error())
			continue
		}

		a, err := taskClient.Enqueue(fetchTask, asynq.Timeout(30*time.Minute), asynq.Queue(FetchQueueName), asynq.MaxRetry(3))
		if err != nil {
			logger.AthenaLogger.Errorf("failed to enqueue fetch spending task", "error", err.Error())
			continue
		}
		logger.AthenaLogger.Infow("Enqueued fetch spending task", "task_id", a.ID)
	}
}
