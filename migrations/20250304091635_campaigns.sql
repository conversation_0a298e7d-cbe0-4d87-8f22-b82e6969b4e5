-- migrate:up
CREATE TABLE IF NOT EXISTS fb.campaigns (
    id BIGINT,
    company_id BIGINT,

    name TEXT,
    status TEXT,
    effective_status TEXT,
    available BOOLEAN,
    adaccount_id BIGINT,
    marketer_id BIGINT,
    updated_by_id BIGINT,
    objective TEXT,
    connected BOOLEAN,
    country_id INTEGER,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ,

    CONSTRAINT pk_campaigns PRIMARY KEY (company_id, id)
);

ALTER TABLE fb.campaigns
ADD CONSTRAINT fk_campaigns_adaccount FOREIGN KEY (company_id, adaccount_id)
REFERENCES fb.adaccounts(company_id, id) ON DELETE CASCADE;

CREATE INDEX IF NOT EXISTS idx_campaigns_company_id_id ON fb.campaigns (company_id, id);
CREATE INDEX IF NOT EXISTS idx_campaigns_company_id_adaccount_id ON fb.campaigns (company_id, adaccount_id);

ALTER TABLE fb.campaigns ADD CONSTRAINT unique_company_id_id_3 UNIQUE (company_id, id);

-- migrate:down
DROP INDEX IF EXISTS fb.idx_campaigns_company_id_adaccount_id;
DROP INDEX IF EXISTS fb.idx_campaigns_company_id_id;
ALTER TABLE fb.campaigns DROP CONSTRAINT IF EXISTS fk_campaigns_adaccount;
DROP TABLE IF EXISTS fb.campaigns;
