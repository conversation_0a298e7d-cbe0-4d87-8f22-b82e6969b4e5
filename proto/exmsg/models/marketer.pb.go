// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: proto/models/marketer.proto

package models

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Marketer struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Marketer) Reset() {
	*x = Marketer{}
	mi := &file_proto_models_marketer_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Marketer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Marketer) ProtoMessage() {}

func (x *Marketer) ProtoReflect() protoreflect.Message {
	mi := &file_proto_models_marketer_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Marketer.ProtoReflect.Descriptor instead.
func (*Marketer) Descriptor() ([]byte, []int) {
	return file_proto_models_marketer_proto_rawDescGZIP(), []int{0}
}

func (x *Marketer) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Marketer) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

var File_proto_models_marketer_proto protoreflect.FileDescriptor

var file_proto_models_marketer_proto_rawDesc = string([]byte{
	0x0a, 0x1b, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d,
	0x61, 0x72, 0x6b, 0x65, 0x74, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c, 0x65,
	0x78, 0x6d, 0x73, 0x67, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x22, 0x2e, 0x0a, 0x08, 0x4d,
	0x61, 0x72, 0x6b, 0x65, 0x74, 0x65, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x36, 0x5a, 0x34, 0x67,
	0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x61, 0x37, 0x39, 0x32, 0x33, 0x2f,
	0x61, 0x74, 0x68, 0x65, 0x6e, 0x61, 0x2d, 0x67, 0x6f, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f,
	0x65, 0x78, 0x6d, 0x73, 0x67, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x3b, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_proto_models_marketer_proto_rawDescOnce sync.Once
	file_proto_models_marketer_proto_rawDescData []byte
)

func file_proto_models_marketer_proto_rawDescGZIP() []byte {
	file_proto_models_marketer_proto_rawDescOnce.Do(func() {
		file_proto_models_marketer_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proto_models_marketer_proto_rawDesc), len(file_proto_models_marketer_proto_rawDesc)))
	})
	return file_proto_models_marketer_proto_rawDescData
}

var file_proto_models_marketer_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_proto_models_marketer_proto_goTypes = []any{
	(*Marketer)(nil), // 0: exmsg.models.Marketer
}
var file_proto_models_marketer_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_proto_models_marketer_proto_init() }
func file_proto_models_marketer_proto_init() {
	if File_proto_models_marketer_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proto_models_marketer_proto_rawDesc), len(file_proto_models_marketer_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_proto_models_marketer_proto_goTypes,
		DependencyIndexes: file_proto_models_marketer_proto_depIdxs,
		MessageInfos:      file_proto_models_marketer_proto_msgTypes,
	}.Build()
	File_proto_models_marketer_proto = out.File
	file_proto_models_marketer_proto_goTypes = nil
	file_proto_models_marketer_proto_depIdxs = nil
}
