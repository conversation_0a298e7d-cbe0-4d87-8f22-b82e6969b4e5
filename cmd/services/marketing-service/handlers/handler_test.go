package handlers

import (
	"context"
	"gitlab.com/a7923/athena-go/pkg/dbtool"
	"gitlab.com/a7923/athena-go/proto/exmsg/models"
	"gitlab.com/a7923/athena-go/proto/exmsg/services"
	"gitlab.com/a7923/athena-go/tests"
	"log"
	"testing"
)

var marketingHandler *MarketingHandler
var ctx = context.Background()

func init() {
	err := tests.LoadTestConfig()
	if err != nil {
		log.Fatal("Failed to load config", err)
	}

	psql, err := dbtool.NewConnectionManager(dbtool.DBTypePostgreSQL, nil)
	if err != nil {
		log.Fatal("Failed to connect to database: ", err)
	}
	marketingHandler = NewMarketingHandler(psql)
}

//func TestGetLeads(t *testing.T) {
//	// Test GetLeads
//	resp := services.LeadResponse{}
//	err := mae.GetLeads(ctx, &services.LeadRequest{}, &resp)
//	if err != nil {
//		t.Fatal("Failed to get leads", err)
//	}
//	t.Log("Leads: ", resp.Leads)
//}

func TestMarketingHandler_CreateAdsBiz(t *testing.T) {
	// Test GetLeads
	resp := services.MarketingResponse{}
	err := marketingHandler.CreateAdsBiz(ctx, &services.MarketingRequest{
		Adsbiz: &models.AdBiz{
			AdId:          "123",
			CountryId:     1,
			ProjectId:     2,
			CompanyId:     3,
			ProductId:     4,
			LandingPageId: "65ffcf01-d5f5-4757-83f5-f70f2a00d62f",
		},
	}, &resp)
	if err != nil {
		t.Fatal("Failed to get leads", err)
	}
	t.Log("Leads: ", resp.ExecResult)
}
