syntax = "proto3";

package exmsg.services;

option go_package = "gitlab.com/a7923/athena-go/proto/exmsg/services;services";

import "models/user.proto";
import "models/department.proto";
import "models/role.proto";
import "models/common.proto";
import "models/project.proto";

service UserService {
  rpc GetUserByID (UserRequest) returns (UserResponse);

  rpc GetProfiles(ProfileRequest) returns (ProfileResponse);
  rpc GetDepartments (DepartmentRequest) returns (DepartmentResponse);
  rpc GetRoles(RoleRequest) returns (RoleResponse);
  rpc GetDataSetScopes(DataSetScopeRequest) returns (DataSetScopeResponse);
  rpc GetProjects(ProjectRequest) returns (ProjectResponse);

  rpc GetUsersByOptions(GetUsersByOptionsRequest) returns (GetUsersByOptionsResponse);
  rpc GetUserPermission(GetUserPermissionRequest) returns (GetUserPermissionResponse);
  rpc GetUserProfile(GetUserPermissionRequest) returns (GetUserProfileResponse);
}

message UserRequest {
  int64 user_id = 1;
}

message ProfileRequest {
  int64 user_id = 1;
  exmsg.models.Query query = 2;
  int64 status = 4;
  repeated int64 department_ids = 5;
  repeated int64 ancestor_department_ids = 6;
}

message RoleRequest {
  int64 role_id = 1;
  repeated int64 role_ids = 2;
  int64 user_id = 3;
  exmsg.models.Query query = 4;

}

message DepartmentRequest {
  int64 department_id = 1;
  repeated int64 department_ids = 2;
  repeated int64 ancestor_ids = 3;
  exmsg.models.Query query = 4;
}

message DataSetScopeRequest {
  int64 data_set_id = 1;
  repeated int64 data_set_ids = 2;
  repeated int64 department_ids = 3;
  exmsg.models.Query query = 4;
}

message UserResponse {
  exmsg.models.User user = 1;
  bool cache_hit = 2;
}

message ProfileResponse {
  repeated exmsg.models.UserProfile profiles = 1;
  uint64 total_count = 2;
}

message DepartmentResponse {
  repeated exmsg.models.Department department = 1;
  uint64 total_count = 2;
}

message RoleResponse {
  repeated exmsg.models.Role roles = 1;
  uint64 total_count = 2;
}

message DataSetScopeResponse {
  repeated exmsg.models.DataSetScope scopes = 1;
  uint64 total_count = 2;
}

message ProjectRequest {
  repeated int64 project_ids = 1;
  exmsg.models.Query query = 2;
}

message ProjectResponse {
  repeated exmsg.models.Project projects = 1;
  uint64 total_count = 2;
}

message GetUsersByOptionsRequest {
  int64 company_id = 1;
  string sort_field = 2;
  bool is_asc = 3;
  repeated int64 ids = 4;
}

message GetUsersByOptionsResponse {
  repeated exmsg.models.User users = 1;
}

message GetUserPermissionRequest {
  int64 user_id = 1;
}

message GetUserPermissionResponse {
  bool has_full_permission = 1;
  bool allow_access_descendants = 2;
}

message GetUserProfileResponse {
  repeated string profiles = 1;
}