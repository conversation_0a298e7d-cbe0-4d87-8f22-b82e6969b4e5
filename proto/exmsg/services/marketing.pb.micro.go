// Code generated by protoc-gen-micro. DO NOT EDIT.
// source: services/marketing.proto

package services

import (
	fmt "fmt"
	_ "gitlab.com/a7923/athena-go/proto/exmsg/models"
	proto "google.golang.org/protobuf/proto"
	math "math"
)

import (
	context "context"
	client "go-micro.dev/v5/client"
	server "go-micro.dev/v5/server"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ client.Option
var _ server.Option

// Client API for MarketingService service

type MarketingService interface {
	CreateAdsBiz(ctx context.Context, in *MarketingRequest, opts ...client.CallOption) (*MarketingResponse, error)
}

type marketingService struct {
	c    client.Client
	name string
}

func NewMarketingService(name string, c client.Client) MarketingService {
	return &marketingService{
		c:    c,
		name: name,
	}
}

func (c *marketingService) CreateAdsBiz(ctx context.Context, in *MarketingRequest, opts ...client.CallOption) (*MarketingResponse, error) {
	req := c.c.NewRequest(c.name, "MarketingService.CreateAdsBiz", in)
	out := new(MarketingResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for MarketingService service

type MarketingServiceHandler interface {
	CreateAdsBiz(context.Context, *MarketingRequest, *MarketingResponse) error
}

func RegisterMarketingServiceHandler(s server.Server, hdlr MarketingServiceHandler, opts ...server.HandlerOption) error {
	type marketingService interface {
		CreateAdsBiz(ctx context.Context, in *MarketingRequest, out *MarketingResponse) error
	}
	type MarketingService struct {
		marketingService
	}
	h := &marketingServiceHandler{hdlr}
	return s.Handle(s.NewHandler(&MarketingService{h}, opts...))
}

type marketingServiceHandler struct {
	MarketingServiceHandler
}

func (h *marketingServiceHandler) CreateAdsBiz(ctx context.Context, in *MarketingRequest, out *MarketingResponse) error {
	return h.MarketingServiceHandler.CreateAdsBiz(ctx, in, out)
}
