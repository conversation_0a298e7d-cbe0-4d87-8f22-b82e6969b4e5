package handlers

import (
	"context"
	"errors"
	"github.com/Masterminds/squirrel"
	"github.com/spf13/cast"
	"gitlab.com/a7923/athena-go/cmd/services/agsale-service/tables"
	"gitlab.com/a7923/athena-go/pkg/dbtool"
	"gitlab.com/a7923/athena-go/pkg/logger"
	"gitlab.com/a7923/athena-go/proto/exmsg/models"
	"gitlab.com/a7923/athena-go/proto/exmsg/services"
)

func (a *AGSaleHandler) CreateOrders(ctx context.Context, request *services.OrderRequest, response *services.OrderResponse) error {
	sqlTool := dbtool.NewInsert(ctx, a.db.GetConnection(), tables.GetOrderTable(), &models.Order{})
	qb := squirrel.
		Insert(sqlTool.GetTable("")).
		Columns(sqlTool.GetQueryColumnList("")...)

	for _, order := range request.InsertOrders {
		qb = qb.Values(sqlTool.GetFilledValues(order)...)
	}

	res, err := sqlTool.Insert(ctx, qb)
	if err != nil {
		logger.AthenaLogger.Errorw("Failed to insert orders", "error", err)
		return err
	}

	response.ExecResult = res
	return nil
}

func (a *AGSaleHandler) GetOrders(ctx context.Context, request *services.OrderRequest, response *services.OrderResponse) error {
	sqlTool := dbtool.NewSelect(ctx, a.db.GetConnection(), tables.GetOrderTable(), &models.Order{})
	limit := dbtool.GetQueryLimit(request.Query.GetLimit(), 50, 250)
	offset := dbtool.GetQueryOffset(limit, request.Query.GetPage())

	qb := squirrel.
		Select(sqlTool.GetQueryColumnList("")...).
		From(sqlTool.GetTable("")).Limit(limit).Offset(offset)

	if len(request.Ids) > 0 {
		qb = qb.Where(squirrel.Eq{"id": request.Ids})
	} else if request.Id > 0 {
		qb = qb.Where(squirrel.Eq{"id": request.Id})
	} else {
		// must be non-empty condition here
	}

	if request.SourceId > 0 {
		qb = qb.Where(squirrel.Eq{"source_id": request.SourceId})
	}

	if request.NotEqualStatus > 0 {
		qb = qb.Where(squirrel.NotEq{"status": request.NotEqualStatus})
	}

	if request.DisplayId != "" {
		qb = qb.Where(squirrel.Eq{"display_id": request.DisplayId})
	}

	if request.CompanyId > 0 {
		qb = qb.Where(squirrel.Eq{"company_id": request.CompanyId})
	}

	response.Orders = make([]*models.Order, 0)
	err := sqlTool.Select(ctx, &response.Orders, qb)
	if err != nil {
		logger.AthenaLogger.Errorw("Failed to scan row", "error", err)
		return err
	}
	return nil
}

func (a *AGSaleHandler) GetOrderProducts(ctx context.Context, request *services.OrderRequest, response *services.OrderResponse) error {
	sqlTool := dbtool.NewSelect(ctx, a.db.GetConnection(), tables.GetOrderProductTable(), &models.OrderProduct{})
	limit := dbtool.GetQueryLimit(request.Query.GetLimit(), 50, 250)
	offset := dbtool.GetQueryOffset(limit, request.Query.GetPage())

	qb := squirrel.
		Select(sqlTool.GetQueryColumnList("")...).
		From(sqlTool.GetTable("")).Limit(limit).Offset(offset)

	qb = qb.Where(squirrel.Eq{"order_id": request.OrderId})

	response.OrderProducts = make([]*models.OrderProduct, 0)
	err := sqlTool.Select(ctx, &response.OrderProducts, qb)
	if err != nil {
		logger.AthenaLogger.Errorw("Failed to scan row", "error", err)
		return err
	}
	return nil
}

func (a *AGSaleHandler) GetOrderDisplayId(ctx context.Context, request *services.OrderDisplayIdRequest, response *services.OrderResponse) error {
	sqlTool := dbtool.NewSelect(ctx, a.db.GetConnection(), tables.GetLeadTable(), &models.Lead{})
	res, err := sqlTool.ExecQuery("SELECT * FROM daily_order_display_id_sequences($1) AS seq(project_id INT, date DATE, count INT)", request.ProjectId)
	if err != nil {
		logger.AthenaLogger.Errorw("Failed to get order display id", "error", err)
		return err
	}
	var item *services.OrderDisplayIdCount = &services.OrderDisplayIdCount{}
	for res.Next() {
		if err := res.StructScan(item); err != nil {
			logger.AthenaLogger.Errorw("Failed to get order display id", "error", err)
		}
	}
	response.OrderDisplayIdCount = item
	return nil
}

func (a *AGSaleHandler) UpdateOrder(ctx context.Context, request *services.OrderRequest, response *services.OrderResponse) error {
	if request.Order == nil {
		return errors.New("order is required")
	}

	if len(request.UpdateFields) == 0 {
		return errors.New("update fields are required")
	}

	if request.Id < 1 {
		return errors.New("id is required")
	}

	sqlTool := dbtool.NewUpdate(ctx, a.db.GetConnection(), tables.GetOrderTable(), request.Order)
	qb := squirrel.
		Update(sqlTool.GetTable("")).
		SetMap(sqlTool.GetUpdateMap(request.Order, request.UpdateFields...)).
		Where(squirrel.Eq{"id": request.Id})

	res, err := sqlTool.Update(ctx, qb)
	if err != nil {
		logger.AthenaLogger.Errorw("Failed to update order", "error", err)
		return err
	}
	response.ExecResult = res
	return nil
}

func (a *AGSaleHandler) RemoveDuplicateOrder(ctx context.Context, request *services.OrderRequest, response *services.OrderResponse) error {
	if request.Order == nil {
		return errors.New("order is required")
	}
	sqlTool, err := dbtool.NewTransaction(ctx, a.db.GetConnection())
	if err != nil {
		logger.AthenaLogger.Errorw("Failed to start transaction", "error", err)
		return err
	}

	defer func() {
		if err != nil {
			rerr := sqlTool.RollbackTransactions()
			if rerr != nil {
				logger.AthenaLogger.Errorw("Failed to rollback transaction remove duplicate order", "error", rerr, "order_id", request.Id)
			}
		}
	}()

	// search duplicate order of this order
	var possibleOrders []*models.PossibleDuplicateOrders = nil
	sqlTool.PrepareSelect(ctx, tables.GetPossibleDuplicateOrders(), &models.PossibleDuplicateOrders{})
	qb := squirrel.
		Select(sqlTool.GetQueryColumnList("")...).
		From(sqlTool.GetTable("")).
		Where(squirrel.Eq{"order_id": request.Order.Id})
	err = sqlTool.Select(ctx, &possibleOrders, qb)
	if err != nil {
		logger.AthenaLogger.Errorw("Failed to get possible duplicate orders", "error", err)
		return err
	}

	// remove duplicate data
	sqlTool.PrepareDelete(ctx, tables.GetPossibleDuplicateOrders(), &models.PossibleDuplicateOrders{})
	dqb := squirrel.Delete(sqlTool.GetTable("")).Where(squirrel.Or{
		squirrel.Eq{"order_id": request.Order.Id},
		squirrel.Eq{"possible_duplicate_order_id": request.Order.Id},
	})
	_, dErr := sqlTool.Delete(ctx, dqb)
	if dErr != nil {
		logger.AthenaLogger.Errorw("Failed to delete order", "error", dErr)
	}

	// add record to removed duplicate order
	if len(possibleOrders) > 0 {
		var removedDuplicateOrders []*models.RemovedDuplicateOrders = nil
		for _, possibleOrder := range possibleOrders {
			removedDuplicateOrders = append(removedDuplicateOrders, &models.RemovedDuplicateOrders{
				OrderId:          possibleOrder.OrderId,
				DuplicateOrderId: possibleOrder.PossibleDuplicateOrderId,
			})
			removedDuplicateOrders = append(removedDuplicateOrders, &models.RemovedDuplicateOrders{
				OrderId:          possibleOrder.PossibleDuplicateOrderId,
				DuplicateOrderId: possibleOrder.OrderId,
			})
		}
		sqlTool.PrepareInsert(ctx, tables.GetRemovedDuplicateOrders(), &models.RemovedDuplicateOrders{})
		iQb := squirrel.
			Insert(sqlTool.GetTable("")).
			Columns(sqlTool.GetQueryColumnList("")...).
			Values(sqlTool.GetFilledValues(removedDuplicateOrders)...)
		_, iErr := sqlTool.Insert(ctx, iQb)
		if iErr != nil {
			logger.AthenaLogger.Errorw("Failed to insert removed duplicate order", "error", iErr)
			return iErr
		}

		// add log remove duplicate order
		var logsRemoveDuplicateOrders []*models.Log = nil
		for _, possibleOrder := range possibleOrders {
			logsRemoveDuplicateOrders = append(logsRemoveDuplicateOrders, &models.Log{
				TableName: "orders",
				Action:    "REMOVE_DUPLICATE",
				RecordId:  cast.ToString(possibleOrder.OrderId),
				Changes: []string{
					"order_id",
					request.Order.DisplayId,
					"order_duplicate_display_ids",
					possibleOrder.PossibleDuplicateOrderDisplayId,
				},
			})
			logsRemoveDuplicateOrders = append(logsRemoveDuplicateOrders, &models.Log{
				TableName: "orders",
				Action:    "REMOVE_DUPLICATE",
				RecordId:  cast.ToString(possibleOrder.OrderId),
				Changes: []string{
					"order_id",
					possibleOrder.PossibleDuplicateOrderDisplayId,
					"order_duplicate_display_ids",
					request.Order.DisplayId,
				},
			})
		}
		sqlTool.PrepareInsert(ctx, tables.GetLogTable(), &models.Log{})
		iLogQb := squirrel.
			Insert(sqlTool.GetTable("")).
			Columns(sqlTool.GetQueryColumnList("")...).
			Values(sqlTool.GetFilledValues(logsRemoveDuplicateOrders)...)
		_, iLogErr := sqlTool.Insert(ctx, iLogQb)
		if iLogErr != nil {
			logger.AthenaLogger.Errorw("Failed to insert removed duplicate order log", "error", iLogErr)
			return iLogErr
		}
	}
	err = sqlTool.CommitTransactions()
	if err != nil {
		logger.AthenaLogger.Errorw("Failed to commit transaction", "error", err)
		return err
	}
	response.ExecResult = &models.SQLResult{
		RowsAffected: 0,
	}
	logger.AthenaLogger.Infow("Successfully remove duplicate orders")
	return nil
}
