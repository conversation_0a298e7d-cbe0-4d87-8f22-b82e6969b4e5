// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: proto/models/campaign.proto

package models

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Campaign struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	Id                  int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Status              string                 `protobuf:"bytes,3,opt,name=status,proto3" json:"status,omitempty"`
	EffectiveStatus     string                 `protobuf:"bytes,4,opt,name=effective_status,json=effectiveStatus,proto3" json:"effective_status,omitempty"`
	Avaiable            *bool                  `protobuf:"varint,5,opt,name=avaiable,proto3,oneof" json:"avaiable,omitempty"`
	AdaccountId         int64                  `protobuf:"varint,6,opt,name=adaccount_id,json=adaccountId,proto3" json:"adaccount_id,omitempty"`
	Objective           *string                `protobuf:"bytes,7,opt,name=objective,proto3,oneof" json:"objective,omitempty"`
	CountryId           *int64                 `protobuf:"varint,8,opt,name=country_id,json=countryId,proto3,oneof" json:"country_id,omitempty"`
	MarketerId          *int64                 `protobuf:"varint,9,opt,name=marketer_id,json=marketerId,proto3,oneof" json:"marketer_id,omitempty"`
	AdaccountName       *string                `protobuf:"bytes,10,opt,name=adaccount_name,json=adaccountName,proto3,oneof" json:"adaccount_name,omitempty"`
	AnalyticAccountId   *int64                 `protobuf:"varint,11,opt,name=analytic_account_id,json=analyticAccountId,proto3,oneof" json:"analytic_account_id,omitempty"`
	AnalyticAccountName *string                `protobuf:"bytes,12,opt,name=analytic_account_name,json=analyticAccountName,proto3,oneof" json:"analytic_account_name,omitempty"`
	UpdatedById         *int64                 `protobuf:"varint,13,opt,name=updated_by_id,json=updatedById,proto3,oneof" json:"updated_by_id,omitempty"`
	UpdatedAt           *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=updated_at,json=updatedAt,proto3,oneof" json:"updated_at,omitempty"`
	CompanyId           *int64                 `protobuf:"varint,15,opt,name=company_id,json=companyId,proto3,oneof" json:"company_id,omitempty"`
	LandingId           *string                `protobuf:"bytes,16,opt,name=landing_id,json=landingId,proto3,oneof" json:"landing_id,omitempty"`
	LandingName         *string                `protobuf:"bytes,17,opt,name=landing_name,json=landingName,proto3,oneof" json:"landing_name,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *Campaign) Reset() {
	*x = Campaign{}
	mi := &file_proto_models_campaign_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Campaign) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Campaign) ProtoMessage() {}

func (x *Campaign) ProtoReflect() protoreflect.Message {
	mi := &file_proto_models_campaign_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Campaign.ProtoReflect.Descriptor instead.
func (*Campaign) Descriptor() ([]byte, []int) {
	return file_proto_models_campaign_proto_rawDescGZIP(), []int{0}
}

func (x *Campaign) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Campaign) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Campaign) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *Campaign) GetEffectiveStatus() string {
	if x != nil {
		return x.EffectiveStatus
	}
	return ""
}

func (x *Campaign) GetAvaiable() bool {
	if x != nil && x.Avaiable != nil {
		return *x.Avaiable
	}
	return false
}

func (x *Campaign) GetAdaccountId() int64 {
	if x != nil {
		return x.AdaccountId
	}
	return 0
}

func (x *Campaign) GetObjective() string {
	if x != nil && x.Objective != nil {
		return *x.Objective
	}
	return ""
}

func (x *Campaign) GetCountryId() int64 {
	if x != nil && x.CountryId != nil {
		return *x.CountryId
	}
	return 0
}

func (x *Campaign) GetMarketerId() int64 {
	if x != nil && x.MarketerId != nil {
		return *x.MarketerId
	}
	return 0
}

func (x *Campaign) GetAdaccountName() string {
	if x != nil && x.AdaccountName != nil {
		return *x.AdaccountName
	}
	return ""
}

func (x *Campaign) GetAnalyticAccountId() int64 {
	if x != nil && x.AnalyticAccountId != nil {
		return *x.AnalyticAccountId
	}
	return 0
}

func (x *Campaign) GetAnalyticAccountName() string {
	if x != nil && x.AnalyticAccountName != nil {
		return *x.AnalyticAccountName
	}
	return ""
}

func (x *Campaign) GetUpdatedById() int64 {
	if x != nil && x.UpdatedById != nil {
		return *x.UpdatedById
	}
	return 0
}

func (x *Campaign) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *Campaign) GetCompanyId() int64 {
	if x != nil && x.CompanyId != nil {
		return *x.CompanyId
	}
	return 0
}

func (x *Campaign) GetLandingId() string {
	if x != nil && x.LandingId != nil {
		return *x.LandingId
	}
	return ""
}

func (x *Campaign) GetLandingName() string {
	if x != nil && x.LandingName != nil {
		return *x.LandingName
	}
	return ""
}

var File_proto_models_campaign_proto protoreflect.FileDescriptor

var file_proto_models_campaign_proto_rawDesc = string([]byte{
	0x0a, 0x1b, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x63,
	0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c, 0x65,
	0x78, 0x6d, 0x73, 0x67, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x1a, 0x1f, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xe4, 0x06, 0x0a,
	0x08, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x29, 0x0a, 0x10, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0f, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x1f, 0x0a, 0x08, 0x61, 0x76, 0x61, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x08, 0x48, 0x00, 0x52, 0x08, 0x61, 0x76, 0x61, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x64, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x61, 0x64, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x09, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x09, 0x6f, 0x62, 0x6a, 0x65, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x88, 0x01, 0x01, 0x12, 0x22, 0x0a, 0x0a, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x48, 0x02, 0x52, 0x09, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x24, 0x0a, 0x0b, 0x6d,
	0x61, 0x72, 0x6b, 0x65, 0x74, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03,
	0x48, 0x03, 0x52, 0x0a, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x65, 0x72, 0x49, 0x64, 0x88, 0x01,
	0x01, 0x12, 0x2a, 0x0a, 0x0e, 0x61, 0x64, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x48, 0x04, 0x52, 0x0d, 0x61, 0x64, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x33, 0x0a,
	0x13, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x48, 0x05, 0x52, 0x11, 0x61, 0x6e,
	0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x88,
	0x01, 0x01, 0x12, 0x37, 0x0a, 0x15, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x5f, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x06, 0x52, 0x13, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x27, 0x0a, 0x0d, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x03, 0x48, 0x07, 0x52, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x49,
	0x64, 0x88, 0x01, 0x01, 0x12, 0x3e, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x48, 0x08, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x88, 0x01, 0x01, 0x12, 0x22, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f,
	0x69, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x03, 0x48, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x22, 0x0a, 0x0a, 0x6c, 0x61, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x48, 0x0a, 0x52, 0x09,
	0x6c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x26, 0x0a, 0x0c,
	0x6c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x11, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x0b, 0x52, 0x0b, 0x6c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x4e, 0x61, 0x6d,
	0x65, 0x88, 0x01, 0x01, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x61, 0x62, 0x6c,
	0x65, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x42,
	0x0d, 0x0a, 0x0b, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x42, 0x0e,
	0x0a, 0x0c, 0x5f, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x42, 0x11,
	0x0a, 0x0f, 0x5f, 0x61, 0x64, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x42, 0x16, 0x0a, 0x14, 0x5f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x5f, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x42, 0x18, 0x0a, 0x16, 0x5f, 0x61, 0x6e,
	0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x62, 0x79, 0x5f, 0x69, 0x64, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x5f, 0x69, 0x64, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x6c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f,
	0x69, 0x64, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x6c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x42, 0x36, 0x5a, 0x34, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x61, 0x37, 0x39, 0x32, 0x33, 0x2f, 0x61, 0x74, 0x68, 0x65, 0x6e, 0x61, 0x2d, 0x67,
	0x6f, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x3b, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
})

var (
	file_proto_models_campaign_proto_rawDescOnce sync.Once
	file_proto_models_campaign_proto_rawDescData []byte
)

func file_proto_models_campaign_proto_rawDescGZIP() []byte {
	file_proto_models_campaign_proto_rawDescOnce.Do(func() {
		file_proto_models_campaign_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proto_models_campaign_proto_rawDesc), len(file_proto_models_campaign_proto_rawDesc)))
	})
	return file_proto_models_campaign_proto_rawDescData
}

var file_proto_models_campaign_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_proto_models_campaign_proto_goTypes = []any{
	(*Campaign)(nil),              // 0: exmsg.models.Campaign
	(*timestamppb.Timestamp)(nil), // 1: google.protobuf.Timestamp
}
var file_proto_models_campaign_proto_depIdxs = []int32{
	1, // 0: exmsg.models.Campaign.updated_at:type_name -> google.protobuf.Timestamp
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_proto_models_campaign_proto_init() }
func file_proto_models_campaign_proto_init() {
	if File_proto_models_campaign_proto != nil {
		return
	}
	file_proto_models_campaign_proto_msgTypes[0].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proto_models_campaign_proto_rawDesc), len(file_proto_models_campaign_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_proto_models_campaign_proto_goTypes,
		DependencyIndexes: file_proto_models_campaign_proto_depIdxs,
		MessageInfos:      file_proto_models_campaign_proto_msgTypes,
	}.Build()
	File_proto_models_campaign_proto = out.File
	file_proto_models_campaign_proto_goTypes = nil
	file_proto_models_campaign_proto_depIdxs = nil
}
