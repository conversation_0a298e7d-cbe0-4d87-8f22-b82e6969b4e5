// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v3.5.1
// source: services/marketing.proto

package services

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	MarketingService_CreateAdsBiz_FullMethodName = "/exmsg.services.MarketingService/CreateAdsBiz"
)

// MarketingServiceClient is the client API for MarketingService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type MarketingServiceClient interface {
	CreateAdsBiz(ctx context.Context, in *MarketingRequest, opts ...grpc.CallOption) (*MarketingResponse, error)
}

type marketingServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewMarketingServiceClient(cc grpc.ClientConnInterface) MarketingServiceClient {
	return &marketingServiceClient{cc}
}

func (c *marketingServiceClient) CreateAdsBiz(ctx context.Context, in *MarketingRequest, opts ...grpc.CallOption) (*MarketingResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(MarketingResponse)
	err := c.cc.Invoke(ctx, MarketingService_CreateAdsBiz_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MarketingServiceServer is the server API for MarketingService service.
// All implementations must embed UnimplementedMarketingServiceServer
// for forward compatibility.
type MarketingServiceServer interface {
	CreateAdsBiz(context.Context, *MarketingRequest) (*MarketingResponse, error)
	mustEmbedUnimplementedMarketingServiceServer()
}

// UnimplementedMarketingServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedMarketingServiceServer struct{}

func (UnimplementedMarketingServiceServer) CreateAdsBiz(context.Context, *MarketingRequest) (*MarketingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateAdsBiz not implemented")
}
func (UnimplementedMarketingServiceServer) mustEmbedUnimplementedMarketingServiceServer() {}
func (UnimplementedMarketingServiceServer) testEmbeddedByValue()                          {}

// UnsafeMarketingServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MarketingServiceServer will
// result in compilation errors.
type UnsafeMarketingServiceServer interface {
	mustEmbedUnimplementedMarketingServiceServer()
}

func RegisterMarketingServiceServer(s grpc.ServiceRegistrar, srv MarketingServiceServer) {
	// If the following call pancis, it indicates UnimplementedMarketingServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&MarketingService_ServiceDesc, srv)
}

func _MarketingService_CreateAdsBiz_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MarketingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketingServiceServer).CreateAdsBiz(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MarketingService_CreateAdsBiz_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketingServiceServer).CreateAdsBiz(ctx, req.(*MarketingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// MarketingService_ServiceDesc is the grpc.ServiceDesc for MarketingService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var MarketingService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "exmsg.services.MarketingService",
	HandlerType: (*MarketingServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateAdsBiz",
			Handler:    _MarketingService_CreateAdsBiz_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "services/marketing.proto",
}
