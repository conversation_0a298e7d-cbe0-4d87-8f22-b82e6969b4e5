syntax = "proto3";

package exmsg.services;

option go_package = "gitlab.com/a7923/athena-go/proto/exmsg/services;services";

import "models/lead.proto";
import "models/order.proto";
import "models/common.proto";

service AGSaleService {
  rpc CreateLead(LeadRequest) returns (LeadResponse);
  rpc GetLeads(LeadRequest) returns (LeadResponse);
  rpc GetOrders(OrderRequest) returns (OrderResponse);
  rpc CreateOrders(OrderRequest) returns (OrderResponse);
  rpc UpdateLead(LeadRequest) returns (LeadResponse);
  rpc CreateLeadCareAndItem(LeadRequest) returns (LeadResponse);
  rpc GetCareReasons(CareReasonRequest) returns (CareReasonResponse);
  rpc GetOrderProducts(OrderRequest) returns (OrderResponse);
  rpc GetLandingPages(LandingPageRequest) returns (LandingPageResponse);
  rpc GetOrderDisplayId(OrderDisplayIdRequest) returns (OrderResponse);
  rpc UpdateOrder(OrderRequest) returns (OrderResponse);
  rpc RemoveDuplicateOrder(OrderRequest) returns (OrderResponse);
  rpc RemoveDuplicateLead(LeadRequest) returns (LeadResponse);
}

message OrderDisplayIdRequest {
  int64 project_id = 1;
}

message CareReasonRequest {
  int64 id = 1;
  string reason_key = 2;
  int64 company_id = 3;
  int64 state = 5;
  int64 country_id = 6;
  exmsg.models.Query query = 7;
}

message CareReasonResponse {
  repeated exmsg.models.CareReason care_reasons = 1;
  exmsg.models.SQLResult exec_result = 2;
}

message LeadRequest {
  exmsg.models.Query query = 1;

  // for insert
  exmsg.models.Lead lead = 2;
  exmsg.models.Order order = 3;
  bool using_transaction = 4;

  // filter
  int64 id = 5;
  int64 order_id = 6;
  repeated string update_fields = 7;
  exmsg.models.LeadCare lead_care = 8;
  exmsg.models.LeadCareItem lead_care_item = 9;
  int64 lead_type = 10;
  repeated int64 not_in_status = 11;
}

message OrderRequest {
  exmsg.models.Query query = 1;
  int64 id = 2;
  repeated exmsg.models.Order insert_orders = 3; // for insert
  repeated int64 ids = 4;
  int64 order_id = 5;
  int64 source_id = 6;
  repeated string update_fields = 7;
  exmsg.models.Order order = 8;
  int64 not_equal_status= 9;
  string display_id = 10;
  int64 company_id = 11;
}

message OrderDisplayIdCount {
  string project_id = 1;
  string date = 2;
  string count = 3;
}

message OrderResponse {
  repeated exmsg.models.Order orders = 1;
  exmsg.models.SQLResult exec_result = 2;
  repeated exmsg.models.OrderProduct order_products = 5;
  OrderDisplayIdCount order_display_id_count = 6;
}

message LeadResponse {
  repeated exmsg.models.Lead leads = 2;
  repeated exmsg.models.Order order = 3;
  exmsg.models.SQLResult exec_result = 4;
}

message LandingPageRequest {
  exmsg.models.Query query = 1;
  string id = 2;
  repeated string ids = 3;
  repeated LandingPageCompositeKey lp_composite_keys = 4;
  bool get_all = 5;
  optional string search = 6;
  bool is_admin = 7;
  bool is_search_with_composite_key = 8;
}

message LandingPageCompositeKey {
  int32 country_id = 1;
  int32 project_id = 2;
}

message LandingPageResponse {
  repeated exmsg.models.LandingPage landing_pages = 1;
  exmsg.models.SQLResult exec_result = 2;
}