package reports

import (
	"github.com/Masterminds/squirrel"
	"gitlab.com/a7923/athena-go/pkg/utils"
	"gitlab.com/a7923/athena-go/proto/exmsg/models"
	"gitlab.com/a7923/athena-go/proto/exmsg/services"
)

func BuildClause(qb squirrel.SelectBuilder, req *services.AnalyticsRequest) squirrel.SelectBuilder {
	// require fields
	for _, condition := range req.Conditions {
		if len(condition.Expr) > 0 {
			args := utils.GetValueFromArgs(condition.Args)
			qb = qb.Where(condition.Expr, args)
			continue
		}

		if len(condition.Field) == 0 {
			continue
		}

		var pred interface{}
		args := utils.GetValueFromArgs(condition.Args)
		switch condition.Operator {
		case models.Operator_EQ:
			pred = squirrel.Eq{condition.Field: args}
		case models.Operator_NEQ:
			pred = squirrel.NotEq{condition.Field: args}
		case models.Operator_GT:
			pred = squirrel.Gt{condition.Field: args}
		case models.Operator_GTE:
			pred = squirrel.GtOrEq{condition.Field: args}
		case models.Operator_LT:
			pred = squirrel.Lt{condition.Field: args}
		case models.Operator_LTE:
			pred = squirrel.LtOrEq{condition.Field: args}
		case models.Operator_IN:
			pred = squirrel.Eq{condition.Field: args}
		case models.Operator_NOT_IN:
			pred = squirrel.NotEq{condition.Field: args}
		case models.Operator_LIKE:
			pred = squirrel.Like{condition.Field: args}
		case models.Operator_NOT_LIKE:
			pred = squirrel.NotLike{condition.Field: args}
		case models.Operator_IS_NULL:
			pred = squirrel.Eq{condition.Field: nil}
		case models.Operator_IS_NOT_NULL:
			pred = squirrel.NotEq{condition.Field: nil}
		case models.Operator_BETWEEN:
			//if len(args) >= 2 {
			//	pred = squirrel.And{
			//		squirrel.GtOrEq{condition.Field: args[0]},
			//		squirrel.LtOrEq{condition.Field: args[1]},
			//	}
			//}
		}
		qb = qb.Where(pred)
	}

	if req.Offset > 0 {
		qb = qb.Offset(uint64(req.Offset))
	}

	if req.Limit > 0 {
		qb = qb.Limit(uint64(req.Limit))
	}

	if len(req.GroupBy) > 0 {
		qb = qb.GroupBy(req.GroupBy...)
	}

	if len(req.OrderBy) > 0 {
		qb = qb.OrderBy(req.OrderBy...)
	}

	return qb
}
