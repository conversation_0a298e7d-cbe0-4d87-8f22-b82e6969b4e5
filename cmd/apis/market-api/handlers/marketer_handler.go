package handlers

import (
	"fmt"
	"net/http"

	"gitlab.com/a7923/athena-go/internal/token"

	"gitlab.com/a7923/athena-go/pkg/common"
	"gitlab.com/a7923/athena-go/pkg/dbtool"
	"gitlab.com/a7923/athena-go/pkg/logger"
	transhttp "gitlab.com/a7923/athena-go/pkg/transport"
	"gitlab.com/a7923/athena-go/proto/exmsg/models"
	"gitlab.com/a7923/athena-go/proto/exmsg/services"
)

const (
	defaultMarketerSortField = "name"
	defaultMarketerSortOrder = "asc"
)

var (
	mapMarketerSortKeyToDBField = map[string]string{
		"id":   "id",
		"name": "name",
	}
	allowedMarketerSortFields = []string{"id", "name"}
)

type ListMarketersHandler struct {
	UserService services.UserService
}

type ListMarketersResponse struct {
	Marketers []*models.User `json:"data"`
}

func (h *ListMarketersHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	// Retrieve companyID from request header
	var companyID int64
	if err := common.ExtractHeader(r, "X-Gw-Companyid", &companyID); err != nil {
		transhttp.RespondError(w, http.StatusBadRequest, fmt.Sprintf("failed to extract companyID from request header: %v", err))
		return
	}
	if companyID <= 0 {
		transhttp.RespondError(w, http.StatusBadRequest, "Company ID must greater than 0")
		return
	}

	// Extract Sorting Parameters
	sortByParam, err := common.ExtractSortByParam(r, defaultMarketerSortField, defaultMarketerSortOrder, allowedMarketerSortFields)
	if err != nil {
		transhttp.RespondError(w, http.StatusBadRequest, fmt.Sprintf("Invalid sort by param: %v", err))
		return
	}

	listReq := &services.GetUsersByOptionsRequest{
		CompanyId: companyID,
		SortField: mapMarketerSortKeyToDBField[sortByParam.Field],
		IsAsc:     sortByParam.Order == "asc",
	}

	mids, err := token.GetMarketerIds(r, h.UserService, listReq.Ids)
	if err != nil {
		logger.AthenaLogger.Errorw("failed to get marketer ids", "error", err)
		errMsg := fmt.Sprintf("failed to get marketer ids: %v", err)
		transhttp.RespondError(w, http.StatusInternalServerError, errMsg)
		return
	}
	listReq.Ids = mids
	// Send the request to the User GRPC service
	listResp, err := h.UserService.GetUsersByOptions(r.Context(), listReq, dbtool.WithFieldSelect("id", "name"))
	if err != nil {
		logger.AthenaLogger.Errorw("failed to get users by options", "error", err)
		errMsg := fmt.Sprintf("failed to get users by options: %v", err)
		transhttp.RespondError(w, http.StatusInternalServerError, errMsg)
		return
	}
	if listResp.Users == nil {
		listResp.Users = make([]*models.User, 0)
	}
	response := ListMarketersResponse{
		Marketers: listResp.Users,
	}
	transhttp.RespondJSON(w, http.StatusOK, response)
}
