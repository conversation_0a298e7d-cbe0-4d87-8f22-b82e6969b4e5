package tables

import (
	"gitlab.com/a7923/athena-go/pkg/dbtool"
	"time"
)

func GetLeadTable() *dbtool.Table {
	return &dbtool.Table{
		Name:          "leads",
		AIColumns:     []string{"id"},
		ColumnMapper:  map[string]string{},
		IgnoreColumns: []string{},
		DefaultAlias:  "l",
		NotNullColumns: map[string]interface{}{
			// Timestamp fields with NOW() default
			"created_at": func() interface{} { return time.Now() },
			"updated_at": func() interface{} { return time.Now() },

			// Integer fields
			"id":       nil,      // Skip as it's handled by sequence
			"order_id": 0,        // NOT NULL but no default
			"state":    int16(0), // smallint default '0'

			// Boolean fields
			"ignore_duplicate_warning": false, // default false
		},
	}
}

func GetLeadCareTable() *dbtool.Table {
	return &dbtool.Table{
		Name:          "lead_cares",
		AIColumns:     []string{"id"},
		ColumnMapper:  map[string]string{},
		IgnoreColumns: []string{},
		DefaultAlias:  "lc",
	}
}

func GetLeadCareItemTable() *dbtool.Table {
	return &dbtool.Table{
		Name:          "lead_care_items",
		AIColumns:     []string{"id"},
		ColumnMapper:  map[string]string{},
		IgnoreColumns: []string{},
		DefaultAlias:  "lc",
	}
}

func GetCareReasonTable() *dbtool.Table {
	return &dbtool.Table{
		Name:          "care_reasons",
		AIColumns:     []string{"id"},
		ColumnMapper:  map[string]string{},
		IgnoreColumns: []string{},
		DefaultAlias:  "lc",
	}
}

func GetLogTable() *dbtool.Table {
	return &dbtool.Table{
		Name:          "logs",
		AIColumns:     []string{"id"},
		ColumnMapper:  map[string]string{},
		IgnoreColumns: []string{},
		DefaultAlias:  "l",
	}
}

func GetPossibleDuplicateOrders() *dbtool.Table {
	return &dbtool.Table{
		Name:          "possible_duplicate_orders",
		AIColumns:     []string{"id"},
		ColumnMapper:  map[string]string{},
		IgnoreColumns: []string{},
		DefaultAlias:  "rdo",
	}
}

func GetRemovedDuplicateOrders() *dbtool.Table {
	return &dbtool.Table{
		Name:          "removed_duplicate_orders",
		AIColumns:     []string{"id"},
		ColumnMapper:  map[string]string{},
		IgnoreColumns: []string{},
		DefaultAlias:  "rdo",
	}
}
func GetDuplicateLeads() *dbtool.Table {
	return &dbtool.Table{
		Name:          "duplicate_leads",
		AIColumns:     []string{"id"},
		ColumnMapper:  map[string]string{},
		IgnoreColumns: []string{},
		DefaultAlias:  "rdo",
	}
}
