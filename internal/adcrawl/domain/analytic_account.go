package domain

import (
	"time"
)

type AnalyticAccount struct {
	ID              int64     `json:"id"`
	Username        string    `json:"username"`
	ProfilePicURL   string    `json:"profile_pic_url"`
	Token           string    `json:"token"`
	MarketerID      int64     `json:"marketer_id"`
	MarketerName    string    `json:"marketer_name"`
	Available       bool      `json:"available"`
	CompanyID       int64     `json:"company_id"`
	CreatedAt       time.Time `json:"created_at"`
	CountAdAccounts int64     `json:"-"`
}

type ListAnalyticAccountsResponse struct {
	AnalyticAccounts []AnalyticAccount
	Total            int64
}

type AnalyticAccountFilter struct {
	MarketerIDs []int64
	Status      *bool
	SearchName  *string
}
