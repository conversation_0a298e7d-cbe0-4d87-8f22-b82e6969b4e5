// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v3.5.1
// source: services/webhook.proto

package services

import (
	context "context"
	models "gitlab.com/a7923/athena-go/proto/exmsg/models"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	WebhookService_InsertLogs_FullMethodName = "/exmsg.services.WebhookService/InsertLogs"
)

// WebhookServiceClient is the client API for WebhookService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type WebhookServiceClient interface {
	InsertLogs(ctx context.Context, in *models.WebhookEvents, opts ...grpc.CallOption) (*models.SQLResult, error)
}

type webhookServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewWebhookServiceClient(cc grpc.ClientConnInterface) WebhookServiceClient {
	return &webhookServiceClient{cc}
}

func (c *webhookServiceClient) InsertLogs(ctx context.Context, in *models.WebhookEvents, opts ...grpc.CallOption) (*models.SQLResult, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(models.SQLResult)
	err := c.cc.Invoke(ctx, WebhookService_InsertLogs_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// WebhookServiceServer is the server API for WebhookService service.
// All implementations must embed UnimplementedWebhookServiceServer
// for forward compatibility.
type WebhookServiceServer interface {
	InsertLogs(context.Context, *models.WebhookEvents) (*models.SQLResult, error)
	mustEmbedUnimplementedWebhookServiceServer()
}

// UnimplementedWebhookServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedWebhookServiceServer struct{}

func (UnimplementedWebhookServiceServer) InsertLogs(context.Context, *models.WebhookEvents) (*models.SQLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InsertLogs not implemented")
}
func (UnimplementedWebhookServiceServer) mustEmbedUnimplementedWebhookServiceServer() {}
func (UnimplementedWebhookServiceServer) testEmbeddedByValue()                        {}

// UnsafeWebhookServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to WebhookServiceServer will
// result in compilation errors.
type UnsafeWebhookServiceServer interface {
	mustEmbedUnimplementedWebhookServiceServer()
}

func RegisterWebhookServiceServer(s grpc.ServiceRegistrar, srv WebhookServiceServer) {
	// If the following call pancis, it indicates UnimplementedWebhookServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&WebhookService_ServiceDesc, srv)
}

func _WebhookService_InsertLogs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(models.WebhookEvents)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WebhookServiceServer).InsertLogs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WebhookService_InsertLogs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WebhookServiceServer).InsertLogs(ctx, req.(*models.WebhookEvents))
	}
	return interceptor(ctx, in, info, handler)
}

// WebhookService_ServiceDesc is the grpc.ServiceDesc for WebhookService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var WebhookService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "exmsg.services.WebhookService",
	HandlerType: (*WebhookServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "InsertLogs",
			Handler:    _WebhookService_InsertLogs_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "services/webhook.proto",
}
