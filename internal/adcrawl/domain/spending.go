package domain

import (
	"context"
	"strconv"

	"github.com/spf13/cast"
	"gitlab.com/a7923/athena-go/pkg/common"
)

type Spending struct {
	AnalyticAccountID int64   `json:"analytic_account_id"`
	AdAccountID       int64   `json:"ad_account_id"`
	CampaignID        int64   `json:"campaign_id"`
	AdsetID           int64   `json:"adset_id"`
	AdID              int64   `json:"ad_id"`
	DateStart         string  `json:"date_start"`
	Value             string  `json:"value"`
	RawNumeric        float64 `json:"spend"`
	ExchangeRate      float64 `json:"exchange_rate"`
	CPM               float64 `json:"cpm"`
	CPC               float64 `json:"cpc"`
	CTR               float64 `json:"ctr"`
	Clicks            float64 `json:"clicks"`
	Frequency         float64 `json:"frequency"`
	P25               float64 `json:"p25"`
	P50               float64 `json:"p50"`
	P75               float64 `json:"p75"`
	P95               float64 `json:"p95"`
	VideoAverage      float64 `json:"video_average"`
	Impressions       float64 `json:"impressions"`
	HourlyStat        int     `json:"hourly_stats_aggregated_by_advertiser_time_zone"`
}

// DummySpending is a dummy struct to help unmarshal JSON
type DummySpending struct {
	AccountID    string `json:"account_id"`
	CampaignID   string `json:"campaign_id"`
	AdsetID      string `json:"adset_id"`
	AdID         string `json:"ad_id"`
	DateStart    string `json:"date_start"`
	RawNumeric   string `json:"spend"`
	CPM          string `json:"cpm"`
	CPC          string `json:"cpc"`
	CTR          string `json:"ctr"`
	Clicks       string `json:"clicks"`
	Frequency    string `json:"frequency"`
	P25          string `json:"p25"`
	P50          string `json:"p50"`
	P75          string `json:"p75"`
	P95          string `json:"p95"`
	VideoAverage string `json:"video_average"`
	Impressions  string `json:"impressions"`
	HourlyStat   string `json:"hourly_stats_aggregated_by_advertiser_time_zone"`
}

func ConvertDummySpendingToSpending(ds DummySpending) *Spending {
	s := &Spending{
		AdAccountID: cast.ToInt64(ds.AccountID),
		AdID:        cast.ToInt64(ds.AdID),
		CampaignID:  cast.ToInt64(ds.CampaignID),
		AdsetID:     cast.ToInt64(ds.AdsetID),
		DateStart:   ds.DateStart,
	}

	if ds.RawNumeric != "" {
		s.RawNumeric, _ = strconv.ParseFloat(ds.RawNumeric, 64)
	}
	if ds.CPM != "" {
		s.CPM, _ = strconv.ParseFloat(ds.CPM, 64)
	}
	if ds.CPC != "" {
		s.CPC, _ = strconv.ParseFloat(ds.CPC, 64)
	}
	if ds.CTR != "" {
		s.CTR, _ = strconv.ParseFloat(ds.CTR, 64)
	}
	if ds.Clicks != "" {
		s.Clicks, _ = strconv.ParseFloat(ds.Clicks, 64)
	}
	if ds.Frequency != "" {
		s.Frequency, _ = strconv.ParseFloat(ds.Frequency, 64)
	}
	if ds.Impressions != "" {
		s.Impressions, _ = strconv.ParseFloat(ds.Impressions, 64)
	}

	if ds.P25 != "" {
		s.P25 = cast.ToFloat64(ds.P25)
	}
	if ds.P50 != "" {
		s.P50 = cast.ToFloat64(ds.P50)
	}
	if ds.P75 != "" {
		s.P75 = cast.ToFloat64(ds.P75)
	}
	if ds.P95 != "" {
		s.P95 = cast.ToFloat64(ds.P95)
	}
	if ds.VideoAverage != "" {
		s.VideoAverage = cast.ToFloat64(ds.VideoAverage)
	}

	hour, err := common.RetrieveHour(ds.HourlyStat)
	if err != nil {
		return nil
	} else {
		s.HourlyStat = hour
	}

	return s
}

type SpendingRepository interface {
	BatchInsert(ctx context.Context, sps []Spending) error
	UpdateMarketerID(ctx context.Context) error
}
