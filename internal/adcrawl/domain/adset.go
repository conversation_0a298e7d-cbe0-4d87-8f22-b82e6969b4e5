package domain

import (
	"context"

	"github.com/spf13/cast"
	"gitlab.com/a7923/athena-go/pkg/common"
)

type AdSet struct {
	ID               int64  `json:"id"`
	Name             string `json:"name"`
	Status           string `json:"status"`
	EffectiveStatus  string `json:"effective_status"`
	OptimizationGoal string `json:"optimization_goal"`
	AdAccountID      int64  `json:"account_id"`
	DestinationType  string `json:"destination_type"`
	CampaignID       int64  `json:"campaign_id"`
	MarketName       string `json:"market_name"`
	MarketID         int    `json:"market_id"`
}

type AdSetRepository interface {
	BatchInsert(ctx context.Context, adsets []AdSet, scheduled bool) error
}

type DummyAdSet struct {
	ID               string    `json:"id"`
	Name             string    `json:"name"`
	Status           string    `json:"status"`
	EffectiveStatus  string    `json:"effective_status"`
	OptimizationGoal string    `json:"optimization_goal"`
	AdAccountID      string    `json:"account_id"`
	DestinationType  string    `json:"destination_type"`
	CampaignID       string    `json:"campaign_id"`
	Targeting        Targeting `json:"targeting"`
}

type Targeting struct {
	GeoLocations GeoLocations `json:"geo_locations"`
}

type GeoLocations struct {
	Countries []string `json:"countries"`
}

func (a DummyAdSet) ToAdSet() AdSet {
	adset := AdSet{
		ID:               cast.ToInt64(a.ID),
		Name:             a.Name,
		Status:           a.Status,
		EffectiveStatus:  a.EffectiveStatus,
		OptimizationGoal: a.OptimizationGoal,
		AdAccountID:      cast.ToInt64(a.AdAccountID),
		DestinationType:  a.DestinationType,
		CampaignID:       cast.ToInt64(a.CampaignID),
	}
	if len(a.Targeting.GeoLocations.Countries) > 0 {
		adset.MarketName = a.Targeting.GeoLocations.Countries[0]
		adset.MarketID = common.BusinessCountryCodeToDialingCode(adset.MarketName)
	}
	return adset
}
