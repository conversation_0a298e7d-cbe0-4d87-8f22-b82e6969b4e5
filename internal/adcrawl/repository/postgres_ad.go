package repository

import (
	"context"

	"github.com/Masterminds/squirrel"
	"github.com/jmoiron/sqlx"
	"gitlab.com/a7923/athena-go/internal/adcrawl/domain"
	"gitlab.com/a7923/athena-go/pkg/logger"
)

var _ domain.AdRepository = (*PostgresAdRepository)(nil)

type PostgresAdRepository struct {
	db           *sqlx.DB
	queryBuilder squirrel.StatementBuilderType
}

func NewPostgresAdRepository(db *sqlx.DB) *PostgresAdRepository {
	return &PostgresAdRepository{
		db:           db,
		queryBuilder: squirrel.StatementBuilder.PlaceholderFormat(squirrel.Dollar),
	}
}

const (
	adsTable = "fb.ads"
)

var (
	insertedAdColumns = []string{
		"id",
		"name",
		"status",
		"effective_status",
		"adset_id",
		"page_id",
		"object_story_id",
		"object_story_spec",
		"image_url",
		"post_id",
	}
)

func (par *PostgresAdRepository) BatchInsert(ctx context.Context, ads []domain.Ad) error {
	qb := par.queryBuilder.Insert(adsTable).Columns(insertedAdColumns...).
		SuffixExpr(squirrel.Expr(`
			ON CONFLICT (id) DO UPDATE SET
				name = EXCLUDED.name,
				status = EXCLUDED.status,
				effective_status = EXCLUDED.effective_status,
				adset_id = EXCLUDED.adset_id,
				page_id = EXCLUDED.page_id,
				object_story_id = EXCLUDED.object_story_id,
				object_story_spec = EXCLUDED.object_story_spec,
				image_url = EXCLUDED.image_url,
				updated_at = NOW(),
				post_id = EXCLUDED.post_id,
				deleted_at = NULL
		`))

	for _, ad := range ads {
		if ad.ObjectStorySpec == nil {
			ad.ObjectStorySpec = []byte("{}")
		}
		qb = qb.Values(
			ad.ID,
			ad.Name,
			ad.Status,
			ad.EffectiveStatus,
			ad.AdSetID,
			ad.PageID,
			ad.ObjectStoryID,
			ad.ObjectStorySpec,
			ad.ImageURL,
			ad.PostID,
		)
	}

	insertQuery, args, err := qb.ToSql()
	if err != nil {
		logger.AthenaLogger.Errorf("failed to build insert query: %v", err)
		return err
	}

	if _, err = par.db.ExecContext(ctx, insertQuery, args...); err != nil {
		logger.AthenaLogger.Errorf("failed to insert ads: %v", err)
		return err
	}

	return nil
}
