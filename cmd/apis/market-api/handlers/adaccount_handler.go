package handlers

import (
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	http2 "gitlab.com/a7923/athena-go/internal/http"
	"gitlab.com/a7923/athena-go/internal/token"

	"github.com/spf13/cast"
	"gitlab.com/a7923/athena-go/pkg/common"
	"gitlab.com/a7923/athena-go/pkg/logger"
	transhttp "gitlab.com/a7923/athena-go/pkg/transport"
	"gitlab.com/a7923/athena-go/proto/exmsg/models"
	"gitlab.com/a7923/athena-go/proto/exmsg/services"
)

type ListAdAccountsHandler struct {
	MarketClient services.MarketService
	UserClient   services.UserService
}

const (
	defaultAdAccountPage      = 1
	defaultAdAccountLimit     = 10
	defaultAdAccountSortField = "updatedAt"
	defaultAdAccountSortOrder = "desc"
)

var (
	mapAdAccountSortFieldToDBField = map[string]string{
		"id":                  "id",
		"adAccountName":       "name",
		"analyticAccountName": "analytic_account_name",
		"timezoneOffset":      "timezone_offset_hours_utc",
		"updatedAt":           "updated_at",
		"status":              "status",
		"marketerID":          "marketer_id",
		"updatedByID":         "updated_by_id",
	}
	allowedAdAccountSortFields = []string{"id", "adAccountName", "analyticAccountName", "timezoneOffset",
		"updatedAt", "status", "marketerID", "updatedByID"}
	allowedAdAccountStatus = []string{"active", "disabled"}
)

type ListAdAccountsResponse struct {
	AdAccountDTOs []AdAccountDTO    `json:"data"`
	Pagination    common.Pagination `json:"pagination"`
}

func (h *ListAdAccountsHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	defer func() {
		logger.AthenaLogger.Debugw("Processed request", "url", r.URL.Path, "took", time.Since(start).Milliseconds())
	}()

	// Extract Pagination Parameters
	paginationParam, err := common.ExtractPaginationQueryParam(r, defaultAdAccountPage, defaultAdAccountLimit)
	if err != nil {
		transhttp.RespondError(w, http.StatusBadRequest, fmt.Sprintf("Invalid pagination param: %v", err))
		return
	}

	// Extract Sorting Parameters
	sortByParam, err := common.ExtractSortByParam(r, defaultAdAccountSortField, defaultAdAccountSortOrder, allowedAdAccountSortFields)
	if err != nil {
		transhttp.RespondError(w, http.StatusBadRequest, fmt.Sprintf("Invalid sort by param: %v", err))
		return
	}

	// Retrieve Company ID
	var companyID int64
	if err := common.ExtractHeader(r, "X-Gw-Companyid", &companyID); err != nil {
		transhttp.RespondError(w, http.StatusBadRequest, fmt.Sprintf("failed to extract companyID from request header: %v", err))
		return
	}
	if companyID <= 0 {
		transhttp.RespondError(w, http.StatusBadRequest, "companyID must be greater than 0")
		return
	}

	svcReq := &services.ListAdAccountsRequest{
		Page:      int32(paginationParam.Page),
		Limit:     int32(paginationParam.Limit),
		CompanyId: companyID,
		SortField: mapAdAccountSortFieldToDBField[sortByParam.Field],
		IsAsc:     sortByParam.Order == "asc",
	}

	queryParams := r.URL.Query()
	// Search by name or ID (Optional)
	searchTerm := queryParams.Get("search")
	if searchTerm != "" {
		svcReq.SearchTerm = &searchTerm
	}

	// Filter by status (Optional)
	status := queryParams.Get("status")
	if status != "" {
		if !common.InSlice(status, allowedAdAccountStatus) {
			transhttp.RespondError(w, http.StatusBadRequest, fmt.Sprintf("Invalid status, supported status: %+v", allowedAdAccountStatus))
			return
		}
	}
	if status != "" {
		active := false
		if status == "active" {
			active = true
		}
		svcReq.IsActive = &active
	}

	// Filter by analytic account ID (allowed multiple IDs) (Optional)
	queryAnalyticAccountIDs, ok := queryParams["analyticAccountID"]
	if ok {
		analyticAccountIDs := make([]int64, len(queryAnalyticAccountIDs))
		for i := range queryAnalyticAccountIDs {
			analyticAccountIDs[i] = cast.ToInt64(queryAnalyticAccountIDs[i])
		}
		svcReq.AnalyticAccountIds = analyticAccountIDs
	}

	// Filter by marketer ID (allowed multiple IDs) (Optional)
	queryMarketerIDs, ok := queryParams["marketerID"]
	if ok {
		marketerIDs := make([]int64, len(queryMarketerIDs))
		for i := range queryMarketerIDs {
			marketerIDs[i] = cast.ToInt64(queryMarketerIDs[i])
		}
		svcReq.MarketerIds = marketerIDs
	}

	mids, err := token.GetMarketerIds(r, h.UserClient, svcReq.MarketerIds)
	if err != nil {
		logger.AthenaLogger.Errorw("Failed to get marketer IDs", "error", err)
		errMsg := fmt.Sprintf("Failed to get marketer IDs: %v", err)
		transhttp.RespondError(w, http.StatusInternalServerError, errMsg)
		return
	}
	svcReq.MarketerIds = mids
	// Send request to Market GRPC service
	resp, err := h.MarketClient.ListAdAccounts(r.Context(), svcReq)
	if err != nil {
		logger.AthenaLogger.Errorw("Failed to list ad accounts", "error", err)
		errMsg := fmt.Sprintf("Failed to list ad accounts: %v", err)
		transhttp.RespondError(w, http.StatusInternalServerError, errMsg)
		return
	}

	// Prepare response
	if resp.Adaccounts == nil {
		resp.Adaccounts = make([]*models.AdAccount, 0)
	}
	adAccountDTOs := make([]AdAccountDTO, len(resp.Adaccounts))
	for i, adAccount := range resp.Adaccounts {
		adAccountDTOs[i] = ToAdAccountDTO(adAccount)
	}
	listResp := &ListAdAccountsResponse{
		AdAccountDTOs: adAccountDTOs,
		Pagination: common.Pagination{
			Page:  paginationParam.Page,
			Limit: paginationParam.Limit,
			Total: resp.Total,
		},
	}
	transhttp.RespondJSON(w, http.StatusOK, listResp)
}

type UpdateAdAccountsHandler struct {
	MarketClient services.MarketService
}

type UpdateAdAccountsRequest struct {
	AdAccountIDs []string `json:"ad_account_ids"`
	MarketerID   string   `json:"marketer_id"`
}

type UpdateAdAccountsResponse struct {
	UpdatedByID string `json:"updated_by_id"`
	*services.BulkUpdateAdAccountsResponse
}

func (h *UpdateAdAccountsHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	defer func() {
		logger.AthenaLogger.Debugw("Processed request", "url", r.URL.Path, "took", time.Since(start).Milliseconds())
	}()

	req := &UpdateAdAccountsRequest{}
	if err := json.NewDecoder(r.Body).Decode(req); err != nil {
		transhttp.RespondError(w, http.StatusBadRequest, "Invalid request body")
		return
	}
	if len(req.AdAccountIDs) == 0 {
		transhttp.RespondError(w, http.StatusBadRequest, "Ad account IDs are required")
		return
	}

	if req.MarketerID == "" {
		transhttp.RespondError(w, http.StatusBadRequest, "Marketer ID is required")
		return
	}
	marketerID, err := cast.ToInt64E(req.MarketerID)
	if err != nil {
		transhttp.RespondError(w, http.StatusBadRequest, fmt.Sprintf("Invalid Marketer ID: %v", err))
		return
	}
	if marketerID <= 0 {
		transhttp.RespondError(w, http.StatusBadRequest, "Marketer ID must be greater than 0")
		return
	}

	var companyID int64
	if err := common.ExtractHeader(r, "X-Gw-Companyid", &companyID); err != nil {
		transhttp.RespondError(w, http.StatusBadRequest, fmt.Sprintf("failed to extract companyID from request header: %v", err))
		return
	}
	if companyID <= 0 {
		transhttp.RespondError(w, http.StatusBadRequest, "companyID must be greater than 0")
		return
	}

	var updatedByID int64
	if err := common.ExtractHeader(r, "X-Gw-Sub", &updatedByID); err != nil {
		transhttp.RespondError(w, http.StatusBadRequest, fmt.Sprintf("failed to extract updatedByID from request header: %v", err))
		return
	}
	if updatedByID <= 0 {
		transhttp.RespondError(w, http.StatusBadRequest, "updatedByID must be greater than 0")
		return
	}

	adAccountIds := make([]int64, len(req.AdAccountIDs))
	for i, id := range req.AdAccountIDs {
		adAccountIds[i] = cast.ToInt64(id)
	}
	resp, err := h.MarketClient.BulkUpdateAdAccounts(r.Context(), &services.BulkUpdateAdAccountsRequest{
		CompanyId:    companyID,
		AdaccountIds: adAccountIds,
		MarketerId:   marketerID,
		UpdatedById:  updatedByID,
	})
	if err != nil {
		logger.AthenaLogger.Errorw("Failed to update ad accounts", "error", err, "adAccountIds", adAccountIds,
			"marketerID", marketerID, "updatedByID", updatedByID)
		errMsg := fmt.Sprintf("Failed to update ad accounts: %v", err)
		transhttp.RespondError(w, http.StatusInternalServerError, errMsg)
		return
	}
	logger.AthenaLogger.Infow("Ad accounts updated", "adAccountIds", adAccountIds, "marketerID", marketerID, "companyID", companyID,
		"updatedByID", updatedByID)
	transhttp.RespondJSON(w, http.StatusOK, UpdateAdAccountsResponse{
		UpdatedByID:                  cast.ToString(updatedByID),
		BulkUpdateAdAccountsResponse: resp,
	})
}

type DeleteAdAccountsHandler struct {
	MarketClient services.MarketService
}

type DeleteAdAccountsRequest struct {
	IDs []string `json:"ids"`
}

func (h *DeleteAdAccountsHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	defer func() {
		logger.AthenaLogger.Debugw("Processed request", "url", r.URL.Path, "took", time.Since(start).Milliseconds())
	}()

	req := &DeleteAdAccountsRequest{}
	if err := json.NewDecoder(r.Body).Decode(req); err != nil {
		transhttp.RespondError(w, http.StatusBadRequest, "Invalid request body")
		return
	}
	if len(req.IDs) == 0 {
		transhttp.RespondError(w, http.StatusBadRequest, "Adaccount IDs are required")
		return
	}
	adAccountIds := make([]int64, len(req.IDs))
	for i, id := range req.IDs {
		adAccountIds[i] = cast.ToInt64(id)
	}

	// extract companyID from request header
	var companyID int64
	if err := common.ExtractHeader(r, "X-Gw-Companyid", &companyID); err != nil {
		transhttp.RespondError(w, http.StatusBadRequest, fmt.Sprintf("failed to extract companyID from request header: %v", err))
		return
	}
	if companyID <= 0 {
		transhttp.RespondError(w, http.StatusBadRequest, "companyID must be greater than 0")
		return
	}

	_, err := h.MarketClient.BulkDeleteAdAccounts(r.Context(), &services.BulkDeleteAdAccountsRequest{
		CompanyId: companyID,
		Ids:       adAccountIds,
	})
	if err != nil {
		logger.AthenaLogger.Errorw("Failed to delete ad accounts", "error", err, "adAccountIds", adAccountIds)
		errMsg := fmt.Sprintf("Failed to delete ad accounts: %v", err)
		transhttp.RespondError(w, http.StatusInternalServerError, errMsg)
		return
	}
	logger.AthenaLogger.Infow("Ad accounts deleted", "adAccountIds", adAccountIds, "companyID", companyID,
		"deletedByID", http2.GetUserIDFromRequest(r))
	transhttp.RespondJSON(w, http.StatusOK, map[string]interface{}{
		"success": true,
	})
}

type AdAccountDTO struct {
	ID                string `json:"id"`
	AnalyticAccountID string `json:"analytic_account_id"`
	MarketerID        string `json:"marketer_id"`
	UpdatedByID       string `json:"updated_by_id"`
	*models.AdAccount
}

func ToAdAccountDTO(adAccount *models.AdAccount) AdAccountDTO {
	return AdAccountDTO{
		ID:                cast.ToString(adAccount.Id),
		AnalyticAccountID: cast.ToString(adAccount.AnalyticAccountId),
		MarketerID:        cast.ToString(adAccount.MarketerId),
		UpdatedByID:       cast.ToString(adAccount.UpdatedById),
		AdAccount:         adAccount,
	}
}
