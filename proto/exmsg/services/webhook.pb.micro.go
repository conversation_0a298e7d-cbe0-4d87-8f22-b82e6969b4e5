// Code generated by protoc-gen-micro. DO NOT EDIT.
// source: services/webhook.proto

package services

import (
	fmt "fmt"
	models "gitlab.com/a7923/athena-go/proto/exmsg/models"
	proto "google.golang.org/protobuf/proto"
	math "math"
)

import (
	context "context"
	client "go-micro.dev/v5/client"
	server "go-micro.dev/v5/server"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ client.Option
var _ server.Option

// Client API for WebhookService service

type WebhookService interface {
	InsertLogs(ctx context.Context, in *models.WebhookEvents, opts ...client.CallOption) (*models.SQLResult, error)
}

type webhookService struct {
	c    client.Client
	name string
}

func NewWebhookService(name string, c client.Client) WebhookService {
	return &webhookService{
		c:    c,
		name: name,
	}
}

func (c *webhookService) InsertLogs(ctx context.Context, in *models.WebhookEvents, opts ...client.CallOption) (*models.SQLResult, error) {
	req := c.c.NewRequest(c.name, "WebhookService.InsertLogs", in)
	out := new(models.SQLResult)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for WebhookService service

type WebhookServiceHandler interface {
	InsertLogs(context.Context, *models.WebhookEvents, *models.SQLResult) error
}

func RegisterWebhookServiceHandler(s server.Server, hdlr WebhookServiceHandler, opts ...server.HandlerOption) error {
	type webhookService interface {
		InsertLogs(ctx context.Context, in *models.WebhookEvents, out *models.SQLResult) error
	}
	type WebhookService struct {
		webhookService
	}
	h := &webhookServiceHandler{hdlr}
	return s.Handle(s.NewHandler(&WebhookService{h}, opts...))
}

type webhookServiceHandler struct {
	WebhookServiceHandler
}

func (h *webhookServiceHandler) InsertLogs(ctx context.Context, in *models.WebhookEvents, out *models.SQLResult) error {
	return h.WebhookServiceHandler.InsertLogs(ctx, in, out)
}
