package domain

import (
	"context"
	"time"

	"github.com/spf13/cast"
	"gitlab.com/a7923/athena-go/pkg/common"
)

type AdAccount struct {
	ID                     int64     `json:"account_id"`
	CompanyID              int64     `json:"company_id"`
	Name                   string    `json:"name"`
	Status                 int8      `json:"account_status"`
	Currency               string    `json:"currency"`
	TimezoneOffsetHoursUTC float32   `json:"timezone_offset_hours_utc"`
	CountryID              int       `json:"country_id"`
	AnalyticAccountID      int64     `json:"analytic_account_id"`
	AnalyticAccountName    string    `json:"-"`
	UpdatedAt              time.Time `json:"-"`
	UpdatedByID            int64     `json:"updated_by_id"`
	MarketerID             int64     `json:"marketer_id"`
}

type AdAccountRepository interface {
	BatchInsert(ctx context.Context, accounts []AdAccount, scheduled bool) error
	GetAllAdAccountIDs(ctx context.Context, analyticAccountID int64) ([]int64, error)
}

type AdAccountFilter struct {
	AnalyticAccountIDs []int64
	MarketerIDs        []int64
	Status             *bool
	SearchName         *string
}

type DummyAdAccount struct {
	ID                     string  `json:"account_id"`
	Name                   string  `json:"name"`
	Status                 int8    `json:"account_status"`
	Currency               string  `json:"currency"`
	TimezoneOffsetHoursUTC float32 `json:"timezone_offset_hours_utc"`
	BusinessCountryCode    string  `json:"business_country_code"`
	AnalyticAccountID      int64   `json:"analytic_account_id"`
}

func (a DummyAdAccount) ToAdAccount() AdAccount {
	if a.BusinessCountryCode == "" {
		a.BusinessCountryCode = "VN"
	}
	return AdAccount{
		ID:                     cast.ToInt64(a.ID),
		Name:                   a.Name,
		Status:                 a.Status,
		Currency:               a.Currency,
		TimezoneOffsetHoursUTC: a.TimezoneOffsetHoursUTC,
		CountryID:              common.BusinessCountryCodeToDialingCode(a.BusinessCountryCode),
		AnalyticAccountID:      a.AnalyticAccountID,
	}
}

type ListAdAccountsResponse struct {
	AdAccounts []AdAccount `json:"ad_accounts"`
	TotalCount int64       `json:"total_count"`
}

func AdAccountStatusIntToStr(status int8) string {
	switch status {
	case 1:
		return "active"
	case 2:
		return "disabled"
	default:
		return "disabled"
	}
}

func AdAccountStatusStrToInt(status string) int8 {
	switch status {
	case "active":
		return 1
	case "disabled":
		return 2
	default:
		return 2
	}
}
