package main

import (
	"gitlab.com/a7923/athena-go/pkg/common"

	"gitlab.com/a7923/athena-go/cmd/services/market-service/handlers"
	"gitlab.com/a7923/athena-go/internal/adcrawl/domain"
	"gitlab.com/a7923/athena-go/internal/adcrawl/repository"
	"gitlab.com/a7923/athena-go/pkg/app"
	"gitlab.com/a7923/athena-go/pkg/dbtool"
	"gitlab.com/a7923/athena-go/pkg/logger"
	"gitlab.com/a7923/athena-go/proto/exmsg/services"
)

func main() {
	svc := app.NewGRPCService(common.ServiceNameMarket)

	connMng, err := dbtool.NewConnectionManager(dbtool.DBTypePostgreSQL, nil)
	if err != nil {
		logger.AthenaLogger.Fatalf("could not connect to db: %v", err)
	}
	db := connMng.GetConnection()
	var (
		adAccRepo    domain.AdAccountRepository = repository.NewPostgresAdAccountRepository(db)
		camRepo      domain.CampaignRepository  = repository.NewPostgresCampaignRepository(db)
		marketerRepo domain.MarketerRepository  = repository.NewPostgresMarketerRepository(db)
	)

	psql, err := dbtool.NewConnectionManager(dbtool.DBTypePostgreSQL, nil)
	if err != nil {
		logger.AthenaLogger.Fatal("Failed to connect to database: ", err)
	}
	defer psql.Close()

	grpcSvc := handlers.NewMarketHandler(adAccRepo, camRepo, marketerRepo, psql)
	err = services.RegisterMarketServiceHandler(svc.Server(), grpcSvc)
	if err != nil {
		logger.AthenaLogger.Fatal(err)
	}

	err = svc.Run()
	if err != nil {
		logger.AthenaLogger.Fatal(err)
	}
}
