package reports

const (
	TeamInChargeTelesale = 1
	TeamInChargeCarePage = 2

	//"date" | "project" | "product" | "marketer" | "adaccount" | "campaign" | "adset"
	DimensionAll       = "group_by_all"
	DimensionDate      = "date"
	DimensionProject   = "project"
	DimensionProduct   = "product"
	DimensionMarketer  = "marketer"
	DimensionAdaccount = "adaccount"
	DimensionCampaign  = "campaign"
	DimensionAdset     = "adset"
	NewTime            = "new_time"
	ConfirmationTime   = "confirmation_time"
	HandoverTime       = "handover_time"
	CreatedTime        = "created_time"
	ProcessingTime     = "processing_time"
)

const rateCTE = "(SELECT rate FROM marketing.currencies WHERE company_id = %v and country_id = %v) AS rate"

var ReportType2Dimension = map[string]string{
	"products-revenue":    DimensionProduct,
	"projects-revenue":    DimensionProject,
	"marketer-chart-data": DimensionMarketer,
	"insights":            DimensionAll,
	"table-by-date":       DimensionDate,
	"table-by-product":    DimensionProduct,
	"table-by-marketer":   DimensionMarketer,
	"table-by-project":    DimensionProject,
	"table-by-adaccount":  DimensionAdaccount,
	"table-by-campaign":   DimensionCampaign,
	"table-by-adset":      DimensionAdset,
}

var rev_columns = []string{
	"lead_count",
	"return_count",
	"new_orders",
	"old_orders",
	"total_orders",
	"handled_lead",
	"sale",
	"revenue",
}

var groupColumns = map[string][]string{
	DimensionDate:      {"formatDateTime(toDate(ts, 'Asia/Ho_Chi_Minh'), '%Y-%m-%d') AS date"},
	DimensionProduct:   {"product_id"},
	DimensionMarketer:  {"marketer_id"},
	DimensionProject:   {"project_id"},
	DimensionAdaccount: {"adaccount_id"},
	DimensionCampaign:  {"campaign_id"},
	DimensionAdset:     {"adset_id"},
	DimensionAll:       {"group_by_all"},
}

var spendingColumns = []string{
	"sum(clicks) AS click",
	"avg(cpm) AS cpm",
	"avg(ctr) AS ctr",
	"avg(cpc) AS cpc",
	"avg(impressions) AS impression",
	"sum(spent) AS spent",

	"any(adset) AS adset",
	"any(campaign) AS campaign",
	"any(adaccount) AS adaccount",
}

var revColumns = []string{
	"uniqExact (o.lead_id) AS lead_count",
	"uniqExactIf(o.order_id, o.order_status IN (11, 12, 13, 14)) AS return_count",

	"uniqExactIf(o.order_id, and(o.order_status not in (-2, 17), toDate(o.created_at) = toDate(o.first_update_status_at))) AS new_orders",
	"uniqExactIf(o.order_id, and(o.order_status not in (-2, 17), toDate(o.created_at) != toDate(o.first_update_status_at))) AS old_orders",

	"uniqExactIf(o.order_id, o.order_status NOT IN (-2, 12, 13, 14, 17)) AS total_orders",
	"uniqExactIf(o.lead_id, o.last_cared_at is not NULL) AS handled_lead",

	"SUM(o.sale) * rate as sale",
	"SUM(o.revenue) * rate as revenue",
}

var primaryColumns = []string{
	"o.company_id AS company_id",
	"o.country_id AS country_id",
	"o.team_in_charge AS team_in_charge",
	"o.project_id AS project_id",
	"o.ts AS ts",
	"o.marketer_id AS marketer_id",
}

// ts, company_id, country_id, project_id, product_id, marketer_id, adaccount_id, campaign_id, adset_id, page_id
var messGroupColumns = map[string]string{
	DimensionDate:      "formatDateTime(toDate(ts, 'Asia/Ho_Chi_Minh'), '%Y-%m-%d')",
	DimensionProduct:   "product_id",
	DimensionMarketer:  "marketer_id",
	DimensionProject:   "project_id",
	DimensionAdaccount: "adaccount_id",
	DimensionCampaign:  "campaign_id",
	DimensionAdset:     "adset_id",
	DimensionAll:       "1",
}

// company_id, project_id, marketer_id, product_id, adaccount_id, campaign_id, ad_id, ts, team_in_charge
var groupByColumn = map[string]string{
	DimensionDate:      "date",
	DimensionProduct:   "product_id",
	DimensionMarketer:  "marketer_id",
	DimensionProject:   "project_id",
	DimensionAdaccount: "adaccount_id",
	DimensionCampaign:  "campaign_id",
	DimensionAdset:     "adset_id",
	DimensionAll:       "group_by_all",
}

var TeamInChargeByDashboardType = map[string]int{
	"carepage": TeamInChargeCarePage,
	"telesale": TeamInChargeTelesale,
}

type OrderDashboardGroupBy string

const (
	Status      OrderDashboardGroupBy = "order_status"
	Project     OrderDashboardGroupBy = "project_id"
	NewAt       OrderDashboardGroupBy = "new_at"
	ConfirmedAt OrderDashboardGroupBy = "confirmed_at"
	HandoverAt  OrderDashboardGroupBy = "handover_at"
	ProductId   OrderDashboardGroupBy = "product_id"
	SaleRepId   OrderDashboardGroupBy = "sale_rep_id"
	Carrier     OrderDashboardGroupBy = "carrier_code"
	Overview    OrderDashboardGroupBy = "overview"
	CarrierDate OrderDashboardGroupBy = "carrier_date"
	CreatedAt   OrderDashboardGroupBy = "created_at"
	CareReason  OrderDashboardGroupBy = "last_care_reason_key"
)

type TelesaleDashboardGroupBy string

var TelesaleDashboardField = struct {
	Status      TelesaleDashboardGroupBy
	Project     TelesaleDashboardGroupBy
	NewAt       TelesaleDashboardGroupBy
	ConfirmedAt TelesaleDashboardGroupBy
	HandoverAt  TelesaleDashboardGroupBy
	ProductId   TelesaleDashboardGroupBy
	SaleRepId   TelesaleDashboardGroupBy
	Carrier     TelesaleDashboardGroupBy
	Overview    TelesaleDashboardGroupBy
}{
	Status:      "order_status",
	Project:     "project_id",
	NewAt:       "new_at",
	ConfirmedAt: "confirmed_at",
	HandoverAt:  "handover_at",
	ProductId:   "product_id",
	SaleRepId:   "sale_rep_id",
	Carrier:     "carrier_code",
	Overview:    "overview",
}

type OrderDashboardOptions string

const (
	ProjectOptions  OrderDashboardOptions = "project_id"
	SaleRepOptions  OrderDashboardOptions = "sale_rep_id"
	ProductOptions  OrderDashboardOptions = "product_id"
	MarketerOptions OrderDashboardOptions = "marketer_id"
	SourceOptions   OrderDashboardOptions = "source"
	TagOptions      OrderDashboardOptions = "tag_id"
	CarrierOptions  OrderDashboardOptions = "carrier_code"
)
