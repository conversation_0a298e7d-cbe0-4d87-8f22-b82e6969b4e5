package handlers

import (
	"github.com/ThreeDotsLabs/watermill/message"
	"gitlab.com/a7923/athena-go/pkg/app"
	"gitlab.com/a7923/athena-go/pkg/logger"
	"gitlab.com/a7923/athena-go/proto/exmsg/services"
	"strings"
	"time"
)

type HelloHandler struct {
	UserClient services.UserService
	Publisher  app.PublisherInterface
	Name       string
}

func (h *HelloHandler) GetName() string {
	return h.Name
}

func (h *HelloHandler) Init() error {
	return nil
}

func (h *HelloHandler) HandleMessage(msg *message.Message) error {
	logger.AthenaLogger.Infof("Received message: %s", msg.Payload)
	start := time.Now()
	if strings.Contains(string(msg.Payload), "test") {
		logger.AthenaLogger.Infof("start sleep for 10 seconds...  (workerId: %v)  ", msg.UUID)
		time.Sleep(10 * time.Second)
	}
	logger.AthenaLogger.Infof("done took %v", time.Since(start))
	return nil
}

func (h *HelloHandler) SetPublisher(p app.PublisherInterface) {
	h.Publisher = p
}

func (h *HelloHandler) Close() {
}
