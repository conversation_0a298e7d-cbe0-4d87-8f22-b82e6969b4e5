package handlers

import (
	"context"
	"github.com/golang/protobuf/ptypes/timestamp"
	"github.com/spf13/cast"
	"gitlab.com/a7923/athena-go/internal/enums"
	"gitlab.com/a7923/athena-go/pkg/dbtool"
	"gitlab.com/a7923/athena-go/proto/exmsg/models"
	"gitlab.com/a7923/athena-go/proto/exmsg/services"
	"gitlab.com/a7923/athena-go/tests"
	"log"
	"testing"
	"time"
)

var agSaleHandler *AGSaleHandler
var ctx = context.Background()

func init() {
	err := tests.LoadTestConfig()
	if err != nil {
		log.Fatal("Failed to load config", err)
	}

	psql, err := dbtool.NewConnectionManager(dbtool.DBTypePostgreSQL, nil)
	if err != nil {
		log.Fatal("Failed to connect to database: ", err)
	}

	redis, err := dbtool.CreateRedisConnection(nil)

	agSaleHandler = NewAGSaleHandler(psql, redis)
}

func TestGetLeads(t *testing.T) {
	// Test GetLeads
	resp := services.LeadResponse{}
	err := agSaleHandler.GetLeads(ctx, &services.LeadRequest{
		Id: 10875,
	}, &resp)
	if err != nil {
		t.Fatal("Failed to get leads", err)
	}
	t.Log("Leads: ", resp.Leads)
}

func TestCreateLeads(t *testing.T) {
	// Test GetLeads
	getOrderResponse := services.OrderResponse{}
	createOrderResponse := services.OrderResponse{}

	err := agSaleHandler.GetOrders(ctx, &services.OrderRequest{
		//Id: 90881,
		Ids: []int64{
			90773,
			//90719,
		},
	}, &getOrderResponse)
	if err != nil {
		t.Fatal("Failed to get orders", err)
	}

	for i, o := range getOrderResponse.Orders {
		o.Id = 0
		o.DisplayId = cast.ToString(time.Now().UnixNano())
		o.PartnerCode = cast.ToString(time.Now().UnixNano()) + cast.ToString(i)
		getOrderResponse.Orders[i] = o
	}

	err = agSaleHandler.CreateOrders(ctx, &services.OrderRequest{
		InsertOrders: getOrderResponse.Orders,
	}, &createOrderResponse)
	if err != nil {
		t.Fatal("Failed to create orders", err)
	}

	resp := services.LeadResponse{}
	lead := &models.Lead{
		// Basic fields
		CreatedAt: &timestamp.Timestamp{
			Seconds: time.Now().Unix(),
			Nanos:   int32(time.Now().Nanosecond()),
		},
		UpdatedAt: &timestamp.Timestamp{
			Seconds: time.Now().Unix(),
			Nanos:   int32(time.Now().Nanosecond()),
		},

		// Required fields
		OrderId: createOrderResponse.ExecResult.LastInsertIds[0],
		State:   1,

		// Optional fields
		UserId:        1001,
		CurrentCareId: 0,
		LastUpdatedState: &timestamp.Timestamp{
			Seconds: time.Now().Add(-24 * time.Hour).Unix(), // 1 day ago
			Nanos:   0,
		},
		UpdatedBy:              3003,
		IgnoreDuplicateWarning: false,
		FormCapturedAt: &timestamp.Timestamp{
			Seconds: time.Now().Add(-1 * time.Hour).Unix(), // 1 hour ago
			Nanos:   0,
		},

		// UTM tracking fields
		UtmSource:   "facebook",
		UtmMedium:   "123456",           // ad.id
		UtmCampaign: "summer_sale_2024", // campaign.id
		UtmTerm:     "789012",           // adset.id
		Link:        "https://example.com/product?ref=fb123",

		// Care related fields
		LastCareId:       4004,
		LastCareItemId:   5005,
		LastCareReasonId: 6006,
		LeadType:         1,
	}

	err = agSaleHandler.CreateLead(ctx, &services.LeadRequest{
		Lead: lead,
	}, &resp)

	if err != nil {
		t.Fatal("Failed to get leads", err)
	}
	t.Log("Leads: ", resp.ExecResult)
}

func TestCreateLeadsWithTx(t *testing.T) {

	//orderId := int64(90773)
	orderId := int64(91041)
	getOrderResponse := services.OrderResponse{}
	getLeadResponse := services.LeadResponse{}
	err := agSaleHandler.GetOrders(ctx, &services.OrderRequest{
		//Ids: []int64{
		//	orderId,
		//},
		//Id: 90881,
		Id: orderId,
	}, &getOrderResponse)
	if err != nil {
		t.Fatal("Failed to get orders", err)
	}

	err = agSaleHandler.GetLeads(ctx, &services.LeadRequest{
		OrderId: orderId,
	}, &getLeadResponse)

	for i, o := range getOrderResponse.Orders {
		o.Id = 0
		o.DisplayId = cast.ToString(time.Now().UnixNano())
		o.PartnerCode = cast.ToString(time.Now().UnixNano()) + cast.ToString(i)
		getOrderResponse.Orders[i] = o
	}

	for i, lead := range getLeadResponse.Leads {
		lead.Id = 0
		lead.State = 0
		lead.LeadType = int64(enums.LeadAfterSale)
		lead.LastCareId = 0
		lead.LastCareReasonId = 0
		lead.LastCareItemId = 0
		lead.CurrentCareId = 0
		getLeadResponse.Leads[i] = lead
	}

	err = agSaleHandler.CreateLead(ctx, &services.LeadRequest{
		Order:            getOrderResponse.Orders[0],
		Lead:             getLeadResponse.Leads[0],
		UsingTransaction: true,
	}, &getLeadResponse)
	if err != nil {
		t.Fatal("Failed to create leads", err)
	}
	t.Log("Leads: ", getLeadResponse.ExecResult)
}

func TestAGSaleHandler_CreateLeadCareAndItem(t *testing.T) {
	leadResponse := &services.LeadResponse{}
	orderResponse := &services.OrderResponse{}
	err := agSaleHandler.GetLeads(ctx, &services.LeadRequest{
		Id: 8096,
	}, leadResponse)
	if err != nil {
		t.Fatal("Failed to get leads", err)
	}
	err = agSaleHandler.GetOrders(ctx, &services.OrderRequest{
		Id: leadResponse.Leads[0].OrderId,
	}, orderResponse)

	order := orderResponse.Orders[0]
	updateLead := leadResponse.Leads[0]

	if updateLead.State != int64(enums.CareState_Junk) {
		updateLead.State = int64(enums.CareState_Junk)
		updateFields := []string{"state"}

		exeResult := &services.LeadResponse{}
		err = agSaleHandler.UpdateLead(ctx, &services.LeadRequest{
			Lead:         updateLead,
			UpdateFields: updateFields,
			Id:           updateLead.Id,
		}, exeResult)
		if err != nil {
			t.Fatal("Failed to update lead", err)
		}
	}
	// create care
	leadCare := &models.LeadCare{
		UserId:    updateLead.UserId,
		LeadId:    updateLead.Id,
		ShiftId:   0,
		State:     int64(enums.CareState_Junk),
		UpdatedBy: 1,
	}

	if leadCare.UserId < 1 {
		leadCare.UserId = 1
	}

	reason := &services.CareReasonResponse{}
	err = agSaleHandler.GetCareReasons(ctx, &services.CareReasonRequest{
		CompanyId: order.CompanyId,
		ReasonKey: "wrong_number",
		Query:     &models.Query{Limit: 1},
	}, reason)
	if err != nil {
		t.Fatal("Failed to get care reasons", err)
	}

	careItem := &models.LeadCareItem{
		//LeadCareId:        0,
		ReasonId: reason.CareReasons[0].Id,
		//TimesRepeatReason: 0,
		Note:   reason.CareReasons[0].Name,
		LeadId: updateLead.Id,
	}

	resp := services.LeadResponse{}
	err = agSaleHandler.CreateLeadCareAndItem(ctx, &services.LeadRequest{
		LeadCare:     leadCare,
		LeadCareItem: careItem,
	}, &resp)

	if err != nil {
		t.Fatal("Failed to create lead care and item", err)
	}
	t.Log("Leads: ", resp.ExecResult)
}

func TestAGSaleHandler_GetOrderProducts(t *testing.T) {
	orderResponse := &services.OrderResponse{}
	err := agSaleHandler.GetOrderProducts(ctx, &services.OrderRequest{
		OrderId: 89769,
	}, orderResponse)

	if err != nil {
		t.Fatal("Failed to get order products", err)
	}
	t.Log("Order Products: ", orderResponse.OrderProducts)
}

func TestAGSaleHandler_GetLandingPages(t *testing.T) {
	landingPageResponse := &services.LandingPageResponse{}
	err := agSaleHandler.GetLandingPages(ctx, &services.LandingPageRequest{
		Id: "21d90600-fc68-4d64-8c87-fe5f303eaf1b",
		Query: &models.Query{
			Limit: 10,
		},
	}, landingPageResponse)
	if err != nil {
		t.Fatal("Failed to get landing pages", err)
	}
	t.Log("Landing Pages: ", landingPageResponse.LandingPages)
}
