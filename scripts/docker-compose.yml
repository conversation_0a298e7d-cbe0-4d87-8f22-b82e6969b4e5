version: '3.8'

services:
  postgres:
    image: postgres:17
    container_name: postgres
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: postgres
    ports:
      - "5432:5432"               # PostgreSQL port
    networks:
      - app-network
    volumes:
      - postgres_data:/var/lib/postgresql/data

networks:
  app-network:
    driver: bridge

volumes:
  postgres_data:
