package handlers

import (
	"context"
	"errors"
	"fmt"
	"maps"
	"time"

	"golang.org/x/sync/errgroup"

	"github.com/hibiken/asynq"
	"github.com/jmoiron/sqlx"
	"github.com/spf13/cast"
	"github.com/spf13/viper"
	"gitlab.com/a7923/athena-go/internal/adcrawl"
	"gitlab.com/a7923/athena-go/internal/adcrawl/domain"
	"gitlab.com/a7923/athena-go/pkg/logger"
	"gitlab.com/a7923/athena-go/proto/exmsg/services"
)

const (
	FetchQueueName         = "fetch"
	CriticalFetchQueueName = "critical_fetch"
	SeedQueueName          = "seed"
	UpdateQueueName        = "update"

	DefaultBaseURL = "https://graph.facebook.com/v22.0"
)

func loadBaseURL() string {
	baseURL = viper.GetString("crawl.base_url")
	if baseURL == "" {
		return DefaultBaseURL
	}
	return baseURL
}

var (
	baseURL                 = DefaultBaseURL
	fetchAdAccountsEndpoint = baseURL + "/me/adaccounts?fields=account_id,name,account_status,currency,business_country_code,timezone_offset_hours_utc&limit=100&access_token=%s"
)

type CrawlHandler struct {
	taskClient        *asynq.Client
	scheduler         *asynq.Scheduler
	taskCfg           adcrawl.TaskConfig
	scheduledInterval int // unit: minute
	tokenRepo         domain.TokenRepository
}

func NewCrawlHandler(taskClient *asynq.Client, scheduler *asynq.Scheduler, taskCfg adcrawl.TaskConfig, scheduledInterval int, tokenRepo domain.TokenRepository) *CrawlHandler {
	baseURL = loadBaseURL()
	go func() {
		if err := scheduler.Run(); err != nil {
			logger.AthenaLogger.Errorf("could not run scheduler: %v", err)
		}
	}()
	return &CrawlHandler{
		taskClient:        taskClient,
		scheduler:         scheduler,
		taskCfg:           taskCfg,
		scheduledInterval: scheduledInterval,
		tokenRepo:         tokenRepo,
	}
}

func (h *CrawlHandler) Crawl(ctx context.Context, req *services.CrawlRequest, resp *services.CrawlResponse) error {
	initialURL := fmt.Sprintf(fetchAdAccountsEndpoint, req.AccessToken)
	extraParams := map[string]interface{}{
		"analytic_account_id": cast.ToString(req.AnalyticAccountId),
		"marketer_id":         cast.ToString(req.MarketerId),
		"company_id":          cast.ToString(req.CompanyId),
	}
	initialTask, err := adcrawl.NewFetchAdRelatedObjectsTask(string(adcrawl.TypeFetchAdAccounts),
		initialURL, req.AccessToken, extraParams)
	if err != nil {
		return err
	}
	if _, err := h.taskClient.Enqueue(initialTask, asynq.Queue(FetchQueueName), asynq.MaxRetry(h.taskCfg.MaxRetry)); err != nil {
		return err
	}

	copiedParams := maps.Clone(extraParams)
	copiedParams["schedule"] = true
	scheduledTask, err := adcrawl.NewFetchAdRelatedObjectsTask(string(adcrawl.TypeFetchAdAccounts),
		initialURL, req.AccessToken, copiedParams)
	if err != nil {
		return err
	}

	// Schedule for cron crawling every 60 minutes
	taskId := fmt.Sprintf("t3-%v-%v", req.CompanyId, req.AnalyticAccountId)
	_, err = h.scheduler.Register(
		fmt.Sprintf("%v/%v * * * *", req.AnalyticAccountId%60, h.scheduledInterval),
		scheduledTask,
		asynq.Queue(FetchQueueName),
		asynq.MaxRetry(h.taskCfg.MaxRetry),
		asynq.TaskID(taskId),
		//asynq.Unique(20*time.Hour), // There's no default choice for unique so set it to 30 years
	)
	if err != nil {
		logger.AthenaLogger.Errorf("failed to schedule task: %v", err)
		return err
	}

	resp.Messsage = fmt.Sprintf("Start crawling and scheduling for access token %s", req.AccessToken)
	resp.Success = true

	return nil
}

func (h *CrawlHandler) AdvancedCrawl(ctx context.Context, req *services.AdvancedCrawlRequest, resp *services.AdvancedCrawlResponse) error {
	tasks, err := h.generateTasks(ctx, req)
	if err != nil {
		return err
	}
	for i := range tasks {
		_, err := h.taskClient.EnqueueContext(ctx, tasks[i], asynq.Queue(CriticalFetchQueueName), asynq.MaxRetry(h.taskCfg.MaxRetry))
		if err != nil {
			return err
		}
	}
	return nil
}

var (
	fetchTaskTypes = []adcrawl.TaskType{
		adcrawl.TypeFetchAdAccounts,
		adcrawl.TypeFetchCampaigns,
		adcrawl.TypeFetchAdSets,
		adcrawl.TypeFetchAds,
		adcrawl.TypeFetchSpendings,
	}
)

func (h *CrawlHandler) generateTasks(ctx context.Context, req *services.AdvancedCrawlRequest) ([]*asynq.Task, error) {
	parentTypeIndex := initTypeToIndex(req.InitType)
	if parentTypeIndex == -1 {
		return nil, errors.New("invalid init type")
	}
	firstFetchType := string(fetchTaskTypes[parentTypeIndex])

	tasks := make([]*asynq.Task, 0)
	var token string
	for _, core := range req.Tasks {
		extraParams := map[string]interface{}{
			"marketer_id": cast.ToString(req.MarketerId),
			"critical":    true,
		}

		if core.Metadata != nil {
			metadata := core.Metadata.AsMap()
			for k, v := range metadata {
				extraParams[k] = v
			}
		}

		if len(core.Token) > 0 {
			token = core.Token
		} else {
			subCtx, cancel := context.WithTimeout(ctx, 5*time.Second)
			defer cancel()
			var err error
			token, err = h.tokenRepo.Get(subCtx, req.InitType, core.Id)
			if err != nil {
				return nil, err
			}
		}
		if req.InitType == "AnalyticAccount" {
			extraParams["analytic_account_id"] = core.Id
		}

		url, _ := adcrawl.GenerateURL(firstFetchType, token, &core.Id, extraParams)
		task, err := adcrawl.NewFetchAdRelatedObjectsTask(firstFetchType, url, token, extraParams)
		if err != nil {
			return nil, err
		}
		tasks = append(tasks, task)
	}
	return tasks, nil
}

func initTypeToIndex(initType string) int {
	switch initType {
	case "AnalyticAccount":
		return 0
	case "AdAccount":
		return 1
	case "Campaign":
		return 2
	case "AdSet":
		return 3
	case "Ad":
		return 4
	default:
		return -1
	}
}

func (h *CrawlHandler) RegisterWhenStart(db *sqlx.DB) error {
	if err := h.registerSeedFetchSpendings(); err != nil {
		logger.AthenaLogger.Errorf("failed to register seed task: %v", err)
		return err
	}
	if err := h.registerFetchAllObjects(db); err != nil {
		logger.AthenaLogger.Errorf("failed to register fetch all objects task: %v", err)
		return err
	}
	if err := h.registerUpdateMarketerIDForSpendings(); err != nil {
		logger.AthenaLogger.Errorf("failed to register updating marketer id for spendings task: %v", err)
		return err
	}
	if err := h.registerUpdateCountryIDForCampaigns(); err != nil {
		logger.AthenaLogger.Errorf("failed to register updating country id for campaigns task: %v", err)
		return err
	}
	return nil
}

const UniqueSeedFetchSpendingsID = "seed-fetch-spendings"

func (h *CrawlHandler) registerSeedFetchSpendings() error {
	// Since spending is the most important objects that need to be fetch, we register to crawl it straightaway
	// from AdAccountID to avoid ratelimit affect
	seedInterval := viper.GetInt("crawl.seed.scheduled_interval")
	seedTask, err := adcrawl.NewSeedFetchSpendingsTask()
	if err != nil {
		logger.AthenaLogger.Errorf("failed to create seed task: %v", err)
		return err
	}
	_, err = h.scheduler.Register(fmt.Sprintf("@every %dm", seedInterval), seedTask, asynq.Queue(SeedQueueName), asynq.MaxRetry(h.taskCfg.MaxRetry),
		asynq.TaskID(UniqueSeedFetchSpendingsID))
	if err != nil {
		logger.AthenaLogger.Errorf("failed to schedule seed task: %v", err)
		return err
	}
	return nil
}

type SeedMaterial struct {
	Token             string `db:"token"`
	AnalyticAccountID int64  `db:"id"`
	MarketerID        int64  `db:"marketer_id"`
	CompanyID         int64  `db:"company_id"`
}

func (h *CrawlHandler) registerFetchAllObjects(db *sqlx.DB) error {
	query := `
		SELECT token, id, marketer_id, company_id
		FROM fb.analytic_accounts
		WHERE deleted_at IS NULL
			AND company_id IS NOT NULL
			AND marketer_id IS NOT NULL
		    AND available = true
	`
	var mats []SeedMaterial
	rows, err := db.Queryx(query)
	if err != nil {
		logger.AthenaLogger.Errorf("failed to get materials for seeding fetch object tasks: %v", err)
		return err
	}
	defer rows.Close()
	for rows.Next() {
		var mat SeedMaterial
		if err := rows.Scan(&mat.Token, &mat.AnalyticAccountID, &mat.MarketerID, &mat.CompanyID); err != nil {
			logger.AthenaLogger.Errorf("failed to scan material for seeding fetch object tasks: %v", err)
			return err
		}
		mats = append(mats, mat)
	}

	var g errgroup.Group
	for _, mat := range mats {
		tokenCopy := mat.Token
		analyticAccountIDCopy := mat.AnalyticAccountID
		marketerIDCopy := mat.MarketerID
		companyIDCopy := mat.CompanyID

		g.Go(func() error {
			taskId := fmt.Sprintf("t2-%v-%v", companyIDCopy, analyticAccountIDCopy)
			initialURL := fmt.Sprintf(fetchAdAccountsEndpoint, tokenCopy)
			extraParams := map[string]interface{}{
				"analytic_account_id": cast.ToString(analyticAccountIDCopy),
				"marketer_id":         cast.ToString(marketerIDCopy),
				"company_id":          cast.ToString(companyIDCopy),
				"schedule":            true,
			}
			scheduledTask, err := adcrawl.NewFetchAdRelatedObjectsTask(string(adcrawl.TypeFetchAdAccounts),
				initialURL, tokenCopy, extraParams)
			if err != nil {
				logger.AthenaLogger.Errorf("failed to create scheduled task: %v", err)
				return err
			}

			cron := fmt.Sprintf("%v/%v * * * *", analyticAccountIDCopy%60, h.scheduledInterval)
			_, err = h.scheduler.Register(
				cron,
				scheduledTask,
				asynq.Queue(FetchQueueName),
				asynq.MaxRetry(h.taskCfg.MaxRetry),
				asynq.TaskID(taskId),
				//asynq.Unique(20*time.Minute), // There's no default choice for unique so set it to 30 years
			)
			if err != nil {
				logger.AthenaLogger.Errorf("failed to schedule task: %v", err)
				return err
			}
			return nil
		})
	}

	if err := g.Wait(); err != nil {
		logger.AthenaLogger.Errorf("failed to wait for goroutines: %v", err)
		return err
	}

	return nil
}

const UniqueUpdateMarketerIDForSpendingsID = "update-marketer-id-for-spendings"

func (h *CrawlHandler) registerUpdateMarketerIDForSpendings() error {
	updateInterval := viper.GetInt("crawl.update.scheduled_interval")
	updateTask, err := adcrawl.NewUpdateMarketerIDTask()
	if err != nil {
		logger.AthenaLogger.Errorf("failed to create updating marketer id for spendings task: %v", err)
		return err
	}
	_, err = h.scheduler.Register(fmt.Sprintf("@every %dm", updateInterval), updateTask, asynq.Queue(UpdateQueueName), asynq.MaxRetry(h.taskCfg.MaxRetry),
		asynq.TaskID(UniqueUpdateMarketerIDForSpendingsID))
	if err != nil {
		logger.AthenaLogger.Errorf("failed to schedule updating marketer id for spendings task: %v", err)
		return err
	}
	return nil
}

const UniqueUpdateCountryIDForCampaignsID = "update-country-id-for-campaigns"

func (h *CrawlHandler) registerUpdateCountryIDForCampaigns() error {
	updateInterval := viper.GetInt("crawl.update.scheduled_interval")
	updateTask, err := adcrawl.NewUpdateCountryIDTask()
	if err != nil {
		logger.AthenaLogger.Errorf("failed to create updating country id for campaigns task: %v", err)
		return err
	}

	_, err = h.scheduler.Register(fmt.Sprintf("@every %dm", updateInterval), updateTask, asynq.Queue(UpdateQueueName), asynq.MaxRetry(h.taskCfg.MaxRetry),
		asynq.TaskID(UniqueUpdateCountryIDForCampaignsID))
	if err != nil {
		logger.AthenaLogger.Errorf("failed to schedule updating country id for campaigns task: %v", err)
		return err
	}
	return nil
}
