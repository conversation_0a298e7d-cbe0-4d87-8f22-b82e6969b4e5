package middlewares

import (
	"github.com/spf13/cast"
	"gitlab.com/a7923/athena-go/pkg/config"
	http2 "gitlab.com/a7923/athena-go/pkg/http"
	transhttp "gitlab.com/a7923/athena-go/pkg/transport"
	"gitlab.com/a7923/athena-go/pkg/utils"
	"net/http"
	"strings"
	"time"
)

var legacyHeaders = map[string]bool{
	"authorization": true,
	"project-ids":   true,
}

type LegacyAuthMiddleware struct {
	baseURL string
}

func NewLegacyAuthMiddleware() *LegacyAuthMiddleware {
	return &LegacyAuthMiddleware{baseURL: config.ViperGetStringWithDefault("legacy_gw.base_url", "https://gapi.agbiz.vn")}
}

type ScopeResponse struct {
	Data [][]int `json:"data"`
}

func (v *LegacyAuthMiddleware) Middleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		isAdmin := cast.ToBool(r.Header.Get("X-Gw-isAdmin"))
		if isAdmin {
			next.ServeHTTP(w, r)
			return
		}

		// check scopes
		userId := cast.ToInt64(r.Header.Get("X-Gw-Sub"))
		if userId == 0 {
			transhttp.RespondError(w, http.StatusUnauthorized, "invalid user")
			return
		}

		// check scopes
		resp := &ScopeResponse{}
		err := http2.NewRequest(&http2.RequestOptions{
			URL:     v.baseURL + "/v2/identity/user-profiles/me/scopes",
			Headers: v.filterHeaders(r.Header),
			Timeout: 5 * time.Second,
		}, resp)
		if err != nil {
			transhttp.RespondError(w, http.StatusInternalServerError, "internal error")
			return
		}

		if resp != nil && len(resp.Data) > 0 {
			r.Header.Set("X-Gw-Scopes", utils.ToJSONString(resp))
		}
		next.ServeHTTP(w, r)
	})
}

func (v *LegacyAuthMiddleware) filterHeaders(header http.Header) http.Header {
	resp := make(http.Header)
	for k, v := range header {
		if legacyHeaders[strings.ToLower(k)] {
			resp[k] = v
		}
	}
	return resp
}
