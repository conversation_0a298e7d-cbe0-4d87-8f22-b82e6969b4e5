CREATE MATERIALIZED VIEW analytics.mv_base_ads
REFRESH EVERY 7 MINUTE TO analytics.base_ads
(
    `ad_id` Int64,
    `ts` Nullable(DateTime),
    `spent` Decimal(16, 2),
    `cpm` Decimal(16, 2),
    `cpc` Decimal(16, 2),
    `ctr` Decimal(16, 2),
    `clicks` <PERSON><PERSON>l(16, 2),
    `impressions` Decimal(16, 2),
    `adset_id` Nullable(Int64),
    `campaign_id` Nullable(Int64),
    `adaccount_id` Nullable(Int64),
    `adset` Nullable(String),
    `campaign` Nullable(String),
    `adaccount` Nullable(String),
    `team_in_charge` UInt8,
    `company_id` Int64,
    `marketer_id` Int64,
    `country_id` Int32,
    `project_id` Int64,
    `product_id` Int64
)
AS WITH
    filtered_spendings AS
    (
        SELECT
            ad_id,
            date_start,
            hourly_stat,
            argMax(raw_numeric, _peerdb_version) AS spent,
            argMax(exchange_rate, _peerdb_version) AS exchange_rate,
            argMax(cpm, _peerdb_version) AS cpm,
            argMax(cpc, _peerdb_version) AS cpc,
            argMax(ctr, _peerdb_version) AS ctr,
            argMax(clicks, _peerdb_version) AS clicks,
            argMax(impressions, _peerdb_version) AS impressions,
            argMax(marketer_id, _peerdb_version) AS marketer_id
        FROM marketing.fb_spendings
        WHERE (deleted_at IS NULL) AND (date_start > '2025-01-01')
        GROUP BY
            ad_id,
            date_start,
            hourly_stat
    ),
    ads AS
    (
        SELECT
            fb_ads.id,
            argMax(fb_ads.page_id, fb_ads._peerdb_version) AS page_id,
            argMax(fb_ads.adset_id, fb_ads._peerdb_version) AS adset_id
        FROM marketing.fb_ads
        WHERE fb_ads._peerdb_is_deleted = 0
        GROUP BY fb_ads.id
    ),
    adset AS
    (
        SELECT
            fb_adsets.id,
            argMax(fb_adsets.campaign_id, fb_adsets._peerdb_version) AS campaign_id,
            argMax(fb_adsets.adaccount_id, fb_adsets._peerdb_version) AS adaccount_id,
            argMax(fb_adsets.name, fb_adsets._peerdb_version) AS name,
            argMax(fb_adsets.optimization_goal, fb_adsets._peerdb_version) AS optimization_goal
        FROM marketing.fb_adsets
        WHERE fb_adsets._peerdb_is_deleted = 0
        GROUP BY fb_adsets.id
    ),
    campaigns AS
    (
        SELECT
            fb_campaigns.id AS campaign_id,
            argMax(fb_campaigns.name, fb_campaigns._peerdb_version) AS name,
            argMax(fb_campaigns.marketer_id, fb_campaigns._peerdb_version) AS marketer_id,
            argMax(fb_campaigns.company_id, fb_campaigns._peerdb_version) AS company_id
        FROM marketing.fb_campaigns
        WHERE (fb_campaigns._peerdb_is_deleted = 0) AND (fb_campaigns.deleted_at IS NULL)
        GROUP BY fb_campaigns.id
    ),
    adaccounts AS
    (
        SELECT
            fb_adaccounts.id AS adaccount_id,
            argMax(fb_adaccounts.name, fb_adaccounts._peerdb_version) AS name,
            argMax(fb_adaccounts.country_id, fb_adaccounts._peerdb_version) AS country_id,
            argMax(fb_adaccounts.analytic_account_id, fb_adaccounts._peerdb_version) AS analytic_id,
            argMax(fb_adaccounts.marketer_id, fb_adaccounts._peerdb_version) AS marketer_id,
            argMax(fb_adaccounts.timezone_offset_hours_utc, fb_adaccounts._peerdb_version) AS timezone_offset
        FROM marketing.fb_adaccounts
        WHERE (fb_adaccounts._peerdb_is_deleted = 0) AND (fb_adaccounts.deleted_at IS NULL)
        GROUP BY fb_adaccounts.id
    ),
    fanpage AS
    (
        SELECT
            toString(fanpage.id) AS fanpage_id,
            argMax(fanpage.company_id, fanpage._peerdb_version) AS company_id,
            argMax(fanpage.project_id, fanpage._peerdb_version) AS project_id,
            argMax(fanpage.product_id, fanpage._peerdb_version) AS product_id,
            argMax(fanpage.country_id, fanpage._peerdb_version) AS country_id
        FROM message_prod.fanpage
        WHERE fanpage._peerdb_is_deleted = 0
        GROUP BY fanpage.id
    ),
    analytic_accounts AS
    (
        SELECT
            fb_analytic_accounts.id AS analytic_account_id,
            argMax(fb_analytic_accounts.company_id, fb_analytic_accounts._peerdb_version) AS company_id,
            argMax(fb_analytic_accounts.marketer_id, fb_analytic_accounts._peerdb_version) AS marketer_id
        FROM marketing.fb_analytic_accounts
        WHERE (fb_analytic_accounts._peerdb_is_deleted = 0) AND (fb_analytic_accounts.deleted_at IS NULL)
        GROUP BY fb_analytic_accounts.id
    ),
    ads_biz AS
    (
        SELECT
            toInt64OrZero(ads_biz.ad_id) AS ad_id,
            argMax(ads_biz.product_id, ads_biz._peerdb_version) AS product_id
        FROM marketing.ads_biz
        WHERE ads_biz._peerdb_is_deleted = 0
        GROUP BY ads_biz.ad_id
    )
SELECT
    s.ad_id AS ad_id,
    addHours(toDate(s.date_start), s.hourly_stat) - toIntervalHour(a.timezone_offset) AS ts,
    s.spent * coalesce(exchange_rate, 1) AS spent,
    s.cpm AS cpm,
    s.cpc AS cpc,
    s.ctr AS ctr,
    toInt64(s.clicks) AS clicks,
    toInt64(s.impressions) AS impressions,
    argMax(ad.adset_id, ad.id) AS adset_id,
    argMax(adset.campaign_id, adset.id) AS campaign_id,
    argMax(adset.adaccount_id, adset.id) AS adaccount_id,
    argMax(adset.name, ad.id) AS adset,
    argMax(c.name, c.campaign_id) AS campaign,
    argMax(a.name, a.adaccount_id) AS adaccount,
    coalesce(if(max(adset.optimization_goal) = 'OFFSITE_CONVERSIONS', 1, 2), 0) AS team_in_charge,
    coalesce(multiIf(max(c.company_id) > 0, max(c.company_id), max(an.company_id)), 0) AS company_id,
    coalesce(multiIf(max(c.marketer_id) > 0, max(c.marketer_id), max(a.marketer_id) > 0, max(a.marketer_id), max(an.marketer_id)), 0) AS marketer_id,
    coalesce(if(team_in_charge = 1, max(a.country_id), max(f.country_id)), 0) AS country_id,
    coalesce(if(team_in_charge = 1, max(mp.project_id), max(f.project_id)), 0) AS project_id,
    coalesce(if(team_in_charge = 1, max(ab.product_id), max(f.product_id)), 0) AS product_id
FROM filtered_spendings AS s
         INNER JOIN ads AS ad ON ad.id = s.ad_id
         INNER JOIN adset AS adset ON adset.id = ad.adset_id
         INNER JOIN campaigns AS c ON c.campaign_id = adset.campaign_id
         INNER JOIN adaccounts AS a ON a.adaccount_id = adset.adaccount_id
         LEFT JOIN fanpage AS f ON f.fanpage_id = ad.page_id
         LEFT JOIN analytic_accounts AS an ON an.analytic_account_id = a.analytic_id
         LEFT JOIN analytics.marketer_projects AS mp ON mp.id = c.marketer_id
         LEFT JOIN ads_biz AS ab ON ab.ad_id = ad.id
GROUP BY
    s.ad_id,
    ts,
    spent,
    s.cpm,
    s.cpc,
    s.ctr,
    s.clicks,
    s.impressions