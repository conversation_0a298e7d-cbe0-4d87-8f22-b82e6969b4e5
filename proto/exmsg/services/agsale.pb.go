// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: services/agsale.proto

package services

import (
	models "gitlab.com/a7923/athena-go/proto/exmsg/models"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type OrderDisplayIdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProjectId     int64                  `protobuf:"varint,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OrderDisplayIdRequest) Reset() {
	*x = OrderDisplayIdRequest{}
	mi := &file_services_agsale_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OrderDisplayIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderDisplayIdRequest) ProtoMessage() {}

func (x *OrderDisplayIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_agsale_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderDisplayIdRequest.ProtoReflect.Descriptor instead.
func (*OrderDisplayIdRequest) Descriptor() ([]byte, []int) {
	return file_services_agsale_proto_rawDescGZIP(), []int{0}
}

func (x *OrderDisplayIdRequest) GetProjectId() int64 {
	if x != nil {
		return x.ProjectId
	}
	return 0
}

type CareReasonRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	ReasonKey     string                 `protobuf:"bytes,2,opt,name=reason_key,json=reasonKey,proto3" json:"reason_key,omitempty"`
	CompanyId     int64                  `protobuf:"varint,3,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	State         int64                  `protobuf:"varint,5,opt,name=state,proto3" json:"state,omitempty"`
	CountryId     int64                  `protobuf:"varint,6,opt,name=country_id,json=countryId,proto3" json:"country_id,omitempty"`
	Query         *models.Query          `protobuf:"bytes,7,opt,name=query,proto3" json:"query,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CareReasonRequest) Reset() {
	*x = CareReasonRequest{}
	mi := &file_services_agsale_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CareReasonRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CareReasonRequest) ProtoMessage() {}

func (x *CareReasonRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_agsale_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CareReasonRequest.ProtoReflect.Descriptor instead.
func (*CareReasonRequest) Descriptor() ([]byte, []int) {
	return file_services_agsale_proto_rawDescGZIP(), []int{1}
}

func (x *CareReasonRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CareReasonRequest) GetReasonKey() string {
	if x != nil {
		return x.ReasonKey
	}
	return ""
}

func (x *CareReasonRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *CareReasonRequest) GetState() int64 {
	if x != nil {
		return x.State
	}
	return 0
}

func (x *CareReasonRequest) GetCountryId() int64 {
	if x != nil {
		return x.CountryId
	}
	return 0
}

func (x *CareReasonRequest) GetQuery() *models.Query {
	if x != nil {
		return x.Query
	}
	return nil
}

type CareReasonResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CareReasons   []*models.CareReason   `protobuf:"bytes,1,rep,name=care_reasons,json=careReasons,proto3" json:"care_reasons,omitempty"`
	ExecResult    *models.SQLResult      `protobuf:"bytes,2,opt,name=exec_result,json=execResult,proto3" json:"exec_result,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CareReasonResponse) Reset() {
	*x = CareReasonResponse{}
	mi := &file_services_agsale_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CareReasonResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CareReasonResponse) ProtoMessage() {}

func (x *CareReasonResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_agsale_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CareReasonResponse.ProtoReflect.Descriptor instead.
func (*CareReasonResponse) Descriptor() ([]byte, []int) {
	return file_services_agsale_proto_rawDescGZIP(), []int{2}
}

func (x *CareReasonResponse) GetCareReasons() []*models.CareReason {
	if x != nil {
		return x.CareReasons
	}
	return nil
}

func (x *CareReasonResponse) GetExecResult() *models.SQLResult {
	if x != nil {
		return x.ExecResult
	}
	return nil
}

type LeadRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	Query *models.Query          `protobuf:"bytes,1,opt,name=query,proto3" json:"query,omitempty"`
	// for insert
	Lead             *models.Lead  `protobuf:"bytes,2,opt,name=lead,proto3" json:"lead,omitempty"`
	Order            *models.Order `protobuf:"bytes,3,opt,name=order,proto3" json:"order,omitempty"`
	UsingTransaction bool          `protobuf:"varint,4,opt,name=using_transaction,json=usingTransaction,proto3" json:"using_transaction,omitempty"`
	// filter
	Id            int64                `protobuf:"varint,5,opt,name=id,proto3" json:"id,omitempty"`
	OrderId       int64                `protobuf:"varint,6,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	UpdateFields  []string             `protobuf:"bytes,7,rep,name=update_fields,json=updateFields,proto3" json:"update_fields,omitempty"`
	LeadCare      *models.LeadCare     `protobuf:"bytes,8,opt,name=lead_care,json=leadCare,proto3" json:"lead_care,omitempty"`
	LeadCareItem  *models.LeadCareItem `protobuf:"bytes,9,opt,name=lead_care_item,json=leadCareItem,proto3" json:"lead_care_item,omitempty"`
	LeadType      int64                `protobuf:"varint,10,opt,name=lead_type,json=leadType,proto3" json:"lead_type,omitempty"`
	NotInStatus   []int64              `protobuf:"varint,11,rep,packed,name=not_in_status,json=notInStatus,proto3" json:"not_in_status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LeadRequest) Reset() {
	*x = LeadRequest{}
	mi := &file_services_agsale_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LeadRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LeadRequest) ProtoMessage() {}

func (x *LeadRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_agsale_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LeadRequest.ProtoReflect.Descriptor instead.
func (*LeadRequest) Descriptor() ([]byte, []int) {
	return file_services_agsale_proto_rawDescGZIP(), []int{3}
}

func (x *LeadRequest) GetQuery() *models.Query {
	if x != nil {
		return x.Query
	}
	return nil
}

func (x *LeadRequest) GetLead() *models.Lead {
	if x != nil {
		return x.Lead
	}
	return nil
}

func (x *LeadRequest) GetOrder() *models.Order {
	if x != nil {
		return x.Order
	}
	return nil
}

func (x *LeadRequest) GetUsingTransaction() bool {
	if x != nil {
		return x.UsingTransaction
	}
	return false
}

func (x *LeadRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *LeadRequest) GetOrderId() int64 {
	if x != nil {
		return x.OrderId
	}
	return 0
}

func (x *LeadRequest) GetUpdateFields() []string {
	if x != nil {
		return x.UpdateFields
	}
	return nil
}

func (x *LeadRequest) GetLeadCare() *models.LeadCare {
	if x != nil {
		return x.LeadCare
	}
	return nil
}

func (x *LeadRequest) GetLeadCareItem() *models.LeadCareItem {
	if x != nil {
		return x.LeadCareItem
	}
	return nil
}

func (x *LeadRequest) GetLeadType() int64 {
	if x != nil {
		return x.LeadType
	}
	return 0
}

func (x *LeadRequest) GetNotInStatus() []int64 {
	if x != nil {
		return x.NotInStatus
	}
	return nil
}

type OrderRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Query          *models.Query          `protobuf:"bytes,1,opt,name=query,proto3" json:"query,omitempty"`
	Id             int64                  `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	InsertOrders   []*models.Order        `protobuf:"bytes,3,rep,name=insert_orders,json=insertOrders,proto3" json:"insert_orders,omitempty"` // for insert
	Ids            []int64                `protobuf:"varint,4,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	OrderId        int64                  `protobuf:"varint,5,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	SourceId       int64                  `protobuf:"varint,6,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	UpdateFields   []string               `protobuf:"bytes,7,rep,name=update_fields,json=updateFields,proto3" json:"update_fields,omitempty"`
	Order          *models.Order          `protobuf:"bytes,8,opt,name=order,proto3" json:"order,omitempty"`
	NotEqualStatus int64                  `protobuf:"varint,9,opt,name=not_equal_status,json=notEqualStatus,proto3" json:"not_equal_status,omitempty"`
	DisplayId      string                 `protobuf:"bytes,10,opt,name=display_id,json=displayId,proto3" json:"display_id,omitempty"`
	CompanyId      int64                  `protobuf:"varint,11,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *OrderRequest) Reset() {
	*x = OrderRequest{}
	mi := &file_services_agsale_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OrderRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderRequest) ProtoMessage() {}

func (x *OrderRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_agsale_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderRequest.ProtoReflect.Descriptor instead.
func (*OrderRequest) Descriptor() ([]byte, []int) {
	return file_services_agsale_proto_rawDescGZIP(), []int{4}
}

func (x *OrderRequest) GetQuery() *models.Query {
	if x != nil {
		return x.Query
	}
	return nil
}

func (x *OrderRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *OrderRequest) GetInsertOrders() []*models.Order {
	if x != nil {
		return x.InsertOrders
	}
	return nil
}

func (x *OrderRequest) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *OrderRequest) GetOrderId() int64 {
	if x != nil {
		return x.OrderId
	}
	return 0
}

func (x *OrderRequest) GetSourceId() int64 {
	if x != nil {
		return x.SourceId
	}
	return 0
}

func (x *OrderRequest) GetUpdateFields() []string {
	if x != nil {
		return x.UpdateFields
	}
	return nil
}

func (x *OrderRequest) GetOrder() *models.Order {
	if x != nil {
		return x.Order
	}
	return nil
}

func (x *OrderRequest) GetNotEqualStatus() int64 {
	if x != nil {
		return x.NotEqualStatus
	}
	return 0
}

func (x *OrderRequest) GetDisplayId() string {
	if x != nil {
		return x.DisplayId
	}
	return ""
}

func (x *OrderRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

type OrderDisplayIdCount struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProjectId     string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	Date          string                 `protobuf:"bytes,2,opt,name=date,proto3" json:"date,omitempty"`
	Count         string                 `protobuf:"bytes,3,opt,name=count,proto3" json:"count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OrderDisplayIdCount) Reset() {
	*x = OrderDisplayIdCount{}
	mi := &file_services_agsale_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OrderDisplayIdCount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderDisplayIdCount) ProtoMessage() {}

func (x *OrderDisplayIdCount) ProtoReflect() protoreflect.Message {
	mi := &file_services_agsale_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderDisplayIdCount.ProtoReflect.Descriptor instead.
func (*OrderDisplayIdCount) Descriptor() ([]byte, []int) {
	return file_services_agsale_proto_rawDescGZIP(), []int{5}
}

func (x *OrderDisplayIdCount) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *OrderDisplayIdCount) GetDate() string {
	if x != nil {
		return x.Date
	}
	return ""
}

func (x *OrderDisplayIdCount) GetCount() string {
	if x != nil {
		return x.Count
	}
	return ""
}

type OrderResponse struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	Orders              []*models.Order        `protobuf:"bytes,1,rep,name=orders,proto3" json:"orders,omitempty"`
	ExecResult          *models.SQLResult      `protobuf:"bytes,2,opt,name=exec_result,json=execResult,proto3" json:"exec_result,omitempty"`
	OrderProducts       []*models.OrderProduct `protobuf:"bytes,5,rep,name=order_products,json=orderProducts,proto3" json:"order_products,omitempty"`
	OrderDisplayIdCount *OrderDisplayIdCount   `protobuf:"bytes,6,opt,name=order_display_id_count,json=orderDisplayIdCount,proto3" json:"order_display_id_count,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *OrderResponse) Reset() {
	*x = OrderResponse{}
	mi := &file_services_agsale_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OrderResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderResponse) ProtoMessage() {}

func (x *OrderResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_agsale_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderResponse.ProtoReflect.Descriptor instead.
func (*OrderResponse) Descriptor() ([]byte, []int) {
	return file_services_agsale_proto_rawDescGZIP(), []int{6}
}

func (x *OrderResponse) GetOrders() []*models.Order {
	if x != nil {
		return x.Orders
	}
	return nil
}

func (x *OrderResponse) GetExecResult() *models.SQLResult {
	if x != nil {
		return x.ExecResult
	}
	return nil
}

func (x *OrderResponse) GetOrderProducts() []*models.OrderProduct {
	if x != nil {
		return x.OrderProducts
	}
	return nil
}

func (x *OrderResponse) GetOrderDisplayIdCount() *OrderDisplayIdCount {
	if x != nil {
		return x.OrderDisplayIdCount
	}
	return nil
}

type LeadResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Leads         []*models.Lead         `protobuf:"bytes,2,rep,name=leads,proto3" json:"leads,omitempty"`
	Order         []*models.Order        `protobuf:"bytes,3,rep,name=order,proto3" json:"order,omitempty"`
	ExecResult    *models.SQLResult      `protobuf:"bytes,4,opt,name=exec_result,json=execResult,proto3" json:"exec_result,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LeadResponse) Reset() {
	*x = LeadResponse{}
	mi := &file_services_agsale_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LeadResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LeadResponse) ProtoMessage() {}

func (x *LeadResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_agsale_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LeadResponse.ProtoReflect.Descriptor instead.
func (*LeadResponse) Descriptor() ([]byte, []int) {
	return file_services_agsale_proto_rawDescGZIP(), []int{7}
}

func (x *LeadResponse) GetLeads() []*models.Lead {
	if x != nil {
		return x.Leads
	}
	return nil
}

func (x *LeadResponse) GetOrder() []*models.Order {
	if x != nil {
		return x.Order
	}
	return nil
}

func (x *LeadResponse) GetExecResult() *models.SQLResult {
	if x != nil {
		return x.ExecResult
	}
	return nil
}

type LandingPageRequest struct {
	state                    protoimpl.MessageState     `protogen:"open.v1"`
	Query                    *models.Query              `protobuf:"bytes,1,opt,name=query,proto3" json:"query,omitempty"`
	Id                       string                     `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	Ids                      []string                   `protobuf:"bytes,3,rep,name=ids,proto3" json:"ids,omitempty"`
	LpCompositeKeys          []*LandingPageCompositeKey `protobuf:"bytes,4,rep,name=lp_composite_keys,json=lpCompositeKeys,proto3" json:"lp_composite_keys,omitempty"`
	GetAll                   bool                       `protobuf:"varint,5,opt,name=get_all,json=getAll,proto3" json:"get_all,omitempty"`
	Search                   *string                    `protobuf:"bytes,6,opt,name=search,proto3,oneof" json:"search,omitempty"`
	IsAdmin                  bool                       `protobuf:"varint,7,opt,name=is_admin,json=isAdmin,proto3" json:"is_admin,omitempty"`
	IsSearchWithCompositeKey bool                       `protobuf:"varint,8,opt,name=is_search_with_composite_key,json=isSearchWithCompositeKey,proto3" json:"is_search_with_composite_key,omitempty"`
	unknownFields            protoimpl.UnknownFields
	sizeCache                protoimpl.SizeCache
}

func (x *LandingPageRequest) Reset() {
	*x = LandingPageRequest{}
	mi := &file_services_agsale_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LandingPageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LandingPageRequest) ProtoMessage() {}

func (x *LandingPageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_agsale_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LandingPageRequest.ProtoReflect.Descriptor instead.
func (*LandingPageRequest) Descriptor() ([]byte, []int) {
	return file_services_agsale_proto_rawDescGZIP(), []int{8}
}

func (x *LandingPageRequest) GetQuery() *models.Query {
	if x != nil {
		return x.Query
	}
	return nil
}

func (x *LandingPageRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *LandingPageRequest) GetIds() []string {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *LandingPageRequest) GetLpCompositeKeys() []*LandingPageCompositeKey {
	if x != nil {
		return x.LpCompositeKeys
	}
	return nil
}

func (x *LandingPageRequest) GetGetAll() bool {
	if x != nil {
		return x.GetAll
	}
	return false
}

func (x *LandingPageRequest) GetSearch() string {
	if x != nil && x.Search != nil {
		return *x.Search
	}
	return ""
}

func (x *LandingPageRequest) GetIsAdmin() bool {
	if x != nil {
		return x.IsAdmin
	}
	return false
}

func (x *LandingPageRequest) GetIsSearchWithCompositeKey() bool {
	if x != nil {
		return x.IsSearchWithCompositeKey
	}
	return false
}

type LandingPageCompositeKey struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CountryId     int32                  `protobuf:"varint,1,opt,name=country_id,json=countryId,proto3" json:"country_id,omitempty"`
	ProjectId     int32                  `protobuf:"varint,2,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LandingPageCompositeKey) Reset() {
	*x = LandingPageCompositeKey{}
	mi := &file_services_agsale_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LandingPageCompositeKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LandingPageCompositeKey) ProtoMessage() {}

func (x *LandingPageCompositeKey) ProtoReflect() protoreflect.Message {
	mi := &file_services_agsale_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LandingPageCompositeKey.ProtoReflect.Descriptor instead.
func (*LandingPageCompositeKey) Descriptor() ([]byte, []int) {
	return file_services_agsale_proto_rawDescGZIP(), []int{9}
}

func (x *LandingPageCompositeKey) GetCountryId() int32 {
	if x != nil {
		return x.CountryId
	}
	return 0
}

func (x *LandingPageCompositeKey) GetProjectId() int32 {
	if x != nil {
		return x.ProjectId
	}
	return 0
}

type LandingPageResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	LandingPages  []*models.LandingPage  `protobuf:"bytes,1,rep,name=landing_pages,json=landingPages,proto3" json:"landing_pages,omitempty"`
	ExecResult    *models.SQLResult      `protobuf:"bytes,2,opt,name=exec_result,json=execResult,proto3" json:"exec_result,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LandingPageResponse) Reset() {
	*x = LandingPageResponse{}
	mi := &file_services_agsale_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LandingPageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LandingPageResponse) ProtoMessage() {}

func (x *LandingPageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_agsale_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LandingPageResponse.ProtoReflect.Descriptor instead.
func (*LandingPageResponse) Descriptor() ([]byte, []int) {
	return file_services_agsale_proto_rawDescGZIP(), []int{10}
}

func (x *LandingPageResponse) GetLandingPages() []*models.LandingPage {
	if x != nil {
		return x.LandingPages
	}
	return nil
}

func (x *LandingPageResponse) GetExecResult() *models.SQLResult {
	if x != nil {
		return x.ExecResult
	}
	return nil
}

var File_services_agsale_proto protoreflect.FileDescriptor

var file_services_agsale_proto_rawDesc = string([]byte{
	0x0a, 0x15, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x61, 0x67, 0x73, 0x61, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x1a, 0x11, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x6c, 0x65, 0x61, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x12, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0x36, 0x0a, 0x15, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x69, 0x73, 0x70,
	0x6c, 0x61, 0x79, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x22, 0xc1, 0x01, 0x0a, 0x11,
	0x43, 0x61, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x5f, 0x6b, 0x65, 0x79, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x4b, 0x65, 0x79,
	0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12,
	0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05,
	0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x72, 0x79, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x22,
	0x8b, 0x01, 0x0a, 0x12, 0x43, 0x61, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3b, 0x0a, 0x0c, 0x63, 0x61, 0x72, 0x65, 0x5f, 0x72,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x65,
	0x78, 0x6d, 0x73, 0x67, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x43, 0x61, 0x72, 0x65,
	0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52, 0x0b, 0x63, 0x61, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x73, 0x12, 0x38, 0x0a, 0x0b, 0x65, 0x78, 0x65, 0x63, 0x5f, 0x72, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x53, 0x51, 0x4c, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x52, 0x0a, 0x65, 0x78, 0x65, 0x63, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0xc0, 0x03,
	0x0a, 0x0b, 0x4c, 0x65, 0x61, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x29, 0x0a,
	0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x65,
	0x78, 0x6d, 0x73, 0x67, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x52, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x12, 0x26, 0x0a, 0x04, 0x6c, 0x65, 0x61, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x4c, 0x65, 0x61, 0x64, 0x52, 0x04, 0x6c, 0x65, 0x61, 0x64,
	0x12, 0x29, 0x0a, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x13, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x52, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x2b, 0x0a, 0x11, 0x75,
	0x73, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x75, 0x73, 0x69, 0x6e, 0x67, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x66, 0x69,
	0x65, 0x6c, 0x64, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x12, 0x33, 0x0a, 0x09, 0x6c, 0x65, 0x61, 0x64,
	0x5f, 0x63, 0x61, 0x72, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x65, 0x78,
	0x6d, 0x73, 0x67, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x4c, 0x65, 0x61, 0x64, 0x43,
	0x61, 0x72, 0x65, 0x52, 0x08, 0x6c, 0x65, 0x61, 0x64, 0x43, 0x61, 0x72, 0x65, 0x12, 0x40, 0x0a,
	0x0e, 0x6c, 0x65, 0x61, 0x64, 0x5f, 0x63, 0x61, 0x72, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x4c, 0x65, 0x61, 0x64, 0x43, 0x61, 0x72, 0x65, 0x49, 0x74, 0x65,
	0x6d, 0x52, 0x0c, 0x6c, 0x65, 0x61, 0x64, 0x43, 0x61, 0x72, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x12,
	0x1b, 0x0a, 0x09, 0x6c, 0x65, 0x61, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x08, 0x6c, 0x65, 0x61, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x0d,
	0x6e, 0x6f, 0x74, 0x5f, 0x69, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0b, 0x20,
	0x03, 0x28, 0x03, 0x52, 0x0b, 0x6e, 0x6f, 0x74, 0x49, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x22, 0x85, 0x03, 0x0a, 0x0c, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x29, 0x0a, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x13, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x38, 0x0a, 0x0d,
	0x69, 0x6e, 0x73, 0x65, 0x72, 0x74, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x0c, 0x69, 0x6e, 0x73, 0x65, 0x72, 0x74,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x03, 0x52, 0x03, 0x69, 0x64, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64,
	0x12, 0x23, 0x0a, 0x0d, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64,
	0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x46,
	0x69, 0x65, 0x6c, 0x64, 0x73, 0x12, 0x29, 0x0a, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x12, 0x28, 0x0a, 0x10, 0x6e, 0x6f, 0x74, 0x5f, 0x65, 0x71, 0x75, 0x61, 0x6c, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x6e, 0x6f, 0x74, 0x45,
	0x71, 0x75, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x69,
	0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x22, 0x5e, 0x0a, 0x13, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x49, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x61,
	0x74, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x93, 0x02, 0x0a, 0x0d, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2b, 0x0a, 0x06, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x65, 0x78, 0x6d,
	0x73, 0x67, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52,
	0x06, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x12, 0x38, 0x0a, 0x0b, 0x65, 0x78, 0x65, 0x63, 0x5f,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x65,
	0x78, 0x6d, 0x73, 0x67, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x53, 0x51, 0x4c, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x0a, 0x65, 0x78, 0x65, 0x63, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x41, 0x0a, 0x0e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x64, 0x75,
	0x63, 0x74, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x65, 0x78, 0x6d, 0x73,
	0x67, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x72,
	0x6f, 0x64, 0x75, 0x63, 0x74, 0x52, 0x0d, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x64,
	0x75, 0x63, 0x74, 0x73, 0x12, 0x58, 0x0a, 0x16, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x64, 0x69,
	0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x69, 0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x69, 0x73, 0x70, 0x6c,
	0x61, 0x79, 0x49, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x13, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x49, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x9d,
	0x01, 0x0a, 0x0c, 0x4c, 0x65, 0x61, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x28, 0x0a, 0x05, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x4c, 0x65,
	0x61, 0x64, 0x52, 0x05, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x12, 0x29, 0x0a, 0x05, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x05, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x12, 0x38, 0x0a, 0x0b, 0x65, 0x78, 0x65, 0x63, 0x5f, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x65, 0x78, 0x6d, 0x73,
	0x67, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x53, 0x51, 0x4c, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x52, 0x0a, 0x65, 0x78, 0x65, 0x63, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0xd2,
	0x02, 0x0a, 0x12, 0x4c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x29, 0x0a, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x03, 0x69,
	0x64, 0x73, 0x12, 0x53, 0x0a, 0x11, 0x6c, 0x70, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x73, 0x69,
	0x74, 0x65, 0x5f, 0x6b, 0x65, 0x79, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e,
	0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x4c,
	0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x65, 0x4b, 0x65, 0x79, 0x52, 0x0f, 0x6c, 0x70, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x65, 0x4b, 0x65, 0x79, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x67, 0x65, 0x74, 0x5f, 0x61,
	0x6c, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x67, 0x65, 0x74, 0x41, 0x6c, 0x6c,
	0x12, 0x1b, 0x0a, 0x06, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x48, 0x00, 0x52, 0x06, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x88, 0x01, 0x01, 0x12, 0x19, 0x0a,
	0x08, 0x69, 0x73, 0x5f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x07, 0x69, 0x73, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x12, 0x3e, 0x0a, 0x1c, 0x69, 0x73, 0x5f, 0x73,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x77, 0x69, 0x74, 0x68, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f,
	0x73, 0x69, 0x74, 0x65, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x18,
	0x69, 0x73, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x57, 0x69, 0x74, 0x68, 0x43, 0x6f, 0x6d, 0x70,
	0x6f, 0x73, 0x69, 0x74, 0x65, 0x4b, 0x65, 0x79, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x73, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x22, 0x57, 0x0a, 0x17, 0x4c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x50, 0x61,
	0x67, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x65, 0x4b, 0x65, 0x79, 0x12, 0x1d,
	0x0a, 0x0a, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x09, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x49, 0x64, 0x12, 0x1d, 0x0a,
	0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x22, 0x8f, 0x01, 0x0a,
	0x13, 0x4c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3e, 0x0a, 0x0d, 0x6c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f,
	0x70, 0x61, 0x67, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x65, 0x78,
	0x6d, 0x73, 0x67, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x4c, 0x61, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x50, 0x61, 0x67, 0x65, 0x52, 0x0c, 0x6c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x50,
	0x61, 0x67, 0x65, 0x73, 0x12, 0x38, 0x0a, 0x0b, 0x65, 0x78, 0x65, 0x63, 0x5f, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x65, 0x78, 0x6d, 0x73,
	0x67, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x53, 0x51, 0x4c, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x52, 0x0a, 0x65, 0x78, 0x65, 0x63, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x32, 0xa7,
	0x08, 0x0a, 0x0d, 0x41, 0x47, 0x53, 0x61, 0x6c, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x12, 0x47, 0x0a, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4c, 0x65, 0x61, 0x64, 0x12, 0x1b,
	0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e,
	0x4c, 0x65, 0x61, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x65, 0x78,
	0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x4c, 0x65, 0x61,
	0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x45, 0x0a, 0x08, 0x47, 0x65, 0x74,
	0x4c, 0x65, 0x61, 0x64, 0x73, 0x12, 0x1b, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x4c, 0x65, 0x61, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x2e, 0x4c, 0x65, 0x61, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x48, 0x0a, 0x09, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x12, 0x1c, 0x2e,
	0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x65, 0x78,
	0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0c, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x12, 0x1c, 0x2e, 0x65, 0x78, 0x6d,
	0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x47, 0x0a, 0x0a, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x4c, 0x65, 0x61, 0x64, 0x12, 0x1b, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x4c, 0x65, 0x61, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x2e, 0x4c, 0x65, 0x61, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x52, 0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4c, 0x65, 0x61, 0x64, 0x43, 0x61,
	0x72, 0x65, 0x41, 0x6e, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x1b, 0x2e, 0x65, 0x78, 0x6d, 0x73,
	0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x4c, 0x65, 0x61, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x4c, 0x65, 0x61, 0x64, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x57, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x43, 0x61, 0x72, 0x65, 0x52,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x73, 0x12, 0x21, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x43, 0x61, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x65, 0x78, 0x6d, 0x73,
	0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x43, 0x61, 0x72, 0x65, 0x52,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4f, 0x0a,
	0x10, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74,
	0x73, 0x12, 0x1c, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x1d, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5a,
	0x0a, 0x0f, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x67, 0x65,
	0x73, 0x12, 0x22, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x2e, 0x4c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x4c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x50, 0x61,
	0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x59, 0x0a, 0x11, 0x47, 0x65,
	0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x49, 0x64, 0x12,
	0x25, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x49, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4a, 0x0a, 0x0b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x12, 0x1c, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x53, 0x0a, 0x14, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x44, 0x75, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x1c, 0x2e, 0x65, 0x78, 0x6d, 0x73,
	0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x50, 0x0a, 0x13, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65,
	0x44, 0x75, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x65, 0x4c, 0x65, 0x61, 0x64, 0x12, 0x1b, 0x2e,
	0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x4c,
	0x65, 0x61, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x65, 0x78, 0x6d,
	0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x4c, 0x65, 0x61, 0x64,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x3a, 0x5a, 0x38, 0x67, 0x69, 0x74, 0x6c,
	0x61, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x61, 0x37, 0x39, 0x32, 0x33, 0x2f, 0x61, 0x74, 0x68,
	0x65, 0x6e, 0x61, 0x2d, 0x67, 0x6f, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x65, 0x78, 0x6d,
	0x73, 0x67, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x3b, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_services_agsale_proto_rawDescOnce sync.Once
	file_services_agsale_proto_rawDescData []byte
)

func file_services_agsale_proto_rawDescGZIP() []byte {
	file_services_agsale_proto_rawDescOnce.Do(func() {
		file_services_agsale_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_services_agsale_proto_rawDesc), len(file_services_agsale_proto_rawDesc)))
	})
	return file_services_agsale_proto_rawDescData
}

var file_services_agsale_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_services_agsale_proto_goTypes = []any{
	(*OrderDisplayIdRequest)(nil),   // 0: exmsg.services.OrderDisplayIdRequest
	(*CareReasonRequest)(nil),       // 1: exmsg.services.CareReasonRequest
	(*CareReasonResponse)(nil),      // 2: exmsg.services.CareReasonResponse
	(*LeadRequest)(nil),             // 3: exmsg.services.LeadRequest
	(*OrderRequest)(nil),            // 4: exmsg.services.OrderRequest
	(*OrderDisplayIdCount)(nil),     // 5: exmsg.services.OrderDisplayIdCount
	(*OrderResponse)(nil),           // 6: exmsg.services.OrderResponse
	(*LeadResponse)(nil),            // 7: exmsg.services.LeadResponse
	(*LandingPageRequest)(nil),      // 8: exmsg.services.LandingPageRequest
	(*LandingPageCompositeKey)(nil), // 9: exmsg.services.LandingPageCompositeKey
	(*LandingPageResponse)(nil),     // 10: exmsg.services.LandingPageResponse
	(*models.Query)(nil),            // 11: exmsg.models.Query
	(*models.CareReason)(nil),       // 12: exmsg.models.CareReason
	(*models.SQLResult)(nil),        // 13: exmsg.models.SQLResult
	(*models.Lead)(nil),             // 14: exmsg.models.Lead
	(*models.Order)(nil),            // 15: exmsg.models.Order
	(*models.LeadCare)(nil),         // 16: exmsg.models.LeadCare
	(*models.LeadCareItem)(nil),     // 17: exmsg.models.LeadCareItem
	(*models.OrderProduct)(nil),     // 18: exmsg.models.OrderProduct
	(*models.LandingPage)(nil),      // 19: exmsg.models.LandingPage
}
var file_services_agsale_proto_depIdxs = []int32{
	11, // 0: exmsg.services.CareReasonRequest.query:type_name -> exmsg.models.Query
	12, // 1: exmsg.services.CareReasonResponse.care_reasons:type_name -> exmsg.models.CareReason
	13, // 2: exmsg.services.CareReasonResponse.exec_result:type_name -> exmsg.models.SQLResult
	11, // 3: exmsg.services.LeadRequest.query:type_name -> exmsg.models.Query
	14, // 4: exmsg.services.LeadRequest.lead:type_name -> exmsg.models.Lead
	15, // 5: exmsg.services.LeadRequest.order:type_name -> exmsg.models.Order
	16, // 6: exmsg.services.LeadRequest.lead_care:type_name -> exmsg.models.LeadCare
	17, // 7: exmsg.services.LeadRequest.lead_care_item:type_name -> exmsg.models.LeadCareItem
	11, // 8: exmsg.services.OrderRequest.query:type_name -> exmsg.models.Query
	15, // 9: exmsg.services.OrderRequest.insert_orders:type_name -> exmsg.models.Order
	15, // 10: exmsg.services.OrderRequest.order:type_name -> exmsg.models.Order
	15, // 11: exmsg.services.OrderResponse.orders:type_name -> exmsg.models.Order
	13, // 12: exmsg.services.OrderResponse.exec_result:type_name -> exmsg.models.SQLResult
	18, // 13: exmsg.services.OrderResponse.order_products:type_name -> exmsg.models.OrderProduct
	5,  // 14: exmsg.services.OrderResponse.order_display_id_count:type_name -> exmsg.services.OrderDisplayIdCount
	14, // 15: exmsg.services.LeadResponse.leads:type_name -> exmsg.models.Lead
	15, // 16: exmsg.services.LeadResponse.order:type_name -> exmsg.models.Order
	13, // 17: exmsg.services.LeadResponse.exec_result:type_name -> exmsg.models.SQLResult
	11, // 18: exmsg.services.LandingPageRequest.query:type_name -> exmsg.models.Query
	9,  // 19: exmsg.services.LandingPageRequest.lp_composite_keys:type_name -> exmsg.services.LandingPageCompositeKey
	19, // 20: exmsg.services.LandingPageResponse.landing_pages:type_name -> exmsg.models.LandingPage
	13, // 21: exmsg.services.LandingPageResponse.exec_result:type_name -> exmsg.models.SQLResult
	3,  // 22: exmsg.services.AGSaleService.CreateLead:input_type -> exmsg.services.LeadRequest
	3,  // 23: exmsg.services.AGSaleService.GetLeads:input_type -> exmsg.services.LeadRequest
	4,  // 24: exmsg.services.AGSaleService.GetOrders:input_type -> exmsg.services.OrderRequest
	4,  // 25: exmsg.services.AGSaleService.CreateOrders:input_type -> exmsg.services.OrderRequest
	3,  // 26: exmsg.services.AGSaleService.UpdateLead:input_type -> exmsg.services.LeadRequest
	3,  // 27: exmsg.services.AGSaleService.CreateLeadCareAndItem:input_type -> exmsg.services.LeadRequest
	1,  // 28: exmsg.services.AGSaleService.GetCareReasons:input_type -> exmsg.services.CareReasonRequest
	4,  // 29: exmsg.services.AGSaleService.GetOrderProducts:input_type -> exmsg.services.OrderRequest
	8,  // 30: exmsg.services.AGSaleService.GetLandingPages:input_type -> exmsg.services.LandingPageRequest
	0,  // 31: exmsg.services.AGSaleService.GetOrderDisplayId:input_type -> exmsg.services.OrderDisplayIdRequest
	4,  // 32: exmsg.services.AGSaleService.UpdateOrder:input_type -> exmsg.services.OrderRequest
	4,  // 33: exmsg.services.AGSaleService.RemoveDuplicateOrder:input_type -> exmsg.services.OrderRequest
	3,  // 34: exmsg.services.AGSaleService.RemoveDuplicateLead:input_type -> exmsg.services.LeadRequest
	7,  // 35: exmsg.services.AGSaleService.CreateLead:output_type -> exmsg.services.LeadResponse
	7,  // 36: exmsg.services.AGSaleService.GetLeads:output_type -> exmsg.services.LeadResponse
	6,  // 37: exmsg.services.AGSaleService.GetOrders:output_type -> exmsg.services.OrderResponse
	6,  // 38: exmsg.services.AGSaleService.CreateOrders:output_type -> exmsg.services.OrderResponse
	7,  // 39: exmsg.services.AGSaleService.UpdateLead:output_type -> exmsg.services.LeadResponse
	7,  // 40: exmsg.services.AGSaleService.CreateLeadCareAndItem:output_type -> exmsg.services.LeadResponse
	2,  // 41: exmsg.services.AGSaleService.GetCareReasons:output_type -> exmsg.services.CareReasonResponse
	6,  // 42: exmsg.services.AGSaleService.GetOrderProducts:output_type -> exmsg.services.OrderResponse
	10, // 43: exmsg.services.AGSaleService.GetLandingPages:output_type -> exmsg.services.LandingPageResponse
	6,  // 44: exmsg.services.AGSaleService.GetOrderDisplayId:output_type -> exmsg.services.OrderResponse
	6,  // 45: exmsg.services.AGSaleService.UpdateOrder:output_type -> exmsg.services.OrderResponse
	6,  // 46: exmsg.services.AGSaleService.RemoveDuplicateOrder:output_type -> exmsg.services.OrderResponse
	7,  // 47: exmsg.services.AGSaleService.RemoveDuplicateLead:output_type -> exmsg.services.LeadResponse
	35, // [35:48] is the sub-list for method output_type
	22, // [22:35] is the sub-list for method input_type
	22, // [22:22] is the sub-list for extension type_name
	22, // [22:22] is the sub-list for extension extendee
	0,  // [0:22] is the sub-list for field type_name
}

func init() { file_services_agsale_proto_init() }
func file_services_agsale_proto_init() {
	if File_services_agsale_proto != nil {
		return
	}
	file_services_agsale_proto_msgTypes[8].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_services_agsale_proto_rawDesc), len(file_services_agsale_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_services_agsale_proto_goTypes,
		DependencyIndexes: file_services_agsale_proto_depIdxs,
		MessageInfos:      file_services_agsale_proto_msgTypes,
	}.Build()
	File_services_agsale_proto = out.File
	file_services_agsale_proto_goTypes = nil
	file_services_agsale_proto_depIdxs = nil
}
