package repository

import (
	"context"
	"os"
	"testing"
	"time"

	"github.com/jmoiron/sqlx"
	_ "github.com/lib/pq"
	"gitlab.com/a7923/athena-go/internal/adcrawl/domain"
)

func TestAdAccountBatchInsert(t *testing.T) {
	db, err := sqlx.ConnectContext(context.Background(), "postgres", os.Getenv("AD_DB_URL"))
	if err != nil {
		t.Fatal(err)
	}
	defer db.Close()
	postgresAdAccountRepository := NewPostgresAdAccountRepository(db)
	adAccounts := []domain.AdAccount{}
	for i := 0; i < 1000; i++ {
		adAccounts = append(adAccounts, domain.AdAccount{
			ID:                     int64(************** + i),
			Name:                   "test",
			Status:                 5,
			Currency:               "USD",
			TimezoneOffsetHoursUTC: 8,
			CountryID:              111,
		})
	}

	defer func() {
		for i := range adAccounts {
			_, err := db.ExecContext(context.Background(), "DELETE FROM fb.adaccounts WHERE id=$1", adAccounts[i].ID)
			if err != nil {
				t.Fatal(err)
			}
		}
	}()

	withElapsedTime(t, func() {
		t.Run("trivial", func(t *testing.T) {
			if err := postgresAdAccountRepository.BatchInsert(context.Background(), adAccounts, false); err != nil {
				t.Fatal(err)
			}
		})
	})

}

func TestAdAccountBatchInsertAfterDelete(t *testing.T) {
	db, err := sqlx.ConnectContext(context.Background(), "postgres", os.Getenv("AD_DB_URL"))
	if err != nil {
		t.Fatal(err)
	}
	defer db.Close()
	postgresAdAccountRepository := NewPostgresAdAccountRepository(db)
	adAccounts := []domain.AdAccount{
		{
			ID:                     int64(***************),
			Name:                   "Mạnh 5",
			Status:                 5,
			Currency:               "USD",
			TimezoneOffsetHoursUTC: 8,
			AnalyticAccountID:      ***************,
		},
		{
			ID:                     int64(***************),
			Name:                   "Mạnh 6",
			Status:                 1,
			TimezoneOffsetHoursUTC: 7,
			AnalyticAccountID:      ***************,
		},
	}

	// defer func() {
	// 	for i := range adAccounts {
	// 		_, err := pool.Exec(context.Background(), "UPDATE fb.adaccounts SET deleted_at = NOW() WHERE id = $1", adAccounts[i].ID)
	// 		if err != nil {
	// 			t.Fatal(err)
	// 		}
	// 	}
	// }()

	withElapsedTime(t, func() {
		t.Run("trivial", func(t *testing.T) {
			if err := postgresAdAccountRepository.BatchInsert(context.Background(), adAccounts, false); err != nil {
				t.Fatal(err)
			}
		})
	})
}

func TestGetAllAdAccountIDs(t *testing.T) {
	db, err := sqlx.ConnectContext(context.Background(), "postgres", os.Getenv("AD_DB_URL"))
	if err != nil {
		t.Fatal(err)
	}
	defer db.Close()
	postgresAdAccountRepository := NewPostgresAdAccountRepository(db)
	ids, err := postgresAdAccountRepository.GetAllAdAccountIDs(context.Background(), 122162427746102756)
	if err != nil {
		t.Fatal(err)
	}
	t.Logf("ids: %v", ids)
}

func withElapsedTime(t *testing.T, f func()) {
	start := time.Now()
	f()
	elapsed := time.Since(start)
	t.Logf("elapsed time: %s", elapsed)
}
