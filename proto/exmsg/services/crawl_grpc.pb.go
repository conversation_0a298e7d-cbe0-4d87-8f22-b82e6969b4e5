// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v3.5.1
// source: services/crawl.proto

package services

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	CrawlService_Crawl_FullMethodName         = "/exmsg.services.CrawlService/Crawl"
	CrawlService_AdvancedCrawl_FullMethodName = "/exmsg.services.CrawlService/AdvancedCrawl"
)

// CrawlServiceClient is the client API for CrawlService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CrawlServiceClient interface {
	Crawl(ctx context.Context, in *CrawlRequest, opts ...grpc.CallOption) (*CrawlResponse, error)
	AdvancedCrawl(ctx context.Context, in *AdvancedCrawlRequest, opts ...grpc.CallOption) (*AdvancedCrawlResponse, error)
}

type crawlServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewCrawlServiceClient(cc grpc.ClientConnInterface) CrawlServiceClient {
	return &crawlServiceClient{cc}
}

func (c *crawlServiceClient) Crawl(ctx context.Context, in *CrawlRequest, opts ...grpc.CallOption) (*CrawlResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CrawlResponse)
	err := c.cc.Invoke(ctx, CrawlService_Crawl_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *crawlServiceClient) AdvancedCrawl(ctx context.Context, in *AdvancedCrawlRequest, opts ...grpc.CallOption) (*AdvancedCrawlResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AdvancedCrawlResponse)
	err := c.cc.Invoke(ctx, CrawlService_AdvancedCrawl_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CrawlServiceServer is the server API for CrawlService service.
// All implementations must embed UnimplementedCrawlServiceServer
// for forward compatibility.
type CrawlServiceServer interface {
	Crawl(context.Context, *CrawlRequest) (*CrawlResponse, error)
	AdvancedCrawl(context.Context, *AdvancedCrawlRequest) (*AdvancedCrawlResponse, error)
	mustEmbedUnimplementedCrawlServiceServer()
}

// UnimplementedCrawlServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedCrawlServiceServer struct{}

func (UnimplementedCrawlServiceServer) Crawl(context.Context, *CrawlRequest) (*CrawlResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Crawl not implemented")
}
func (UnimplementedCrawlServiceServer) AdvancedCrawl(context.Context, *AdvancedCrawlRequest) (*AdvancedCrawlResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AdvancedCrawl not implemented")
}
func (UnimplementedCrawlServiceServer) mustEmbedUnimplementedCrawlServiceServer() {}
func (UnimplementedCrawlServiceServer) testEmbeddedByValue()                      {}

// UnsafeCrawlServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CrawlServiceServer will
// result in compilation errors.
type UnsafeCrawlServiceServer interface {
	mustEmbedUnimplementedCrawlServiceServer()
}

func RegisterCrawlServiceServer(s grpc.ServiceRegistrar, srv CrawlServiceServer) {
	// If the following call pancis, it indicates UnimplementedCrawlServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&CrawlService_ServiceDesc, srv)
}

func _CrawlService_Crawl_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CrawlRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CrawlServiceServer).Crawl(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CrawlService_Crawl_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CrawlServiceServer).Crawl(ctx, req.(*CrawlRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CrawlService_AdvancedCrawl_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AdvancedCrawlRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CrawlServiceServer).AdvancedCrawl(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CrawlService_AdvancedCrawl_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CrawlServiceServer).AdvancedCrawl(ctx, req.(*AdvancedCrawlRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// CrawlService_ServiceDesc is the grpc.ServiceDesc for CrawlService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CrawlService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "exmsg.services.CrawlService",
	HandlerType: (*CrawlServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Crawl",
			Handler:    _CrawlService_Crawl_Handler,
		},
		{
			MethodName: "AdvancedCrawl",
			Handler:    _CrawlService_AdvancedCrawl_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "services/crawl.proto",
}
