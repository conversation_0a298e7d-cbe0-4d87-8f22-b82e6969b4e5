-- migrate:up
CREATE TABLE IF NOT EXISTS fb.spendings (
    ad_id BIGINT NOT NULL,
    -- ad_account_id TEXT NOT NULL,
    date_start DATE NOT NULL,
    hourly_stat INT NOT NULL,
    cost DECIMAL(16,2), -- this need to be multiplied with exchange rate
    raw_numeric DECIMAL(16,2) NOT NULL,
    exchange_rate DECIMAL(16,2),
    cpm DECIMAL(16,2) NOT NULL,
    cpc DECIMAL(16,2) NOT NULL,
    ctr DECIMAL(16,2) NOT NULL,
    clicks DECIMAL(16,2) NOT NULL,
    frequency DECIMAL(16,2) NOT NULL,
    p25 DECIMAL(16,2),
    p50 DECIMAL(16,2),
    p75 DECIMAL(16,2),
    p95 DECIMAL(16,2),
    video_average DECIMAL(16,2),
    impressions DECIMAL(16,2) NOT NULL,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ,
    deleted_at TIMESTAMPTZ,
    -- FOREIGN KEY (ad_account_id) REFERENCES fb.adaccounts(id) ON DELETE CASCADE,
    FOREIGN KEY (ad_id) REFERENCES fb.ads(id) ON DELETE CASCADE,
    PRIMARY KEY (ad_id, date_start, hourly_stat)
);

-- migrate:down
DROP TABLE IF EXISTS fb.spendings CASCADE;