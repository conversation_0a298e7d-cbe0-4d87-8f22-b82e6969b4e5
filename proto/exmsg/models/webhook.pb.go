// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v3.5.1
// source: webhook.proto

package models

import (
	_struct "github.com/golang/protobuf/ptypes/struct"
	timestamp "github.com/golang/protobuf/ptypes/timestamp"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type WebhookEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int64                `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Kind        string               `protobuf:"bytes,2,opt,name=kind,proto3" json:"kind,omitempty"`
	Payload     *_struct.Struct      `protobuf:"bytes,3,opt,name=payload,proto3" json:"payload,omitempty"`
	CreatedAt   *timestamp.Timestamp `protobuf:"bytes,4,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt   *timestamp.Timestamp `protobuf:"bytes,5,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	Meta        *_struct.Struct      `protobuf:"bytes,6,opt,name=meta,proto3" json:"meta,omitempty"`
	MessageUuid string               `protobuf:"bytes,7,opt,name=message_uuid,json=messageUuid,proto3" json:"message_uuid,omitempty"`
	IsRetry     bool                 `protobuf:"varint,8,opt,name=is_retry,json=isRetry,proto3" json:"is_retry,omitempty"`
}

func (x *WebhookEvent) Reset() {
	*x = WebhookEvent{}
	mi := &file_webhook_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WebhookEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebhookEvent) ProtoMessage() {}

func (x *WebhookEvent) ProtoReflect() protoreflect.Message {
	mi := &file_webhook_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebhookEvent.ProtoReflect.Descriptor instead.
func (*WebhookEvent) Descriptor() ([]byte, []int) {
	return file_webhook_proto_rawDescGZIP(), []int{0}
}

func (x *WebhookEvent) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *WebhookEvent) GetKind() string {
	if x != nil {
		return x.Kind
	}
	return ""
}

func (x *WebhookEvent) GetPayload() *_struct.Struct {
	if x != nil {
		return x.Payload
	}
	return nil
}

func (x *WebhookEvent) GetCreatedAt() *timestamp.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *WebhookEvent) GetUpdatedAt() *timestamp.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *WebhookEvent) GetMeta() *_struct.Struct {
	if x != nil {
		return x.Meta
	}
	return nil
}

func (x *WebhookEvent) GetMessageUuid() string {
	if x != nil {
		return x.MessageUuid
	}
	return ""
}

func (x *WebhookEvent) GetIsRetry() bool {
	if x != nil {
		return x.IsRetry
	}
	return false
}

type WebhookEvents struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Events []*WebhookEvent `protobuf:"bytes,1,rep,name=events,proto3" json:"events,omitempty"`
}

func (x *WebhookEvents) Reset() {
	*x = WebhookEvents{}
	mi := &file_webhook_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WebhookEvents) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebhookEvents) ProtoMessage() {}

func (x *WebhookEvents) ProtoReflect() protoreflect.Message {
	mi := &file_webhook_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebhookEvents.ProtoReflect.Descriptor instead.
func (*WebhookEvents) Descriptor() ([]byte, []int) {
	return file_webhook_proto_rawDescGZIP(), []int{1}
}

func (x *WebhookEvents) GetEvents() []*WebhookEvent {
	if x != nil {
		return x.Events
	}
	return nil
}

var File_webhook_proto protoreflect.FileDescriptor

var file_webhook_proto_rawDesc = []byte{
	0x0a, 0x0d, 0x77, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x0c, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x1a, 0x1f, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xc6, 0x02, 0x0a,
	0x0c, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6b, 0x69, 0x6e,
	0x64, 0x12, 0x31, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x07, 0x70, 0x61, 0x79,
	0x6c, 0x6f, 0x61, 0x64, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12,
	0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x2b, 0x0a, 0x04, 0x6d, 0x65,
	0x74, 0x61, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63,
	0x74, 0x52, 0x04, 0x6d, 0x65, 0x74, 0x61, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x5f, 0x75, 0x75, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x55, 0x75, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x73,
	0x5f, 0x72, 0x65, 0x74, 0x72, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x69, 0x73,
	0x52, 0x65, 0x74, 0x72, 0x79, 0x22, 0x43, 0x0a, 0x0d, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b,
	0x45, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x32, 0x0a, 0x06, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x45, 0x76, 0x65,
	0x6e, 0x74, 0x52, 0x06, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x42, 0x36, 0x5a, 0x34, 0x67, 0x69,
	0x74, 0x6c, 0x61, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x61, 0x37, 0x39, 0x32, 0x33, 0x2f, 0x61,
	0x74, 0x68, 0x65, 0x6e, 0x61, 0x2d, 0x67, 0x6f, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x65,
	0x78, 0x6d, 0x73, 0x67, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x3b, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_webhook_proto_rawDescOnce sync.Once
	file_webhook_proto_rawDescData = file_webhook_proto_rawDesc
)

func file_webhook_proto_rawDescGZIP() []byte {
	file_webhook_proto_rawDescOnce.Do(func() {
		file_webhook_proto_rawDescData = protoimpl.X.CompressGZIP(file_webhook_proto_rawDescData)
	})
	return file_webhook_proto_rawDescData
}

var file_webhook_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_webhook_proto_goTypes = []any{
	(*WebhookEvent)(nil),        // 0: exmsg.models.WebhookEvent
	(*WebhookEvents)(nil),       // 1: exmsg.models.WebhookEvents
	(*_struct.Struct)(nil),      // 2: google.protobuf.Struct
	(*timestamp.Timestamp)(nil), // 3: google.protobuf.Timestamp
}
var file_webhook_proto_depIdxs = []int32{
	2, // 0: exmsg.models.WebhookEvent.payload:type_name -> google.protobuf.Struct
	3, // 1: exmsg.models.WebhookEvent.created_at:type_name -> google.protobuf.Timestamp
	3, // 2: exmsg.models.WebhookEvent.updated_at:type_name -> google.protobuf.Timestamp
	2, // 3: exmsg.models.WebhookEvent.meta:type_name -> google.protobuf.Struct
	0, // 4: exmsg.models.WebhookEvents.events:type_name -> exmsg.models.WebhookEvent
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_webhook_proto_init() }
func file_webhook_proto_init() {
	if File_webhook_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_webhook_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_webhook_proto_goTypes,
		DependencyIndexes: file_webhook_proto_depIdxs,
		MessageInfos:      file_webhook_proto_msgTypes,
	}.Build()
	File_webhook_proto = out.File
	file_webhook_proto_rawDesc = nil
	file_webhook_proto_goTypes = nil
	file_webhook_proto_depIdxs = nil
}
