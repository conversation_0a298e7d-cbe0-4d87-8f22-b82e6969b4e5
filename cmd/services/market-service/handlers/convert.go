package handlers

import (
	"gitlab.com/a7923/athena-go/internal/adcrawl/domain"
	"gitlab.com/a7923/athena-go/proto/exmsg/models"
)

func convertAdAccountProtoToDomain(protoAdAccounts []*models.AdAccount) []domain.AdAccount {
	adAccounts := make([]domain.AdAccount, len(protoAdAccounts))
	for i := range protoAdAccounts {
		adAccounts[i] = domain.AdAccount{
			ID:                     protoAdAccounts[i].Id,
			Name:                   protoAdAccounts[i].Name,
			Status:                 domain.AdAccountStatusStrToInt(protoAdAccounts[i].Status),
			Currency:               protoAdAccounts[i].Currency,
			TimezoneOffsetHoursUTC: protoAdAccounts[i].TimezoneOffsetHoursUtc,
			AnalyticAccountID:      protoAdAccounts[i].AnalyticAccountId,
		}
		if protoAdAccounts[i].MarketerId != nil {
			adAccounts[i].MarketerID = *protoAdAccounts[i].MarketerId
		}
	}
	return adAccounts
}

func convertCampaignsProtoToDomain(protoCampaigns []*models.Campaign) []domain.Campaign {
	campaigns := make([]domain.Campaign, len(protoCampaigns))
	for i := range protoCampaigns {
		campaigns[i] = domain.Campaign{
			ID:              protoCampaigns[i].Id,
			Name:            protoCampaigns[i].Name,
			Status:          protoCampaigns[i].Status,
			EffectiveStatus: protoCampaigns[i].EffectiveStatus,
			AdAccountID:     protoCampaigns[i].AdaccountId,
		}
		if protoCampaigns[i].Objective != nil {
			campaigns[i].Objective = *protoCampaigns[i].Objective
		}
		if protoCampaigns[i].MarketerId != nil {
			campaigns[i].MarketerID = *protoCampaigns[i].MarketerId
		}
	}
	return campaigns
}

func convertAdsetsProtoToDomain(protoAdsets []*models.Adset) []domain.AdSet {
	adsets := make([]domain.AdSet, len(protoAdsets))
	for i := range protoAdsets {
		adsets[i] = domain.AdSet{
			ID:               protoAdsets[i].Id,
			Name:             protoAdsets[i].Name,
			Status:           protoAdsets[i].Status,
			EffectiveStatus:  protoAdsets[i].EffectiveStatus,
			OptimizationGoal: protoAdsets[i].OptimizationGoal,
			CampaignID:       protoAdsets[i].CampaignId,
			AdAccountID:      protoAdsets[i].AdaccountId,
		}
	}
	return adsets
}

func convertAdsProtoToDomain(protoAds []*models.Ad) []domain.Ad {
	ads := make([]domain.Ad, len(protoAds))
	for i := range protoAds {
		ads[i] = domain.Ad{
			ID:              protoAds[i].Id,
			Name:            protoAds[i].Name,
			Status:          protoAds[i].Status,
			EffectiveStatus: protoAds[i].EffectiveStatus,
			AdSetID:         protoAds[i].AdsetId,
			PageID:          protoAds[i].PageId,
			ObjectStoryID:   protoAds[i].ObjectStoryId,
			ObjectStorySpec: protoAds[i].ObjectStorySpec,
			ImageURL:        protoAds[i].ImageUrl,
		}
	}
	return ads
}

func convertSpendingsProtoToDomain(protoSpendings []*models.Spending) []domain.Spending {
	spendings := make([]domain.Spending, len(protoSpendings))
	for i := range protoSpendings {
		spendings[i] = domain.Spending{
			AdID:         protoSpendings[i].AdId,
			DateStart:    protoSpendings[i].DateStart,
			HourlyStat:   int(protoSpendings[i].HourlyStat),
			RawNumeric:   protoSpendings[i].RawNumeric,
			CPM:          protoSpendings[i].Cpm,
			CPC:          protoSpendings[i].Cpc,
			CTR:          protoSpendings[i].Ctr,
			Clicks:       protoSpendings[i].Clicks,
			Frequency:    protoSpendings[i].Frequency,
			P25:          protoSpendings[i].P25,
			P50:          protoSpendings[i].P50,
			P75:          protoSpendings[i].P75,
			P95:          protoSpendings[i].P95,
			VideoAverage: protoSpendings[i].VideoAverage,
			Impressions:  protoSpendings[i].Impressions,
		}
	}
	return spendings
}
