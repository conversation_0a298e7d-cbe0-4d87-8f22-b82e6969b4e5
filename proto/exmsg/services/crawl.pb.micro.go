// Code generated by protoc-gen-micro. DO NOT EDIT.
// source: services/crawl.proto

package services

import (
	fmt "fmt"
	_ "github.com/golang/protobuf/ptypes/struct"
	proto "google.golang.org/protobuf/proto"
	math "math"
)

import (
	context "context"
	client "go-micro.dev/v5/client"
	server "go-micro.dev/v5/server"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ client.Option
var _ server.Option

// Client API for CrawlService service

type CrawlService interface {
	Crawl(ctx context.Context, in *CrawlRequest, opts ...client.CallOption) (*CrawlResponse, error)
	AdvancedCrawl(ctx context.Context, in *AdvancedCrawlRequest, opts ...client.CallOption) (*AdvancedCrawlResponse, error)
}

type crawlService struct {
	c    client.Client
	name string
}

func NewCrawlService(name string, c client.Client) CrawlService {
	return &crawlService{
		c:    c,
		name: name,
	}
}

func (c *crawlService) Crawl(ctx context.Context, in *CrawlRequest, opts ...client.CallOption) (*CrawlResponse, error) {
	req := c.c.NewRequest(c.name, "CrawlService.Crawl", in)
	out := new(CrawlResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *crawlService) AdvancedCrawl(ctx context.Context, in *AdvancedCrawlRequest, opts ...client.CallOption) (*AdvancedCrawlResponse, error) {
	req := c.c.NewRequest(c.name, "CrawlService.AdvancedCrawl", in)
	out := new(AdvancedCrawlResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for CrawlService service

type CrawlServiceHandler interface {
	Crawl(context.Context, *CrawlRequest, *CrawlResponse) error
	AdvancedCrawl(context.Context, *AdvancedCrawlRequest, *AdvancedCrawlResponse) error
}

func RegisterCrawlServiceHandler(s server.Server, hdlr CrawlServiceHandler, opts ...server.HandlerOption) error {
	type crawlService interface {
		Crawl(ctx context.Context, in *CrawlRequest, out *CrawlResponse) error
		AdvancedCrawl(ctx context.Context, in *AdvancedCrawlRequest, out *AdvancedCrawlResponse) error
	}
	type CrawlService struct {
		crawlService
	}
	h := &crawlServiceHandler{hdlr}
	return s.Handle(s.NewHandler(&CrawlService{h}, opts...))
}

type crawlServiceHandler struct {
	CrawlServiceHandler
}

func (h *crawlServiceHandler) Crawl(ctx context.Context, in *CrawlRequest, out *CrawlResponse) error {
	return h.CrawlServiceHandler.Crawl(ctx, in, out)
}

func (h *crawlServiceHandler) AdvancedCrawl(ctx context.Context, in *AdvancedCrawlRequest, out *AdvancedCrawlResponse) error {
	return h.CrawlServiceHandler.AdvancedCrawl(ctx, in, out)
}
