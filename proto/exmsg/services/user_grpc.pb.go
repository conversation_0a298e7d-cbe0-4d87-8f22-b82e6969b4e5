// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v3.12.4
// source: services/user.proto

package services

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	UserService_GetUserByID_FullMethodName       = "/exmsg.services.UserService/GetUserByID"
	UserService_GetProfiles_FullMethodName       = "/exmsg.services.UserService/GetProfiles"
	UserService_GetDepartments_FullMethodName    = "/exmsg.services.UserService/GetDepartments"
	UserService_GetRoles_FullMethodName          = "/exmsg.services.UserService/GetRoles"
	UserService_GetDataSetScopes_FullMethodName  = "/exmsg.services.UserService/GetDataSetScopes"
	UserService_GetProjects_FullMethodName       = "/exmsg.services.UserService/GetProjects"
	UserService_GetUsersByOptions_FullMethodName = "/exmsg.services.UserService/GetUsersByOptions"
	UserService_GetUserPermission_FullMethodName = "/exmsg.services.UserService/GetUserPermission"
	UserService_GetUserProfile_FullMethodName    = "/exmsg.services.UserService/GetUserProfile"
)

// UserServiceClient is the client API for UserService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type UserServiceClient interface {
	GetUserByID(ctx context.Context, in *UserRequest, opts ...grpc.CallOption) (*UserResponse, error)
	GetProfiles(ctx context.Context, in *ProfileRequest, opts ...grpc.CallOption) (*ProfileResponse, error)
	GetDepartments(ctx context.Context, in *DepartmentRequest, opts ...grpc.CallOption) (*DepartmentResponse, error)
	GetRoles(ctx context.Context, in *RoleRequest, opts ...grpc.CallOption) (*RoleResponse, error)
	GetDataSetScopes(ctx context.Context, in *DataSetScopeRequest, opts ...grpc.CallOption) (*DataSetScopeResponse, error)
	GetProjects(ctx context.Context, in *ProjectRequest, opts ...grpc.CallOption) (*ProjectResponse, error)
	GetUsersByOptions(ctx context.Context, in *GetUsersByOptionsRequest, opts ...grpc.CallOption) (*GetUsersByOptionsResponse, error)
	GetUserPermission(ctx context.Context, in *GetUserPermissionRequest, opts ...grpc.CallOption) (*GetUserPermissionResponse, error)
	GetUserProfile(ctx context.Context, in *GetUserPermissionRequest, opts ...grpc.CallOption) (*GetUserProfileResponse, error)
}

type userServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewUserServiceClient(cc grpc.ClientConnInterface) UserServiceClient {
	return &userServiceClient{cc}
}

func (c *userServiceClient) GetUserByID(ctx context.Context, in *UserRequest, opts ...grpc.CallOption) (*UserResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UserResponse)
	err := c.cc.Invoke(ctx, UserService_GetUserByID_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) GetProfiles(ctx context.Context, in *ProfileRequest, opts ...grpc.CallOption) (*ProfileResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ProfileResponse)
	err := c.cc.Invoke(ctx, UserService_GetProfiles_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) GetDepartments(ctx context.Context, in *DepartmentRequest, opts ...grpc.CallOption) (*DepartmentResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DepartmentResponse)
	err := c.cc.Invoke(ctx, UserService_GetDepartments_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) GetRoles(ctx context.Context, in *RoleRequest, opts ...grpc.CallOption) (*RoleResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RoleResponse)
	err := c.cc.Invoke(ctx, UserService_GetRoles_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) GetDataSetScopes(ctx context.Context, in *DataSetScopeRequest, opts ...grpc.CallOption) (*DataSetScopeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DataSetScopeResponse)
	err := c.cc.Invoke(ctx, UserService_GetDataSetScopes_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) GetProjects(ctx context.Context, in *ProjectRequest, opts ...grpc.CallOption) (*ProjectResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ProjectResponse)
	err := c.cc.Invoke(ctx, UserService_GetProjects_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) GetUsersByOptions(ctx context.Context, in *GetUsersByOptionsRequest, opts ...grpc.CallOption) (*GetUsersByOptionsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetUsersByOptionsResponse)
	err := c.cc.Invoke(ctx, UserService_GetUsersByOptions_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) GetUserPermission(ctx context.Context, in *GetUserPermissionRequest, opts ...grpc.CallOption) (*GetUserPermissionResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetUserPermissionResponse)
	err := c.cc.Invoke(ctx, UserService_GetUserPermission_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) GetUserProfile(ctx context.Context, in *GetUserPermissionRequest, opts ...grpc.CallOption) (*GetUserProfileResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetUserProfileResponse)
	err := c.cc.Invoke(ctx, UserService_GetUserProfile_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UserServiceServer is the server API for UserService service.
// All implementations must embed UnimplementedUserServiceServer
// for forward compatibility.
type UserServiceServer interface {
	GetUserByID(context.Context, *UserRequest) (*UserResponse, error)
	GetProfiles(context.Context, *ProfileRequest) (*ProfileResponse, error)
	GetDepartments(context.Context, *DepartmentRequest) (*DepartmentResponse, error)
	GetRoles(context.Context, *RoleRequest) (*RoleResponse, error)
	GetDataSetScopes(context.Context, *DataSetScopeRequest) (*DataSetScopeResponse, error)
	GetProjects(context.Context, *ProjectRequest) (*ProjectResponse, error)
	GetUsersByOptions(context.Context, *GetUsersByOptionsRequest) (*GetUsersByOptionsResponse, error)
	GetUserPermission(context.Context, *GetUserPermissionRequest) (*GetUserPermissionResponse, error)
	GetUserProfile(context.Context, *GetUserPermissionRequest) (*GetUserProfileResponse, error)
	mustEmbedUnimplementedUserServiceServer()
}

// UnimplementedUserServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedUserServiceServer struct{}

func (UnimplementedUserServiceServer) GetUserByID(context.Context, *UserRequest) (*UserResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserByID not implemented")
}
func (UnimplementedUserServiceServer) GetProfiles(context.Context, *ProfileRequest) (*ProfileResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetProfiles not implemented")
}
func (UnimplementedUserServiceServer) GetDepartments(context.Context, *DepartmentRequest) (*DepartmentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDepartments not implemented")
}
func (UnimplementedUserServiceServer) GetRoles(context.Context, *RoleRequest) (*RoleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRoles not implemented")
}
func (UnimplementedUserServiceServer) GetDataSetScopes(context.Context, *DataSetScopeRequest) (*DataSetScopeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDataSetScopes not implemented")
}
func (UnimplementedUserServiceServer) GetProjects(context.Context, *ProjectRequest) (*ProjectResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetProjects not implemented")
}
func (UnimplementedUserServiceServer) GetUsersByOptions(context.Context, *GetUsersByOptionsRequest) (*GetUsersByOptionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUsersByOptions not implemented")
}
func (UnimplementedUserServiceServer) GetUserPermission(context.Context, *GetUserPermissionRequest) (*GetUserPermissionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserPermission not implemented")
}
func (UnimplementedUserServiceServer) GetUserProfile(context.Context, *GetUserPermissionRequest) (*GetUserProfileResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserProfile not implemented")
}
func (UnimplementedUserServiceServer) mustEmbedUnimplementedUserServiceServer() {}
func (UnimplementedUserServiceServer) testEmbeddedByValue()                     {}

// UnsafeUserServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to UserServiceServer will
// result in compilation errors.
type UnsafeUserServiceServer interface {
	mustEmbedUnimplementedUserServiceServer()
}

func RegisterUserServiceServer(s grpc.ServiceRegistrar, srv UserServiceServer) {
	// If the following call pancis, it indicates UnimplementedUserServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&UserService_ServiceDesc, srv)
}

func _UserService_GetUserByID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).GetUserByID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_GetUserByID_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).GetUserByID(ctx, req.(*UserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_GetProfiles_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProfileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).GetProfiles(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_GetProfiles_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).GetProfiles(ctx, req.(*ProfileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_GetDepartments_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DepartmentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).GetDepartments(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_GetDepartments_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).GetDepartments(ctx, req.(*DepartmentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_GetRoles_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RoleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).GetRoles(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_GetRoles_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).GetRoles(ctx, req.(*RoleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_GetDataSetScopes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DataSetScopeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).GetDataSetScopes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_GetDataSetScopes_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).GetDataSetScopes(ctx, req.(*DataSetScopeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_GetProjects_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProjectRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).GetProjects(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_GetProjects_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).GetProjects(ctx, req.(*ProjectRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_GetUsersByOptions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUsersByOptionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).GetUsersByOptions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_GetUsersByOptions_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).GetUsersByOptions(ctx, req.(*GetUsersByOptionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_GetUserPermission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserPermissionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).GetUserPermission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_GetUserPermission_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).GetUserPermission(ctx, req.(*GetUserPermissionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_GetUserProfile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserPermissionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).GetUserProfile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_GetUserProfile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).GetUserProfile(ctx, req.(*GetUserPermissionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// UserService_ServiceDesc is the grpc.ServiceDesc for UserService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var UserService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "exmsg.services.UserService",
	HandlerType: (*UserServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetUserByID",
			Handler:    _UserService_GetUserByID_Handler,
		},
		{
			MethodName: "GetProfiles",
			Handler:    _UserService_GetProfiles_Handler,
		},
		{
			MethodName: "GetDepartments",
			Handler:    _UserService_GetDepartments_Handler,
		},
		{
			MethodName: "GetRoles",
			Handler:    _UserService_GetRoles_Handler,
		},
		{
			MethodName: "GetDataSetScopes",
			Handler:    _UserService_GetDataSetScopes_Handler,
		},
		{
			MethodName: "GetProjects",
			Handler:    _UserService_GetProjects_Handler,
		},
		{
			MethodName: "GetUsersByOptions",
			Handler:    _UserService_GetUsersByOptions_Handler,
		},
		{
			MethodName: "GetUserPermission",
			Handler:    _UserService_GetUserPermission_Handler,
		},
		{
			MethodName: "GetUserProfile",
			Handler:    _UserService_GetUserProfile_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "services/user.proto",
}
