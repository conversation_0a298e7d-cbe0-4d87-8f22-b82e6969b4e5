package handlers

import (
	"context"
	"github.com/Masterminds/squirrel"
	tables2 "gitlab.com/a7923/athena-go/cmd/services/marketing-service/tables"
	"gitlab.com/a7923/athena-go/pkg/dbtool"
	"gitlab.com/a7923/athena-go/pkg/logger"
	"gitlab.com/a7923/athena-go/proto/exmsg/models"
	"gitlab.com/a7923/athena-go/proto/exmsg/services"
)

type MarketingHandler struct {
	db *dbtool.ConnectionManager
}

//func (e *MarketingHandler) CreteAdsBiz(ctx context.Context, request *services.MarketingRequest, response *services.MarketingResponse) error {
//	//TODO implement me
//	panic("implement me")
//
//}

func (e *MarketingHandler) CreateAdsBiz(ctx context.Context, request *services.MarketingRequest, response *services.MarketingResponse) error {
	sqlTool := dbtool.NewInsert(ctx, e.db.GetConnection(), tables2.GetAdsBizTable(), &models.AdBiz{})
	qb := squirrel.
		Insert(sqlTool.GetTable("")).
		Columns(sqlTool.GetQueryColumnList("")...)

	if len(request.ListAdsbiz) > 0 {
		for _, m := range request.ListAdsbiz {
			qb = qb.Values(sqlTool.GetFilledValues(m)...)
		}
	} else {
		qb = qb.Values(sqlTool.GetFilledValues(request.Adsbiz)...)
	}

	qb = qb.Suffix("ON CONFLICT DO NOTHING")

	res, err := sqlTool.Insert(ctx, qb)
	if err != nil {
		logger.AthenaLogger.Errorw("Failed to insert orders", "error", err)
		return err
	}

	response.ExecResult = res
	return nil
}

func NewMarketingHandler(db *dbtool.ConnectionManager) *MarketingHandler {
	return &MarketingHandler{db: db}
}
