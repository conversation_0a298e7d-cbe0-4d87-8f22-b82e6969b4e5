package domain

import (
	"context"
	"time"

	"github.com/spf13/cast"
)

type Ad struct {
	ID              int64     `json:"id"`
	Name            string    `json:"name"`
	Status          string    `json:"status"`
	EffectiveStatus string    `json:"effective_status"`
	PageID          string    `json:"page_id"`
	ObjectStoryID   *string   `json:"object_story_id"`
	PostID          *string   `json:"post_id"`
	ObjectStorySpec []byte    `json:"object_story_spec"`
	ImageURL        *string   `json:"image_url"`
	CreatedAt       time.Time `json:"created_at"`
	AdSetID         int64     `json:"adset_id"`
}

type AdRepository interface {
	BatchInsert(ctx context.Context, ads []Ad) error
}

type DummyAd struct {
	ID              string    `json:"id"`
	Name            string    `json:"name"`
	Status          string    `json:"status"`
	EffectiveStatus string    `json:"effective_status"`
	PageID          string    `json:"page_id"`
	ObjectStoryID   *string   `json:"object_story_id"`
	ObjectStorySpec []byte    `json:"object_story_spec"`
	ImageURL        *string   `json:"image_url"`
	CreatedAt       time.Time `json:"created_at"`
	AdSetID         string    `json:"adset_id"`
}

func (a DummyAd) ToAd() Ad {
	return Ad{
		ID:              cast.ToInt64(a.ID),
		Name:            a.Name,
		Status:          a.Status,
		EffectiveStatus: a.EffectiveStatus,
		PageID:          a.PageID,
		ObjectStoryID:   a.ObjectStoryID,
		ObjectStorySpec: a.ObjectStorySpec,
		ImageURL:        a.ImageURL,
		CreatedAt:       a.CreatedAt,
		AdSetID:         cast.ToInt64(a.AdSetID),
	}
}
