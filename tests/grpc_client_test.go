package tests

import (
	"context"
	transhttp "gitlab.com/a7923/athena-go/internal"
	"gitlab.com/a7923/athena-go/proto/exmsg/services"
	"go-micro.dev/v5/client"
	"log"
	"testing"
	"time"
)

func TestRequestGRPCServer(t *testing.T) {
	st := time.Now()
	cl := transhttp.CreateNewUserServiceClient(nil)
	d, e := cl.GetUserByID(context.Background(), &services.UserRequest{
		UserId: 388,
	}, client.WithRequestTimeout(51*time.Second))

	if e != nil {
		log.Fatal(e)
	}
	log.Println(time.Since(st), "Get user by ID: ", d)
}
