package token

import (
	"context"
	"github.com/golang-jwt/jwt/v5"
	"gitlab.com/a7923/athena-go/proto/exmsg/models"
)

type Token struct {
	*jwt.Token
}

type Processor interface {
	GetToken(ctx context.Context, tokenString string) (token *Token, err error)
	ExtractMetadata(token *Token) map[string]string
	CheckPermissions(token *Token, requirePermissions map[int64]int64) bool
	GenerateToken(ctx context.Context, user *models.User) (string, error)
}
