package repository

import (
	"context"
	"os"
	"testing"

	"github.com/jmoiron/sqlx"
	_ "github.com/lib/pq"
	"gitlab.com/a7923/athena-go/internal/adcrawl/domain"
)

func TestSpendingBatchInsert(t *testing.T) {
	db, err := sqlx.ConnectContext(context.Background(), "postgres", os.Getenv("AD_DB_URL"))
	if err != nil {
		t.<PERSON>al(err)
	}
	defer db.Close()
	postgresSpendingRepository := NewPostgresSpendingRepository(db)
	spendings := []domain.Spending{}
	for i := 1; i < 24; i++ {
		spendings = append(spendings, domain.Spending{
			AdAccountID:  int64(***************),
			AdID:         int64(120218305031760081),
			CampaignID:   int64(120218305031760081),
			AdsetID:      int64(120218305031760081),
			DateStart:    "2024-07-26",
			HourlyStat:   i,
			RawNumeric:   538.00,
			CPM:          8242.86,
			CPC:          205.00,
			CTR:          11.9,
			Clicks:       35.00,
			Frequency:    0.5,
			P25:          12.0,
			P50:          15.0,
			P75:          22.5,
			P95:          30.0,
			VideoAverage: 25,
			Impressions:  16.00,
		})
	}

	if err := postgresSpendingRepository.BatchInsert(context.Background(), spendings); err != nil {
		t.Fatal(err)
	}

	defer func() {
		for i := range spendings {
			_, err := db.ExecContext(context.Background(), "DELETE FROM fb.spendings WHERE ad_id=$1 AND date_start=$2 AND hourly_stat=$3", spendings[i].AdID, spendings[i].DateStart, spendings[i].HourlyStat)
			if err != nil {
				t.Fatal(err)
			}
		}
	}()
}
