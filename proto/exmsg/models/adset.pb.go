// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: proto/models/adset.proto

package models

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Adset struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Id               int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name             string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Status           string                 `protobuf:"bytes,3,opt,name=status,proto3" json:"status,omitempty"`
	EffectiveStatus  string                 `protobuf:"bytes,4,opt,name=effective_status,json=effectiveStatus,proto3" json:"effective_status,omitempty"`
	OptimizationGoal string                 `protobuf:"bytes,5,opt,name=optimization_goal,json=optimizationGoal,proto3" json:"optimization_goal,omitempty"`
	AdaccountId      int64                  `protobuf:"varint,6,opt,name=adaccount_id,json=adaccountId,proto3" json:"adaccount_id,omitempty"`
	CampaignId       int64                  `protobuf:"varint,7,opt,name=campaign_id,json=campaignId,proto3" json:"campaign_id,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *Adset) Reset() {
	*x = Adset{}
	mi := &file_proto_models_adset_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Adset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Adset) ProtoMessage() {}

func (x *Adset) ProtoReflect() protoreflect.Message {
	mi := &file_proto_models_adset_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Adset.ProtoReflect.Descriptor instead.
func (*Adset) Descriptor() ([]byte, []int) {
	return file_proto_models_adset_proto_rawDescGZIP(), []int{0}
}

func (x *Adset) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Adset) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Adset) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *Adset) GetEffectiveStatus() string {
	if x != nil {
		return x.EffectiveStatus
	}
	return ""
}

func (x *Adset) GetOptimizationGoal() string {
	if x != nil {
		return x.OptimizationGoal
	}
	return ""
}

func (x *Adset) GetAdaccountId() int64 {
	if x != nil {
		return x.AdaccountId
	}
	return 0
}

func (x *Adset) GetCampaignId() int64 {
	if x != nil {
		return x.CampaignId
	}
	return 0
}

var File_proto_models_adset_proto protoreflect.FileDescriptor

var file_proto_models_adset_proto_rawDesc = string([]byte{
	0x0a, 0x18, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61,
	0x64, 0x73, 0x65, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c, 0x65, 0x78, 0x6d, 0x73,
	0x67, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x22, 0xdf, 0x01, 0x0a, 0x05, 0x41, 0x64, 0x73,
	0x65, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x29,
	0x0a, 0x10, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2b, 0x0a, 0x11, 0x6f, 0x70, 0x74,
	0x69, 0x6d, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x67, 0x6f, 0x61, 0x6c, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x6f, 0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x47, 0x6f, 0x61, 0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x64, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x61, 0x64,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61, 0x6d,
	0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a,
	0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x49, 0x64, 0x42, 0x36, 0x5a, 0x34, 0x67, 0x69,
	0x74, 0x6c, 0x61, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x61, 0x37, 0x39, 0x32, 0x33, 0x2f, 0x61,
	0x74, 0x68, 0x65, 0x6e, 0x61, 0x2d, 0x67, 0x6f, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x65,
	0x78, 0x6d, 0x73, 0x67, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x3b, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_proto_models_adset_proto_rawDescOnce sync.Once
	file_proto_models_adset_proto_rawDescData []byte
)

func file_proto_models_adset_proto_rawDescGZIP() []byte {
	file_proto_models_adset_proto_rawDescOnce.Do(func() {
		file_proto_models_adset_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proto_models_adset_proto_rawDesc), len(file_proto_models_adset_proto_rawDesc)))
	})
	return file_proto_models_adset_proto_rawDescData
}

var file_proto_models_adset_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_proto_models_adset_proto_goTypes = []any{
	(*Adset)(nil), // 0: exmsg.models.Adset
}
var file_proto_models_adset_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_proto_models_adset_proto_init() }
func file_proto_models_adset_proto_init() {
	if File_proto_models_adset_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proto_models_adset_proto_rawDesc), len(file_proto_models_adset_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_proto_models_adset_proto_goTypes,
		DependencyIndexes: file_proto_models_adset_proto_depIdxs,
		MessageInfos:      file_proto_models_adset_proto_msgTypes,
	}.Build()
	File_proto_models_adset_proto = out.File
	file_proto_models_adset_proto_goTypes = nil
	file_proto_models_adset_proto_depIdxs = nil
}
