package repository

import (
	"context"
	"errors"

	"github.com/Masterminds/squirrel"
	"github.com/jmoiron/sqlx"
	"gitlab.com/a7923/athena-go/internal/adcrawl/domain"
	"gitlab.com/a7923/athena-go/pkg/logger"
)

var _ domain.TokenRepository = (*PostgresTokenRepository)(nil)

type PostgresTokenRepository struct {
	db *sqlx.DB
	qb squirrel.StatementBuilderType
}

func NewPostgresTokenRepository(db *sqlx.DB) *PostgresTokenRepository {
	return &PostgresTokenRepository{
		db: db,
		qb: squirrel.StatementBuilder.PlaceholderFormat(squirrel.Dollar),
	}
}

func (pr *PostgresTokenRepository) Get(ctx context.Context, objectType string, id int64) (string, error) {
	var sb squirrel.SelectBuilder
	switch objectType {
	case "AnalyticAccount":
		sb = pr.qb.Select("token").
			From("fb.analytic_accounts").
			Where("id = ?", id)
	case "AdAccount":
		sb = pr.qb.Select("aa.token").
			From("fb.analytic_accounts AS aa").
			Join("fb.adaccounts AS a ON a.analytic_account_id = aa.id").
			Where("a.id = ?", id)
	case "Campaign":
		sb = pr.qb.Select("aa.token").
			From("fb.analytic_accounts AS aa").
			Join("fb.adaccounts AS a ON a.analytic_account_id = aa.id").
			Join("fb.campaigns AS c ON c.adaccount_id = a.id").
			Where("c.id = ?", id)
	case "AdSet":
		sb = pr.qb.Select("aa.token").
			From("fb.analytic_accounts AS aa").
			Join("fb.adaccounts AS a ON a.analytic_account_id = aa.id").
			Join("fb.campaigns AS c ON c.adaccount_id = a.id").
			Join("fb.adsets AS s ON s.campaign_id = c.id").
			Where("s.id = ?", id)

	case "Ad":
		sb = pr.qb.Select("aa.token").
			From("fb.analytic_accounts AS aa").
			Join("fb.adaccounts AS a ON a.analytic_account_id = aa.id").
			Join("fb.campaigns AS c ON c.adaccount_id = a.id").
			Join("fb.adsets AS s ON s.campaign_id = c.id").
			Join("fb.ads AS ad ON ad.adset_id = s.id").
			Where("ad.id = ?", id)
	case "Spending":
		sb = pr.qb.Select("aa.token").
			From("fb.analytic_accounts AS aa").
			Join("fb.adaccounts AS a ON a.analytic_account_id = aa.id").
			Join("fb.campaigns AS c ON c.adaccount_id = a.id").
			Join("fb.adsets AS s ON s.campaign_id = c.id").
			Join("fb.ads AS ad ON ad.adset_id = s.id").
			Where("ad.id = ?", id)
	default:
		return "", errors.New("invalid object type")
	}

	stmt, args, err := sb.ToSql()
	if err != nil {
		return "", err
	}

	var token string
	if err := pr.db.GetContext(ctx, &token, stmt, args...); err != nil {
		logger.AthenaLogger.Errorf("failed to get token: %v", err)
		return "", err
	}

	return token, nil
}

func (pr *PostgresTokenRepository) MarkInvalidToken(ctx context.Context, token string) error {
	qb := pr.qb.Update("fb.analytic_accounts").
		Set("available", false).
		Where(squirrel.Eq{"token": token})
	stmt, args, err := qb.ToSql()
	if err != nil {
		return err
	}
	if _, err := pr.db.ExecContext(ctx, stmt, args...); err != nil {
		return err
	}
	return nil
}

func (pr *PostgresTokenRepository) ListActiveTokens(ctx context.Context) ([]domain.ActiveToken, error) {
	qb := pr.qb.Select("id", "token").
		From("fb.analytic_accounts").
		Where(squirrel.And{
			squirrel.Eq{"deleted_at": nil},
			squirrel.NotEq{"company_id": nil},
			squirrel.NotEq{"marketer_id": nil},
			squirrel.Eq{"available": true},
		})
	stmt, args, err := qb.ToSql()
	if err != nil {
		return nil, err
	}
	var activeTokens []domain.ActiveToken
	// if err := pr.db.SelectContext(ctx, &activeTokens, stmt, args...); err != nil {
	// 	logger.AthenaLogger.Errorf("failed to list active tokens: %v", err)
	// 	return nil, err
	// }
	rows, err := pr.db.QueryxContext(ctx, stmt, args...)
	if err != nil {
		return nil, err
	}
	for rows.Next() {
		var activeToken domain.ActiveToken
		if err := rows.Scan(&activeToken.AnalyticAccountID, &activeToken.Token); err != nil {
			return nil, err
		}
		activeTokens = append(activeTokens, activeToken)
	}

	return activeTokens, nil
}
