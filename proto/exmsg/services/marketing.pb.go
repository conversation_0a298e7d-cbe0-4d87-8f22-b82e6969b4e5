// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v3.5.1
// source: services/marketing.proto

package services

import (
	models "gitlab.com/a7923/athena-go/proto/exmsg/models"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MarketingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Query      *models.Query   `protobuf:"bytes,1,opt,name=query,proto3" json:"query,omitempty"`
	Id         int64           `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	Ids        []int64         `protobuf:"varint,3,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	Adsbiz     *models.AdBiz   `protobuf:"bytes,4,opt,name=adsbiz,proto3" json:"adsbiz,omitempty"`
	ListAdsbiz []*models.AdBiz `protobuf:"bytes,5,rep,name=list_adsbiz,json=listAdsbiz,proto3" json:"list_adsbiz,omitempty"`
}

func (x *MarketingRequest) Reset() {
	*x = MarketingRequest{}
	mi := &file_services_marketing_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MarketingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarketingRequest) ProtoMessage() {}

func (x *MarketingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_marketing_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarketingRequest.ProtoReflect.Descriptor instead.
func (*MarketingRequest) Descriptor() ([]byte, []int) {
	return file_services_marketing_proto_rawDescGZIP(), []int{0}
}

func (x *MarketingRequest) GetQuery() *models.Query {
	if x != nil {
		return x.Query
	}
	return nil
}

func (x *MarketingRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *MarketingRequest) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *MarketingRequest) GetAdsbiz() *models.AdBiz {
	if x != nil {
		return x.Adsbiz
	}
	return nil
}

func (x *MarketingRequest) GetListAdsbiz() []*models.AdBiz {
	if x != nil {
		return x.ListAdsbiz
	}
	return nil
}

type MarketingResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Marketing  []*models.Marketing `protobuf:"bytes,1,rep,name=marketing,proto3" json:"marketing,omitempty"`
	ExecResult *models.SQLResult   `protobuf:"bytes,2,opt,name=exec_result,json=execResult,proto3" json:"exec_result,omitempty"`
}

func (x *MarketingResponse) Reset() {
	*x = MarketingResponse{}
	mi := &file_services_marketing_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MarketingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarketingResponse) ProtoMessage() {}

func (x *MarketingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_marketing_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarketingResponse.ProtoReflect.Descriptor instead.
func (*MarketingResponse) Descriptor() ([]byte, []int) {
	return file_services_marketing_proto_rawDescGZIP(), []int{1}
}

func (x *MarketingResponse) GetMarketing() []*models.Marketing {
	if x != nil {
		return x.Marketing
	}
	return nil
}

func (x *MarketingResponse) GetExecResult() *models.SQLResult {
	if x != nil {
		return x.ExecResult
	}
	return nil
}

var File_services_marketing_proto protoreflect.FileDescriptor

var file_services_marketing_proto_rawDesc = []byte{
	0x0a, 0x18, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x6d, 0x61, 0x72, 0x6b, 0x65,
	0x74, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x65, 0x78, 0x6d, 0x73,
	0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x1a, 0x16, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x13, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xc2, 0x01, 0x0a, 0x10, 0x4d, 0x61, 0x72, 0x6b,
	0x65, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x29, 0x0a, 0x05,
	0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x65, 0x78,
	0x6d, 0x73, 0x67, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x52, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x03, 0x52, 0x03, 0x69, 0x64, 0x73, 0x12, 0x2b, 0x0a, 0x06, 0x61, 0x64, 0x73,
	0x62, 0x69, 0x7a, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x65, 0x78, 0x6d, 0x73,
	0x67, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x41, 0x64, 0x42, 0x69, 0x7a, 0x52, 0x06,
	0x61, 0x64, 0x73, 0x62, 0x69, 0x7a, 0x12, 0x34, 0x0a, 0x0b, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x61,
	0x64, 0x73, 0x62, 0x69, 0x7a, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x65, 0x78,
	0x6d, 0x73, 0x67, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x41, 0x64, 0x42, 0x69, 0x7a,
	0x52, 0x0a, 0x6c, 0x69, 0x73, 0x74, 0x41, 0x64, 0x73, 0x62, 0x69, 0x7a, 0x22, 0x84, 0x01, 0x0a,
	0x11, 0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x35, 0x0a, 0x09, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x09,
	0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x38, 0x0a, 0x0b, 0x65, 0x78, 0x65,
	0x63, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17,
	0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x53, 0x51,
	0x4c, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x0a, 0x65, 0x78, 0x65, 0x63, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x32, 0x67, 0x0a, 0x10, 0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x53, 0x0a, 0x0c, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x41, 0x64, 0x73, 0x42, 0x69, 0x7a, 0x12, 0x20, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69,
	0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x65, 0x78, 0x6d, 0x73,
	0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x4d, 0x61, 0x72, 0x6b, 0x65,
	0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x3a, 0x5a, 0x38,
	0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x61, 0x37, 0x39, 0x32, 0x33,
	0x2f, 0x61, 0x74, 0x68, 0x65, 0x6e, 0x61, 0x2d, 0x67, 0x6f, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x3b,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_services_marketing_proto_rawDescOnce sync.Once
	file_services_marketing_proto_rawDescData = file_services_marketing_proto_rawDesc
)

func file_services_marketing_proto_rawDescGZIP() []byte {
	file_services_marketing_proto_rawDescOnce.Do(func() {
		file_services_marketing_proto_rawDescData = protoimpl.X.CompressGZIP(file_services_marketing_proto_rawDescData)
	})
	return file_services_marketing_proto_rawDescData
}

var file_services_marketing_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_services_marketing_proto_goTypes = []any{
	(*MarketingRequest)(nil),  // 0: exmsg.services.MarketingRequest
	(*MarketingResponse)(nil), // 1: exmsg.services.MarketingResponse
	(*models.Query)(nil),      // 2: exmsg.models.Query
	(*models.AdBiz)(nil),      // 3: exmsg.models.AdBiz
	(*models.Marketing)(nil),  // 4: exmsg.models.Marketing
	(*models.SQLResult)(nil),  // 5: exmsg.models.SQLResult
}
var file_services_marketing_proto_depIdxs = []int32{
	2, // 0: exmsg.services.MarketingRequest.query:type_name -> exmsg.models.Query
	3, // 1: exmsg.services.MarketingRequest.adsbiz:type_name -> exmsg.models.AdBiz
	3, // 2: exmsg.services.MarketingRequest.list_adsbiz:type_name -> exmsg.models.AdBiz
	4, // 3: exmsg.services.MarketingResponse.marketing:type_name -> exmsg.models.Marketing
	5, // 4: exmsg.services.MarketingResponse.exec_result:type_name -> exmsg.models.SQLResult
	0, // 5: exmsg.services.MarketingService.CreateAdsBiz:input_type -> exmsg.services.MarketingRequest
	1, // 6: exmsg.services.MarketingService.CreateAdsBiz:output_type -> exmsg.services.MarketingResponse
	6, // [6:7] is the sub-list for method output_type
	5, // [5:6] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_services_marketing_proto_init() }
func file_services_marketing_proto_init() {
	if File_services_marketing_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_services_marketing_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_services_marketing_proto_goTypes,
		DependencyIndexes: file_services_marketing_proto_depIdxs,
		MessageInfos:      file_services_marketing_proto_msgTypes,
	}.Build()
	File_services_marketing_proto = out.File
	file_services_marketing_proto_rawDesc = nil
	file_services_marketing_proto_goTypes = nil
	file_services_marketing_proto_depIdxs = nil
}
