package handlers

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"strconv"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/jmoiron/sqlx"
	"github.com/spf13/viper"
	"gitlab.com/a7923/athena-go/cmd/services/market-service/tables"
	"gitlab.com/a7923/athena-go/internal/adcrawl"
	"gitlab.com/a7923/athena-go/internal/adcrawl/domain"
	"gitlab.com/a7923/athena-go/pkg/common"
	"gitlab.com/a7923/athena-go/pkg/dbtool"
	"gitlab.com/a7923/athena-go/pkg/dbtool/postgres"
	"gitlab.com/a7923/athena-go/proto/exmsg/models"
	"gitlab.com/a7923/athena-go/proto/exmsg/services"
	"google.golang.org/protobuf/types/known/emptypb"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type MarketHandler struct {
	db           *dbtool.ConnectionManager
	fetcher      *adcrawl.Fetcher
	adAccRepo    domain.AdAccountRepository
	campaignRepo domain.CampaignRepository
	marketerRepo domain.MarketerRepository
}

func NewMarketHandler(
	adAccRepo domain.AdAccountRepository,
	campaignRepo domain.CampaignRepository,
	marketerRepo domain.MarketerRepository,
	db *dbtool.ConnectionManager) *MarketHandler {
	return &MarketHandler{
		fetcher: adcrawl.NewFetcher(adcrawl.FetcherConfig{
			RequestTimeout: time.Duration(viper.GetInt("market.fetch_request_time_out")) * time.Second},
		),
		adAccRepo:    adAccRepo,
		campaignRepo: campaignRepo,
		marketerRepo: marketerRepo,
		db:           db,
	}
}

var (
	insertAnalyticAccountColumns = []string{
		"id", "username", "profile_pic_url",
		"token", "marketer_id", "marketer_name", "company_id",
	}
)

func (h *MarketHandler) CreateAnalyticAccount(ctx context.Context, req *services.CreateAnalyticAccountRequest, resp *services.CreateAnalyticAccountResponse) error {
	token := req.Token
	analyticAcc, err := h.fetcher.FetchAnalyticAccount(token)
	if err != nil {
		return err
	}

	analyticAcc.Token = token
	analyticAcc.CompanyID = req.CompanyId
	analyticAcc.MarketerID = req.MarketerId
	analyticAcc.MarketerName = req.MarketerName

	conn := h.db.GetConnection()

	// If conflict occurred, there only 1 case can happen
	// - Soft delete: then we update all fields
	sq := squirrel.StatementBuilder.PlaceholderFormat(squirrel.Dollar)
	qb := sq.Insert(tables.AnalyticAccountTable).
		Columns(insertAnalyticAccountColumns...).
		Values(
			analyticAcc.ID,
			analyticAcc.Username,
			analyticAcc.ProfilePicURL,
			analyticAcc.Token,
			analyticAcc.MarketerID,
			analyticAcc.MarketerName,
			analyticAcc.CompanyID,
		).
		Suffix(`ON CONFLICT (company_id, id)
				DO UPDATE SET
					username = EXCLUDED.username,
					profile_pic_url = EXCLUDED.profile_pic_url,
					token = EXCLUDED.token,
					marketer_id = EXCLUDED.marketer_id,
					marketer_name = EXCLUDED.marketer_name,
					company_id = EXCLUDED.company_id,
					available = true,
					created_at = NOW(),
					updated_at = NULL,
					deleted_at = NULL
		`)

	stmt, args, err := qb.ToSql()
	if err != nil {
		return err
	}
	res, err := conn.ExecContext(ctx, stmt, args...)
	if err != nil {
		return err
	}
	rowsAffected, err := res.RowsAffected()
	if err != nil {
		return err
	}
	if rowsAffected == 0 {
		return dbtool.ErrNoRowsAffected
	}

	resp.AnalyticAccount = &models.AnalyticAccount{
		Id:            analyticAcc.ID,
		Username:      analyticAcc.Username,
		ProfilePicUrl: analyticAcc.ProfilePicURL,
		CompanyId:     analyticAcc.CompanyID,
		MarketerId:    analyticAcc.MarketerID,
		MarketerName:  analyticAcc.MarketerName,
	}

	return nil
}

var (
	selectAnalyticAccountsColumns = []string{
		"id", "username", "profile_pic_url",
		"marketer_id", "marketer_name",
		"available", "company_id", "created_at",
		"COALESCE(cnt.count_ad_accounts, 0) as count_ad_accounts",
	}
)

func (h *MarketHandler) ListAnalyticAccounts(ctx context.Context, req *services.ListAnalyticAccountsRequest, resp *services.ListAnalyticAccountsResponse) error {
	sqlTool := dbtool.NewSelect(ctx, h.db.GetConnection(),
		tables.GetAnalyticAccountTable(), &models.AnalyticAccount{})

	selectQb := squirrel.Select(selectAnalyticAccountsColumns...).
		From(tables.AnalyticAccountTable).
		Where(squirrel.And{
			squirrel.Eq{"company_id": req.CompanyId},
			squirrel.Eq{"deleted_at": nil},
		}).
		LeftJoin(`(SELECT analytic_account_id, count(id) as count_ad_accounts
			FROM fb.adaccounts
			WHERE company_id = ?
			GROUP BY analytic_account_id) AS cnt
			ON fb.analytic_accounts.id = cnt.analytic_account_id`, req.CompanyId).
		OrderBy(fmt.Sprintf("%s %s", req.SortField, common.BoolToOrder(req.IsAsc))).
		Offset(uint64(req.Page-1) * uint64(req.Limit)).
		Limit(uint64(req.Limit))

	if req.SortField != "id" {
		selectQb = selectQb.OrderBy("id asc") // additional sort by id for stable sort
	}
	if req.SearchTerm != nil {
		num, err := strconv.ParseInt(*req.SearchTerm, 10, 64)
		if err != nil {
			selectQb = selectQb.Where(squirrel.ILike{"username": "%" + *req.SearchTerm + "%"})
		} else {
			selectQb = selectQb.Where(squirrel.Eq{"id": num})
		}
	}
	if req.IsAvailable != nil {
		selectQb = selectQb.Where(squirrel.Eq{"available": *req.IsAvailable})
	}
	if req.MarketerIds != nil {
		selectQb = selectQb.Where(common.SubQueryIn("marketer_id", req.MarketerIds))
	}

	if err := sqlTool.Select(ctx, &resp.AnalyticAccounts, selectQb); err != nil {
		return err
	}

	conn := h.db.GetConnection()
	sq := squirrel.StatementBuilder.PlaceholderFormat(squirrel.Dollar)
	totalQb := sq.Select("COUNT(*) as total").
		From("fb.analytic_accounts").
		Where(squirrel.And{
			squirrel.Eq{"company_id": req.CompanyId},
			squirrel.Eq{"deleted_at": nil},
		})

	if req.SearchTerm != nil {
		num, err := strconv.ParseInt(*req.SearchTerm, 10, 64)
		if err != nil {
			totalQb = totalQb.Where(squirrel.ILike{"username": "%" + *req.SearchTerm + "%"})
		} else {
			totalQb = totalQb.Where(squirrel.Eq{"id": num})
		}
	}
	if req.IsAvailable != nil {
		totalQb = totalQb.Where(squirrel.Eq{"available": *req.IsAvailable})
	}
	if len(req.MarketerIds) > 0 {
		totalQb = totalQb.Where(common.SubQueryIn("marketer_id", req.MarketerIds))
	}
	stmt, args, err := totalQb.ToSql()
	if err != nil {
		return err
	}
	if err := conn.GetContext(ctx, &resp.Total, stmt, args...); err != nil {
		return err
	}

	return nil
}

var (
	cteCascadeDeleteAnalyticAccountQuery = `
		WITH analytic_account_deleted AS (
			UPDATE fb.analytic_accounts SET deleted_at = NOW() 
			WHERE company_id = ? AND id IN (?)
			RETURNING id
		),
		adaccount_deleted AS (
			UPDATE fb.adaccounts SET deleted_at = NOW()
			WHERE analytic_account_id IN (SELECT id FROM analytic_account_deleted)
				AND company_id = ?
				AND deleted_at IS NULL
			RETURNING id
		),
		campaign_deleted AS (
			UPDATE fb.campaigns SET deleted_at = NOW()
			WHERE adaccount_id IN (SELECT id FROM adaccount_deleted)
				AND company_id = ?
				AND deleted_at IS NULL
		)
		SELECT 1;
	`
)

func (h *MarketHandler) DeleteAnalyticAccount(ctx context.Context, req *services.DeleteAnalyticAccountRequest, resp *emptypb.Empty) error {
	if len(req.Ids) == 0 {
		return nil
	}

	conn := h.db.GetConnection()
	return postgres.WithTransactionSqlx(ctx, conn, func(tx *sqlx.Tx) error {
		query, args, err := sqlx.In(cteCascadeDeleteAnalyticAccountQuery, req.CompanyId, req.Ids,
			req.CompanyId, req.CompanyId)
		query = tx.Rebind(query)
		if err != nil {
			return err
		}
		if _, err := tx.ExecContext(ctx, query, args...); err != nil {
			return err
		}
		return nil
	})
}

var (
	selectAdAccountsColunns = []string{
		"adacc.id", "adacc.name", "CASE adacc.status WHEN 1 THEN 'active' ELSE 'disabled' END AS status", "adacc.currency",
		"adacc.timezone_offset_hours_utc", "adacc.analytic_account_id",
		"analytic_acc.username as analytic_account_name", "adacc.marketer_id",
		"adacc.updated_at", "adacc.updated_by_id",
	}
)

func (h *MarketHandler) ListAdAccounts(ctx context.Context, req *services.ListAdAccountsRequest, resp *services.ListAdAccountsResponse) error {
	sqlTool := dbtool.NewSelect(ctx, h.db.GetConnection(),
		tables.GetAdAccountTable(), &models.AdAccount{})
	selectQb := squirrel.Select(selectAdAccountsColunns...).
		From(tables.AdAccountTable+" adacc").
		Join(tables.AnalyticAccountTable+" analytic_acc ON analytic_acc.id = adacc.analytic_account_id AND analytic_acc.company_id = ?", req.CompanyId).
		Where(squirrel.Eq{"adacc.company_id": req.CompanyId}).
		Where(squirrel.Eq{"adacc.deleted_at": nil}).
		OrderBy(fmt.Sprintf("%s %s", req.SortField, common.BoolToOrder(req.IsAsc))).
		Offset(uint64(req.Page-1) * uint64(req.Limit)).
		Limit(uint64(req.Limit))

	if req.SortField != "id" {
		selectQb = selectQb.OrderBy("adacc.id asc") // additional sort by id for stable sort
	}
	if req.SearchTerm != nil {
		num, err := strconv.ParseInt(*req.SearchTerm, 10, 64)
		if err != nil {
			selectQb = selectQb.Where(squirrel.ILike{"adacc.name": "%" + *req.SearchTerm + "%"})
		} else {
			selectQb = selectQb.Where(squirrel.Eq{"adacc.id": num})
		}
	}
	if req.IsActive != nil {
		if *req.IsActive {
			selectQb = selectQb.Where(squirrel.Eq{"adacc.status": int16(1)})
		} else {
			selectQb = selectQb.Where(squirrel.NotEq{"adacc.status": int16(1)})
		}
	}
	if len(req.MarketerIds) > 0 {
		selectQb = selectQb.Where(squirrel.Or{
			squirrel.Eq{"adacc.marketer_id": req.MarketerIds},
			squirrel.Eq{"analytic_acc.marketer_id": req.MarketerIds},
		})
	}
	if len(req.AnalyticAccountIds) > 0 {
		selectQb = selectQb.Where(common.SubQueryIn("adacc.analytic_account_id", req.AnalyticAccountIds))
	} else {
		selectQb = selectQb.Where("analytic_account_id IN (SELECT id FROM fb.analytic_accounts WHERE company_id = ?)", req.CompanyId)
	}

	if err := sqlTool.Select(ctx, &resp.Adaccounts, selectQb); err != nil {
		return err
	}

	conn := h.db.GetConnection()
	sq := squirrel.StatementBuilder.PlaceholderFormat(squirrel.Dollar)
	totalQb := sq.Select("COUNT(*) as total").
		From(tables.AdAccountTable+" adacc").
		Join(tables.AnalyticAccountTable+" analytic_acc ON analytic_acc.id = adacc.analytic_account_id AND analytic_acc.company_id = ?", req.CompanyId).
		Where(squirrel.Eq{"adacc.company_id": req.CompanyId}).
		Where(squirrel.Eq{"adacc.deleted_at": nil})
	if req.SearchTerm != nil {
		num, err := strconv.ParseInt(*req.SearchTerm, 10, 64)
		if err != nil {
			totalQb = totalQb.Where(squirrel.ILike{"adacc.name": "%" + *req.SearchTerm + "%"})
		} else {
			totalQb = totalQb.Where(squirrel.Eq{"adacc.id": num})
		}
	}
	if req.IsActive != nil {
		if *req.IsActive {
			totalQb = totalQb.Where(squirrel.Eq{"adacc.status": int16(1)})
		} else {
			totalQb = totalQb.Where(squirrel.NotEq{"adacc.status": int16(1)})
		}
	}
	if len(req.MarketerIds) > 0 {
		totalQb = totalQb.Where(squirrel.Or{
			squirrel.Eq{"adacc.marketer_id": req.MarketerIds},
			squirrel.Eq{"analytic_acc.marketer_id": req.MarketerIds},
		})
	}
	if len(req.AnalyticAccountIds) > 0 {
		totalQb = totalQb.Where(common.SubQueryIn("analytic_account_id", req.AnalyticAccountIds))
	} else {
		totalQb = totalQb.Where("analytic_account_id IN (SELECT id FROM fb.analytic_accounts WHERE company_id = ? AND deleted_at IS NULL)", req.CompanyId)
	}
	stmt, args, err := totalQb.ToSql()
	if err != nil {
		return err
	}
	if err := conn.GetContext(ctx, &resp.Total, stmt, args...); err != nil {
		return err
	}

	return nil
}

func (h *MarketHandler) BulkUpdateAdAccounts(ctx context.Context, req *services.BulkUpdateAdAccountsRequest, resp *services.BulkUpdateAdAccountsResponse) error {
	updateTime := time.Now()
	qb := squirrel.Update(tables.AdAccountTable).
		Set("marketer_id", req.MarketerId).
		Set("updated_by_id", req.UpdatedById).
		Set("updated_at", updateTime).
		Where(squirrel.Eq{"company_id": req.CompanyId}).
		Where(squirrel.Expr("id IN (?)", req.AdaccountIds))
	stmt, args, err := qb.ToSql()
	if err != nil {
		return err
	}
	stmt, args, err = sqlx.In(stmt, args...)
	conn := h.db.GetConnection()
	stmt = conn.Rebind(stmt)
	if err != nil {
		return err
	}
	if _, err := conn.ExecContext(ctx, stmt, args...); err != nil {
		return err
	}
	resp.UpdatedAt = timestamppb.New(updateTime)
	resp.UpdatedById = req.UpdatedById

	return nil
}

var (
	cteCascadeDeleteAdAccountQuery = `
		WITH adaccount_deleted AS (
			UPDATE fb.adaccounts SET deleted_at = NOW()
			WHERE company_id = ? AND id IN (?)
			RETURNING id
		),
		campaign_deleted AS (
			UPDATE fb.campaigns SET deleted_at = NOW()
			WHERE adaccount_id IN (SELECT id FROM adaccount_deleted)
				AND company_id = ?
				AND deleted_at IS NULL
		)
		SELECT 1;
	`
)

func (h *MarketHandler) BulkDeleteAdAccounts(ctx context.Context, req *services.BulkDeleteAdAccountsRequest, resp *emptypb.Empty) error {
	if len(req.Ids) == 0 {
		return nil
	}

	conn := h.db.GetConnection()
	return postgres.WithTransactionSqlx(ctx, conn, func(tx *sqlx.Tx) error {
		query, args, err := sqlx.In(cteCascadeDeleteAdAccountQuery, req.CompanyId, req.Ids, req.CompanyId)
		query = tx.Rebind(query)
		if err != nil {
			return err
		}
		if _, err := tx.ExecContext(ctx, query, args...); err != nil {
			return err
		}
		return nil
	})
}

var (
	selectCampaignsColumns = []string{
		"camp.id", "camp.name", "camp.marketer_id", "camp.adaccount_id",
		"adacc.name AS adaccount_name", "analytic_acc.id AS analytic_account_id",
		"analytic_acc.username AS analytic_account_name", "camp.company_id",
		"camp.updated_by_id", "camp.updated_at", "camp.effective_status as status",
		"camp.landing_id", "camp.landing_name",
	}
)

func (h *MarketHandler) ListCampaigns(ctx context.Context, req *services.ListCampaignsRequest, resp *services.ListCampaignsResponse) error {
	selectQb := squirrel.Select(selectCampaignsColumns...).
		From(tables.CampaignTable+" camp").
		Join(tables.AdAccountTable+" adacc ON adacc.id=camp.adaccount_id AND adacc.company_id=? AND adacc.deleted_at IS NULL", req.CompanyId).
		Join(tables.AnalyticAccountTable+" analytic_acc ON analytic_acc.id=adacc.analytic_account_id AND analytic_acc.company_id=? AND analytic_acc.deleted_at IS NULL", req.CompanyId).
		Where(squirrel.And{
			squirrel.Eq{"camp.company_id": req.CompanyId},
			squirrel.Eq{"camp.deleted_at": nil},
		}).
		OrderBy(fmt.Sprintf("%s %s", req.SortField, common.BoolToOrder(req.IsAsc))).
		Offset(uint64(req.Page-1) * uint64(req.Limit)).
		Limit(uint64(req.Limit))
	if req.SortField != "id" {
		selectQb = selectQb.OrderBy("camp.id asc") // additional sort by id for stable sort
	}
	if req.UserId != nil {
		selectQb = selectQb.Where(squirrel.Eq{"camp.marketer_id": *req.UserId})
	}
	if req.SearchTerm != nil {
		num, err := strconv.ParseInt(*req.SearchTerm, 10, 64)
		if err != nil {
			selectQb = selectQb.Where(squirrel.ILike{"camp.name": "%" + *req.SearchTerm + "%"})
		} else {
			selectQb = selectQb.Where(squirrel.Eq{"camp.id": num})
		}
	}
	if req.Status != nil {
		selectQb = selectQb.Where(squirrel.Eq{"camp.effective_status": *req.Status})
	}
	if len(req.MarketerIds) > 0 {
		selectQb = selectQb.Where(
			squirrel.Or{
				squirrel.Eq{"camp.marketer_id": req.MarketerIds},
				squirrel.Eq{"adacc.marketer_id": req.MarketerIds},
				squirrel.Eq{"analytic_acc.marketer_id": req.MarketerIds},
			},
		)
	}
	if len(req.AnalyticAccountIds) > 0 {
		selectQb = selectQb.Where(common.SubQueryIn("adacc.analytic_account_id", req.AnalyticAccountIds))
	}
	if len(req.AdaccountIds) > 0 {
		selectQb = selectQb.Where(common.SubQueryIn("adaccount_id", req.AdaccountIds))
	}
	if len(req.LandingIds) > 0 {
		selectQb = selectQb.Where(squirrel.Eq{"landing_id": req.LandingIds})
	}
	conn := h.db.GetConnection()
	sqlTool := dbtool.NewInsert(ctx, conn, tables.GetCampaignTable(), &models.Campaign{})
	if err := sqlTool.Select(ctx, &resp.Campaigns, selectQb); err != nil {
		return err
	}

	totalQb := squirrel.Select("COUNT(*) AS total").
		From(tables.CampaignTable+" camp").
		Join(tables.AdAccountTable+" adacc ON adacc.id=camp.adaccount_id AND adacc.company_id=? AND adacc.deleted_at IS NULL", req.CompanyId).
		Join(tables.AnalyticAccountTable+" analytic_acc ON analytic_acc.id=adacc.analytic_account_id AND analytic_acc.company_id=? AND analytic_acc.deleted_at IS NULL", req.CompanyId).
		Where(squirrel.And{
			squirrel.Eq{"camp.company_id": req.CompanyId},
			squirrel.Eq{"camp.deleted_at": nil},
		})
	if req.UserId != nil {
		totalQb = totalQb.Where(squirrel.Eq{"camp.marketer_id": *req.UserId})
	}
	if req.SearchTerm != nil {
		num, err := strconv.ParseInt(*req.SearchTerm, 10, 64)
		if err != nil {
			totalQb = totalQb.Where(squirrel.ILike{"camp.name": "%" + *req.SearchTerm + "%"})
		} else {
			totalQb = totalQb.Where(squirrel.Eq{"camp.id": num})
		}
	}
	if req.Status != nil {
		totalQb = totalQb.Where(squirrel.Eq{"camp.effective_status": *req.Status})
	}
	if len(req.MarketerIds) > 0 {
		totalQb = totalQb.Where(
			squirrel.Or{
				squirrel.Eq{"camp.marketer_id": req.MarketerIds},
				squirrel.Eq{"adacc.marketer_id": req.MarketerIds},
				squirrel.Eq{"analytic_acc.marketer_id": req.MarketerIds},
			},
		)
	}
	if len(req.AnalyticAccountIds) > 0 {
		totalQb = totalQb.Where(common.SubQueryIn("adacc.analytic_account_id", req.AnalyticAccountIds))
	}
	if len(req.AdaccountIds) > 0 {
		totalQb = totalQb.Where(common.SubQueryIn("adaccount_id", req.AdaccountIds))
	}
	if len(req.LandingIds) > 0 {
		totalQb = totalQb.Where(squirrel.Eq{"landing_id": req.LandingIds})
	}
	stmt, args, err := totalQb.PlaceholderFormat(squirrel.Dollar).ToSql()
	if err != nil {
		return err
	}
	if err := conn.GetContext(ctx, &resp.Total, stmt, args...); err != nil {
		return err
	}

	var countNotInBiz int64
	countNotInBizQb := totalQb.
		Where(squirrel.Eq{"camp.available": false})
	stmt, args, err = countNotInBizQb.PlaceholderFormat(squirrel.Dollar).ToSql()
	if err != nil {
		return err
	}
	if err := conn.GetContext(ctx, &countNotInBiz, stmt, args...); err != nil {
		return err
	}

	resp.CountNotInBiz = countNotInBiz
	return nil
}

func (h *MarketHandler) BulkUpdateCampaigns(ctx context.Context, req *services.BulkUpdateCampaignsRequest, resp *services.BulkUpdateCampaignsResponse) error {
	updateFields := make([]string, 0)
	updateValues := make([]any, 0)
	if req.LandingId != nil && req.LandingName != nil {
		updateFields = append(updateFields, "landing_id")
		updateValues = append(updateValues, *req.LandingId)

		updateFields = append(updateFields, "landing_name")
		updateValues = append(updateValues, *req.LandingName)
	}
	if req.MarketerId != nil {
		updateFields = append(updateFields, "marketer_id")
		updateValues = append(updateValues, *req.MarketerId)
	}
	if len(updateFields) == 0 {
		return nil
	}
	updateFields = append(updateFields, "updated_by_id")
	updateValues = append(updateValues, req.UpdatedById)

	updatedAt, err := h.campaignRepo.BulkUpdate(ctx, req.CampaignIds, req.CompanyId, updateFields, updateValues)
	if err != nil {
		return err
	}
	resp.UpdatedById = req.UpdatedById
	resp.UpdatedAt = timestamppb.New(updatedAt)
	return nil
}

func (h *MarketHandler) AssignCompanyForCampaign(ctx context.Context, req *services.AssignCompanyForCampaignRequest, resp *services.AssignCompanyForCampaignResponse) error {
	updatedTime := time.Now()
	resp.UpdatedById = req.UpdatedById
	resp.UpdatedAt = timestamppb.New(updatedTime)
	campaign := &domain.Campaign{
		ID:          req.CampaignId,
		CompanyID:   &req.CompanyId,
		UpdatedByID: req.UpdatedById,
		UpdatedAt:   updatedTime,
	}
	if req.Disable {
		campaign.CompanyID = nil
	}
	updateFields := []string{"company_id", "updated_by_id", "updated_at"}
	return h.campaignRepo.Update(ctx, campaign, updateFields)
}

func (h *MarketHandler) BulkDeleteCampaigns(ctx context.Context, req *services.BulkDeleteCampaignsRequest, resp *emptypb.Empty) error {
	qb := squirrel.Update(tables.CampaignTable).
		Set("deleted_at", time.Now()).
		Where(squirrel.Eq{"company_id": req.CompanyId}).
		Where("id IN (?)", req.Ids).
		Suffix("RETURNING id")
	stmt, args, _ := qb.ToSql()
	stmt, args, _ = sqlx.In(stmt, args...)

	conn := h.db.GetConnection()
	stmt = conn.Rebind(stmt)

	return postgres.WithTransactionSqlx(ctx, conn, func(tx *sqlx.Tx) error {
		deletedCampaignIDs := make([]int64, 0)
		if err := tx.SelectContext(ctx, &deletedCampaignIDs, stmt, args...); err != nil {
			return err
		}
		if len(deletedCampaignIDs) != len(req.Ids) {
			marked := make(map[int64]bool)
			for _, id := range deletedCampaignIDs {
				marked[id] = true
			}
			nonDeletedCampaigns := make([]int64, 0)
			for _, id := range req.Ids {
				if !marked[id] {
					nonDeletedCampaigns = append(nonDeletedCampaigns, id)
				}
			}
			return fmt.Errorf("failed to delete these campaigns: %v", nonDeletedCampaigns)
		}
		return nil
	})
}

func (h *MarketHandler) ListMarketers(ctx context.Context, req *services.ListMarketersRequest, resp *services.ListMarketersResponse) error {
	sb := domain.SortByParam{
		Field: req.SortField,
		Order: common.BoolToOrder(req.IsAsc),
	}

	listResp, err := h.marketerRepo.List(ctx, req.CompanyId, sb)
	if err != nil {
		return err
	}

	resp.Marketers = make([]*models.Marketer, len(listResp.Marketers))
	for i := range listResp.Marketers {
		resp.Marketers[i] = &models.Marketer{
			Id:   listResp.Marketers[i].ID,
			Name: listResp.Marketers[i].Name,
		}
	}
	resp.Total = listResp.Total
	return nil
}

func (h *MarketHandler) BatchInsertAdAccounts(ctx context.Context, req *services.BatchInsertAdAccountsRequest, resp *services.BatchInsertAdAccountsResponse) error {
	sqlTool := dbtool.NewInsert(ctx, h.db.GetConnection(),
		tables.GetAdAccountTable(), &models.AdAccount{})

	qb := squirrel.Insert(tables.AdAccountTable).
		Columns("id", "name", "status", "currency",
			"timezone_offset_hours_utc", "marketer_id", "analytic_account_id",
			"updated_by_id", "created_at", "updated_at").
		Suffix(`ON CONFLICT (id) DO UPDATE
				SET name=EXCLUDED.name,
				status=EXCLUDED.status,
				currency=EXCLUDED.currency,
				timezone_offset_hours_utc=EXCLUDED.timezone_offset_hours_utc,
				analytic_account_id=EXCLUDED.analytic_account_id,
				updated_at=EXCLUDED.updated_at`)

	currentTime := time.Now()
	for i := range req.Adaccounts {
		qb = qb.Values(
			req.Adaccounts[i].Id,
			req.Adaccounts[i].Name,
			domain.AdAccountStatusStrToInt(req.Adaccounts[i].Status),
			req.Adaccounts[i].Currency,
			req.Adaccounts[i].TimezoneOffsetHoursUtc,
			req.Adaccounts[i].MarketerId,
			req.Adaccounts[i].AnalyticAccountId,
			req.Adaccounts[i].MarketerId,
			currentTime,
			currentTime,
		)
	}

	_, err := sqlTool.Insert(ctx, qb)
	if err != nil {
		return err
	}

	return nil
}

func (h *MarketHandler) BatchInsertCampaigns(ctx context.Context, req *services.BatchInsertCampaignsRequest, resp *services.BatchInsertCampaignsResponse) error {
	sqlTool := dbtool.NewInsert(ctx, h.db.GetConnection(),
		tables.GetCampaignTable(), &models.Campaign{})
	qb := squirrel.Insert(tables.CampaignTable).
		Columns("id", "name", "status", "effective_status",
			"adaccount_id", "objective", "marketer_id",
			"updated_by_id", "created_at", "updated_at").
		Suffix(`ON CONFLICT (id) DO UPDATE
				SET name=EXCLUDED.name,
				status=EXCLUDED.status,
				effective_status=EXCLUDED.effective_status,
				adaccount_id=EXCLUDED.adaccount_id,
				objective=EXCLUDED.objective,
				marketer_id=EXCLUDED.marketer_id,
				updated_at=EXCLUDED.updated_at`)
	currentTime := time.Now()
	for i := range req.Campaigns {
		qb = qb.Values(
			req.Campaigns[i].Id,
			req.Campaigns[i].Name,
			req.Campaigns[i].Status,
			req.Campaigns[i].EffectiveStatus,
			req.Campaigns[i].AdaccountId,
			req.Campaigns[i].Objective,
			req.Campaigns[i].MarketerId,
			req.Campaigns[i].MarketerId,
			currentTime,
			currentTime,
		)
	}

	_, err := sqlTool.Insert(ctx, qb)
	if err != nil {
		return err
	}

	return nil
}

func (h *MarketHandler) BatchInsertAdsets(ctx context.Context, req *services.BatchInsertAdsetsRequest, resp *services.BatchInsertAdsetsResponse) error {
	sqlTool := dbtool.NewInsert(ctx, h.db.GetConnection(),
		tables.GetAdSetTable(), &models.Adset{})
	qb := squirrel.Insert(tables.AdSetTable).
		Columns("id", "name", "status", "effective_status",
			"optimization_goal", "campaign_id", "adaccount_id").
		Suffix(`ON CONFLICT (id) DO UPDATE
				SET name=EXCLUDED.name,
				status=EXCLUDED.status,
				effective_status=EXCLUDED.effective_status,
				optimization_goal=EXCLUDED.optimization_goal,
				campaign_id=EXCLUDED.campaign_id,
				adaccount_id=EXCLUDED.adaccount_id,
				updated_at=NOW()`)

	for i := range req.Adsets {
		qb = qb.Values(
			req.Adsets[i].Id,
			req.Adsets[i].Name,
			req.Adsets[i].Status,
			req.Adsets[i].EffectiveStatus,
			req.Adsets[i].OptimizationGoal,
			req.Adsets[i].CampaignId,
			req.Adsets[i].AdaccountId,
		)
	}

	_, err := sqlTool.Insert(ctx, qb)
	if err != nil {
		return err
	}

	return nil
}

func (h *MarketHandler) BatchInsertAds(ctx context.Context, req *services.BatchInsertAdsRequest, resp *services.BatchInsertAdsResponse) error {
	sqlTool := dbtool.NewInsert(ctx, h.db.GetConnection(),
		tables.GetAdTable(), &models.Ad{})
	qb := squirrel.Insert(tables.AdTable).
		Columns("id", "name", "status", "effective_status",
			"adset_id", "page_id", "object_story_id",
			"object_story_spec", "image_url", "created_at").
		Suffix(`ON CONFLICT (id) DO UPDATE
				SET name=EXCLUDED.name,
				status=EXCLUDED.status,
				effective_status=EXCLUDED.effective_status,
				adset_id=EXCLUDED.adset_id,
				page_id=EXCLUDED.page_id,
				object_story_id=EXCLUDED.object_story_id,
				object_story_spec=EXCLUDED.object_story_spec,
				image_url=EXCLUDED.image_url,
				updated_at=NOW()
			`)

	currentTime := time.Now()
	for i := range req.Ads {
		qb = qb.Values(
			req.Ads[i].Id,
			req.Ads[i].Name,
			req.Ads[i].Status,
			req.Ads[i].EffectiveStatus,
			req.Ads[i].AdsetId,
			req.Ads[i].PageId,
			req.Ads[i].ObjectStoryId,
			common.WrapJSONbyte(req.Ads[i].ObjectStorySpec),
			req.Ads[i].ImageUrl,
			currentTime,
		)
	}

	_, err := sqlTool.Insert(ctx, qb)
	if err != nil {
		return err
	}

	return nil
}

func (h *MarketHandler) BatchInsertSpendings(ctx context.Context, req *services.BatchInsertSpendingsRequest, resp *services.BatchInsertSpendingsResponse) error {
	t := tables.GetSpendingTable()
	t.ReturnIdColumn = "-" // Set to "-" to avoid returning the id column
	sqlTool := dbtool.NewInsert(ctx, h.db.GetConnection(),
		t, &models.Spending{})
	qb := squirrel.Insert(tables.SpendingTable).
		Columns("ad_id", "date_start", "hourly_stat", "raw_numeric",
			"cpm", "cpc", "ctr",
			"clicks", "frequency",
			"p25", "p50", "p75", "p95",
			"video_average", "impressions", "updated_at").
		Suffix(`ON CONFLICT (ad_id, date_start, hourly_stat) DO UPDATE
				SET raw_numeric=EXCLUDED.raw_numeric,
				cpm=EXCLUDED.cpm,
				cpc=EXCLUDED.cpc,
				ctr=EXCLUDED.ctr,
				clicks=EXCLUDED.clicks,
				frequency=EXCLUDED.frequency,
				p25=EXCLUDED.p25,
				p50=EXCLUDED.p50,
				p75=EXCLUDED.p75,
				p95=EXCLUDED.p95,
				video_average=EXCLUDED.video_average,
				impressions=EXCLUDED.impressions,
				updated_at=EXCLUDED.updated_at`)

	currentTime := time.Now()
	for i := range req.Spendings {
		qb = qb.Values(
			req.Spendings[i].AdId,
			req.Spendings[i].DateStart,
			req.Spendings[i].HourlyStat,
			req.Spendings[i].RawNumeric,
			req.Spendings[i].Cpm,
			req.Spendings[i].Cpc,
			req.Spendings[i].Ctr,
			req.Spendings[i].Clicks,
			req.Spendings[i].Frequency,
			req.Spendings[i].P25,
			req.Spendings[i].P50,
			req.Spendings[i].P75,
			req.Spendings[i].P95,
			req.Spendings[i].VideoAverage,
			req.Spendings[i].Impressions,
			currentTime,
		)
	}

	_, err := sqlTool.Insert(ctx, qb)
	if err != nil {
		return err
	}

	return nil
}

func (h *MarketHandler) GetTokenByAdID(ctx context.Context, req *services.GetTokenByAdIDRequest, resp *services.GetTokenByAdIDResponse) error {
	conn := h.db.GetConnection()
	tokenQuery := `
		SELECT aa.token
		FROM fb.ads a
		JOIN fb.adsets s ON a.adset_id = s.id
		JOIN fb.campaigns c ON s.campaign_id = c.id
		JOIN fb.adaccounts ac ON c.adaccount_id = ac.id
		JOIN fb.analytic_accounts aa ON ac.analytic_account_id = aa.id
		WHERE a.id = $1;
	`
	if err := conn.GetContext(ctx, &resp.Token, tokenQuery, req.AdId); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			resp.Token = ""
			return nil
		}
		return fmt.Errorf("failed to get token for ad id %d: %s", req.AdId, err.Error())
	}
	return nil
}
