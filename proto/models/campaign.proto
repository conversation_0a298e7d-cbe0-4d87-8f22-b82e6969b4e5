syntax = "proto3";

package exmsg.models;

option go_package = "gitlab.com/a7923/athena-go/proto/exmsg/models;models";

import "google/protobuf/timestamp.proto";

message Campaign {
    int64 id = 1;
    string name = 2;
    string status = 3;
    string effective_status = 4;
    optional bool avaiable = 5;
    int64 adaccount_id = 6;
    optional string objective = 7;
    optional int64 country_id = 8;
    optional int64 marketer_id= 9;
    optional string adaccount_name = 10;
    optional int64 analytic_account_id = 11;
    optional string analytic_account_name = 12;
    optional int64 updated_by_id = 13;
    optional google.protobuf.Timestamp updated_at = 14;
    optional int64 company_id = 15;
    optional string landing_id = 16;
    optional string landing_name = 17;
}