syntax = "proto3";

package exmsg.models;

option go_package = "gitlab.com/a7923/athena-go/proto/exmsg/models;models";
import "google/protobuf/timestamp.proto";
import "google/protobuf/struct.proto";

message Marketing {
  int64 id = 1;
  string type = 2;
  google.protobuf.Timestamp created_at = 3;
  google.protobuf.Struct metadata = 4;
}

message AdBiz {
  string ad_id = 1;
  int64 country_id = 2;
  int64 project_id = 3;
  int64 company_id = 4;
  int64 product_id = 5;
  string landing_page_id = 6;
}