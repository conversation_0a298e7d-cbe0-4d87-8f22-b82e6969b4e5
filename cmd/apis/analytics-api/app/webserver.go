package app

import (
	"github.com/goccy/go-json"
	"github.com/gorilla/mux"
	"github.com/justinas/alice"
	"github.com/spf13/cast"
	"github.com/spf13/viper"
	"gitlab.com/a7923/athena-go/cmd/apis/analytics-api/handlers"
	"gitlab.com/a7923/athena-go/internal"
	http2 "gitlab.com/a7923/athena-go/internal/http"
	"gitlab.com/a7923/athena-go/internal/middlewares"
	"gitlab.com/a7923/athena-go/internal/permissions"
	"gitlab.com/a7923/athena-go/internal/reports"
	"gitlab.com/a7923/athena-go/internal/token"
	"gitlab.com/a7923/athena-go/pkg/app"
	"gitlab.com/a7923/athena-go/pkg/micro/web"
	middleware "gitlab.com/a7923/athena-go/pkg/middlewares"
	transhttp "gitlab.com/a7923/athena-go/pkg/transport"
	"gitlab.com/a7923/athena-go/pkg/utils"
	"gitlab.com/a7923/athena-go/proto/exmsg/services"
	"go-micro.dev/v5/client"
	"net/http"
	"net/url"
	"strings"
)

type Server struct {
	Name     string
	client   client.Client
	producer app.PublisherInterface
}

func NewServer(publisher app.PublisherInterface) *Server {
	return &Server{
		Name:     "analytics",
		producer: publisher,
	}
}

func (s *Server) SetGRPCClient(client client.Client) {
	s.client = client
}

func (s *Server) GetBasePath() string {
	return "/analytics"
}

func (s *Server) GetName() string {
	return s.Name
}

func (s *Server) GetRoutes() web.Routes {
	var mdws []alice.Constructor

	if viper.GetBool("logging.enable") {
		mdws = append(mdws, middleware.LoggingMiddleware)
	}

	analyticsClient := internal.CreateAnalyticsClient(s.client)
	userCient := internal.CreateNewUserServiceClient(s.client)

	legacyAuth := middlewares.NewLegacyAuthMiddleware()

	return []web.Route{
		{
			Name:    "Get Analytics Event",
			Method:  http.MethodGet,
			Pattern: "/reports",
			Handler: &handlers.AnalyticsHandler{
				Producer:        s.producer,
				AnalyticsClient: analyticsClient,
			},
			Middlewares: mdws,
			AuthInfo: web.AuthInfo{
				Enable: true,
				RequirePermissions: map[int64]int64{
					int64(permissions.Dashboard): permissions.DashboardMarketingTelesales | permissions.DashboardMarketingCarePage,
				},
			},
			Timeout: 10000, // 10 seconds
		},
		{
			Name:    "Get Analytics Event",
			Method:  http.MethodGet,
			Pattern: "/report/{team}/{type}",
			Handler: &handlers.AnalyticsHandler{
				Producer:        s.producer,
				AnalyticsClient: analyticsClient,
			},
			Middlewares: append(
				mdws,
				legacyAuth.Middleware,
				paramConvertMiddleware(userCient),
			),
			AuthInfo: web.AuthInfo{
				Enable: true,
				RequirePermissions: map[int64]int64{
					int64(permissions.Dashboard): permissions.DashboardMarketingTelesales | permissions.DashboardMarketingCarePage,
				},
			},
			Timeout: 10000, // 10 seconds
		},
		{
			Name:    "Get Order Dashboard",
			Method:  http.MethodGet,
			Pattern: "/orders/dashboard",
			Handler: &handlers.OrderDashboardHandler{
				Producer:        s.producer,
				AnalyticsClient: analyticsClient,
			},
			Middlewares: append(
				mdws,
				legacyAuth.Middleware,
				paramConvertMiddlewareForOrderDashboard(userCient),
			),
			AuthInfo: web.AuthInfo{
				Enable: true,
				RequirePermissions: map[int64]int64{
					int64(permissions.Dashboard): permissions.DashboardOrderShipmentOverview | permissions.DashboardOrderShipmentFilter,
				},
			},
			Timeout: 10000, // 10 seconds
		},
		{
			Name:    "Get Order Dashboard Option Data",
			Method:  http.MethodGet,
			Pattern: "/orders/dashboard/{action}",
			Handler: &handlers.OrderDashboardHandler{
				Producer:        s.producer,
				AnalyticsClient: analyticsClient,
			},
			Middlewares: append(
				mdws,
				legacyAuth.Middleware,
				paramConvertMiddlewareForOrderDashboard(userCient),
			),
			AuthInfo: web.AuthInfo{
				Enable: true,
				RequirePermissions: map[int64]int64{
					int64(permissions.Dashboard): permissions.DashboardOrderShipmentOverview | permissions.DashboardOrderShipmentFilter,
				},
			},
			Timeout: 10000, // 10 seconds
		},
		{
			Name:    "Get Telesale Dashboard Option Data",
			Method:  http.MethodGet,
			Pattern: "/telesale/dashboard/{action}",
			Handler: &handlers.TelesaleDashboardHandler{
				Producer:        s.producer,
				AnalyticsClient: analyticsClient,
			},
			Middlewares: append(
				mdws,
				legacyAuth.Middleware,
				paramConvertMiddlewareForTelesaleDashboard(userCient),
			),
			AuthInfo: web.AuthInfo{
				Enable: true,
				RequirePermissions: map[int64]int64{
					int64(permissions.Dashboard): permissions.DashboardTelesalesOverview | permissions.DashboardTelesalesFilter,
				},
			},
			Timeout: 10000, // 10 seconds
		},
		{
			Name:    "Get Telesale Dashboard Option Data",
			Method:  http.MethodGet,
			Pattern: "/telesale/dashboard",
			Handler: &handlers.TelesaleDashboardHandler{
				Producer:        s.producer,
				AnalyticsClient: analyticsClient,
			},
			Middlewares: append(
				mdws,
				legacyAuth.Middleware,
				paramConvertMiddlewareForTelesaleDashboard(userCient),
			),
			AuthInfo: web.AuthInfo{
				Enable: true,
				RequirePermissions: map[int64]int64{
					int64(permissions.Dashboard): permissions.DashboardTelesalesOverview | permissions.DashboardTelesalesFilter,
				},
			},
			Timeout: 10000, // 10 seconds
		},
	}
}

func paramConvertMiddleware(userClient services.UserService) alice.Constructor {
	return func(handler http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			vars := mux.Vars(r)
			team := strings.ToLower(vars["team"])
			reportType := strings.ToLower(vars["type"])
			groupBy := reports.ReportType2Dimension[reportType]
			isAdmin := http2.GetIsAdminFromRequest(r)
			query := r.URL.Query()
			dashboardType := query.Get("dashboard_type")
			if len(groupBy) == 0 {
				transhttp.RespondError(w, http.StatusBadRequest, "invalid report type")
				return
			}

			params := make(map[string]interface{})
			teamInCharge := reports.TeamInChargeTelesale
			if v, ok := reports.TeamInChargeByDashboardType[team]; ok {
				teamInCharge = v
			}
			if v, ok := reports.TeamInChargeByDashboardType[dashboardType]; ok {
				teamInCharge = v
			}

			params["team_in_charge"] = teamInCharge
			params["group_by"] = groupBy
			params["order_by"] = "total_orders DESC"

			if groupBy == reports.DimensionDate {
				params["order_by"] = "date DESC"
			}

			for k, v := range params {
				query.Set(k, cast.ToString(v))
			}

			if !isAdmin {
				scopes := middlewares.ScopeResponse{}
				_ = json.Unmarshal([]byte(r.Header.Get("X-Gw-Scopes")), &scopes)
				if len(scopes.Data) > 0 {
					projects := filterProjectIds(cast.ToInt(query.Get("country_id")), query["project_ids"], scopes.Data)
					query.Del("project_ids")
					for _, v := range projects {
						query.Add("project_ids", v)
					}
				}

				marketerIds, err := token.GetMarketerIds(r, userClient, http2.GetIdsFromRequestQuery(r, "marketer_ids"))
				if err != nil {
					transhttp.RespondError(w, http.StatusBadRequest, err.Error())
					return
				}

				query.Del("marketer_ids")
				for _, v := range marketerIds {
					query.Add("marketer_ids", cast.ToString(v))
				}
			}

			r.URL.RawQuery = query.Encode()
			handler.ServeHTTP(w, r)
		})
	}
}

func filterMarketerIds(query url.Values, r *http.Request) []string {
	restrictMarketers := []string{
		cast.ToString(http2.GetUserIDFromRequest(r)),
	}

	for _, v := range http2.GetDescendantIdsFromRequest(r) {
		restrictMarketers = append(restrictMarketers, cast.ToString(v))
	}

	marketerIds := query["marketer_ids"]
	if len(marketerIds) == 0 {
		return restrictMarketers
	}
	return utils.Intersect(marketerIds, restrictMarketers)
}

func filterProjectIds(countryId int, i []string, data [][]int) []string {
	projectIdsFromScopes := make([]string, 0)
	m := make(map[int]bool)
	for _, v := range data {
		if len(v) < 1 {
			continue
		}
		if m[v[1]] {
			continue
		}

		if countryId > 0 && countryId != v[0] {
			continue
		}

		projectIdsFromScopes = append(projectIdsFromScopes, cast.ToString(v[1]))
		m[v[1]] = true
	}

	if len(i) == 0 {
		return projectIdsFromScopes
	}

	return utils.Intersect(projectIdsFromScopes, i)
}

func paramConvertMiddlewareForOrderDashboard(userClient services.UserService) alice.Constructor {
	return func(handler http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			vars := mux.Vars(r)
			action := strings.ToLower(vars["action"])
			query := r.URL.Query()
			isAdmin := http2.GetIsAdminFromRequest(r)
			userId := http2.GetUserIDFromRequest(r)
			saleIds := http2.GetSaleDescendantIdsFromRequest(r)
			carePageIds := http2.GetCarePageDescendantIdsFromRequest(r)
			dids := make([]int64, 0)
			dids = append(dids, carePageIds...)
			dids = append(dids, saleIds...)
			params := make(map[string]interface{})
			if len(action) > 0 {
				params["action"] = action
			}
			if len(dids) > 0 {
				query.Add("dids", utils.IntSliceToString(dids, ","))
			}
			for k, v := range params {
				query.Set(k, cast.ToString(v))
			}
			if !isAdmin {
				scopes := middlewares.ScopeResponse{}
				_ = json.Unmarshal([]byte(r.Header.Get("X-Gw-Scopes")), &scopes)
				if len(scopes.Data) > 0 {
					projects := filterProjectIds(cast.ToInt(query.Get("country_id")), query["project_ids"], scopes.Data)
					query.Del("project_ids")
					for _, v := range projects {
						query.Add("project_ids", v)
					}
				}
				//
				userProfiles, err := userClient.GetUserProfile(r.Context(), &services.GetUserPermissionRequest{
					UserId: userId,
				})
				if err != nil {
					transhttp.RespondError(w, http.StatusBadRequest, err.Error())
					return
				}
				if len(userProfiles.Profiles) > 0 {
					for _, str := range userProfiles.Profiles {
						parts := strings.Split(str, ":")
						if len(parts[1]) > 0 && parts[0] == "0" {
							moduleInCharges := strings.Split(parts[1], ",")
							for i, v := range moduleInCharges {
								switch v {
								case "0":
									if i == 0 {
										query.Del("marketer_ids")
									}
									query.Add("marketer_ids", cast.ToString(userId))
								case "1":
									if i == 0 {
										query.Del("sale_reps")
									}
									query.Add("sale_id", cast.ToString(userId))
								case "2":
									if i == 0 {
										query.Del("sale_reps")
									}
									query.Add("care_page_id", cast.ToString(userId))
								}
							}
							query.Del("dids")
							query.Add("dids", cast.ToString(userId))
						} else if len(parts[1]) > 0 && parts[0] != "0" {
							moduleInCharges := strings.Split(parts[1], ",")
							for _, v := range moduleInCharges {
								switch v {
								case "1":
									query.Add("team_in_charge", "1")
								case "2":
									query.Add("team_in_charge", "2")
								}
							}
						}
					}
				}
			}
			r.URL.RawQuery = query.Encode()
			handler.ServeHTTP(w, r)
		})
	}
}

func paramConvertMiddlewareForTelesaleDashboard(userClient services.UserService) alice.Constructor {
	return func(handler http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			vars := mux.Vars(r)
			action := strings.ToLower(vars["action"])
			query := r.URL.Query()
			isAdmin := http2.GetIsAdminFromRequest(r)
			userId := http2.GetUserIDFromRequest(r)
			saleIds := http2.GetSaleDescendantIdsFromRequest(r)
			carePageIds := http2.GetCarePageDescendantIdsFromRequest(r)
			dids := make([]int64, 0)
			dids = append(dids, carePageIds...)
			dids = append(dids, saleIds...)
			params := make(map[string]interface{})
			if len(action) > 0 {
				params["action"] = action
			}
			if len(dids) > 0 {
				query.Add("dids", utils.IntSliceToString(dids, ","))
			}
			for k, v := range params {
				query.Set(k, cast.ToString(v))
			}
			if !isAdmin {
				scopes := middlewares.ScopeResponse{}
				_ = json.Unmarshal([]byte(r.Header.Get("X-Gw-Scopes")), &scopes)
				if len(scopes.Data) > 0 {
					projects := filterProjectIds(cast.ToInt(query.Get("country_id")), query["project_ids"], scopes.Data)
					query.Del("project_ids")
					for _, v := range projects {
						query.Add("project_ids", v)
					}
				}
				//
				userProfiles, err := userClient.GetUserProfile(r.Context(), &services.GetUserPermissionRequest{
					UserId: userId,
				})
				if err != nil {
					transhttp.RespondError(w, http.StatusBadRequest, err.Error())
					return
				}
				if len(userProfiles.Profiles) > 0 {
					for _, str := range userProfiles.Profiles {
						parts := strings.Split(str, ":")
						if len(parts[1]) > 0 && parts[0] == "0" {
							moduleInCharges := strings.Split(parts[1], ",")
							for i, v := range moduleInCharges {
								switch v {
								case "0":
									if i == 0 {
										query.Del("marketer_ids")
									}
									query.Add("marketer_ids", cast.ToString(userId))
								case "1":
									if i == 0 {
										query.Del("sale_reps")
									}
									query.Add("sale_id", cast.ToString(userId))
								case "2":
									if i == 0 {
										query.Del("sale_reps")
									}
									query.Add("care_page_id", cast.ToString(userId))
								}
							}
							query.Del("dids")
							query.Add("dids", cast.ToString(userId))
						} else if len(parts[1]) > 0 && parts[0] != "0" {
							moduleInCharges := strings.Split(parts[1], ",")
							for _, v := range moduleInCharges {
								switch v {
								case "1":
									query.Add("team_in_charge", "1")
								case "2":
									query.Add("team_in_charge", "2")
								}
							}
						}
					}
				}
			}
			r.URL.RawQuery = query.Encode()
			handler.ServeHTTP(w, r)
		})
	}
}
