package tracking_provider

import (
	"context"
	"fmt"
	"github.com/go-redis/redis/v8"
	"github.com/spf13/cast"
	"gitlab.com/a7923/athena-go/pkg/logger"
	"gitlab.com/a7923/athena-go/proto/exmsg/models"
	"gitlab.com/a7923/athena-go/proto/exmsg/services"
	"google.golang.org/protobuf/proto"
	"time"
)

const (
	LandingNotFoundId = "landing_not_found"
)

type LandingTracking struct {
	MarketingClient services.MarketingService
	AgsaleClient    services.AGSaleService
	RedisClient     *redis.Client
}

func (l *LandingTracking) GetName() string {
	return "landing"
}

func (l *LandingTracking) ListEventTracking() []string {
	return []string{
		"landing_identify",
	}
}

func (l *LandingTracking) SendTracking(ctx context.Context, tracking *models.Event) error {
	data := tracking.Metadata.AsMap()
	landingId := cast.ToString(data["landing_id"])
	adsId := cast.ToString(data["ads_id"])
	if len(adsId) < 1 || len(landingId) < 1 {
		logger.AthenaLogger.Warnw("Missing ads id or landing id")
		return nil
	}

	landing, err := l.getLandingById(ctx, landingId)
	if err != nil {
		logger.AthenaLogger.Errorw("Could not get landing by id",
			"landing_id", landingId,
			"error", err.Error(),
		)
		return err
	}

	if landing.Id == LandingNotFoundId {
		return nil
	}

	_, err = l.MarketingClient.CreateAdsBiz(ctx, &services.MarketingRequest{
		Adsbiz: &models.AdBiz{
			AdId:          adsId,
			CountryId:     landing.CountryId,
			ProjectId:     landing.ProjectId,
			CompanyId:     landing.CompanyId,
			ProductId:     landing.ProductId,
			LandingPageId: landingId,
		},
	})

	if err != nil {
		logger.AthenaLogger.Errorw("Could not insert landing map ads",
			"landing_id", landingId,
			"ads_id", adsId,
			"error", err.Error(),
		)
		return err
	}

	return nil
}

func (l *LandingTracking) getLandingById(ctx context.Context, id string) (*models.LandingPage, error) {
	cacheKey := fmt.Sprintf("landing_page_%s", id)
	fromCache, _ := l.RedisClient.Get(ctx, cacheKey).Bytes()
	if len(fromCache) > 0 {
		landing := &models.LandingPage{}
		_ = proto.Unmarshal(fromCache, landing)
		if len(landing.Id) > 0 {
			return landing, nil
		}
	}

	landing, err := l.AgsaleClient.GetLandingPages(ctx, &services.LandingPageRequest{
		Id: id,
	})
	if err != nil {
		logger.AthenaLogger.Errorw("Could not get landing page",
			"landing_id", id,
			"error", err.Error(),
		)
		return nil, err
	}

	res := &models.LandingPage{
		Id: LandingNotFoundId,
	}

	if len(landing.LandingPages) > 0 {
		res = landing.LandingPages[0]
	}

	cacheData, _ := proto.Marshal(res)
	l.RedisClient.Set(ctx, cacheKey, cacheData, 1*time.Hour)
	return res, nil
}
