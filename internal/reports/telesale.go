package reports

import (
	"fmt"
	"github.com/Masterminds/squirrel"
	"github.com/spf13/cast"
	"gitlab.com/a7923/athena-go/pkg/config"
	"gitlab.com/a7923/athena-go/pkg/utils"
	"gitlab.com/a7923/athena-go/proto/exmsg/models"
	"gitlab.com/a7923/athena-go/proto/exmsg/services"
	"strings"
)

type TelesaleReport struct{}

func addJoin(qb squirrel.SelectBuilder, req *services.AnalyticsRequest) squirrel.SelectBuilder {
	env := config.ViperGetStringWithDefault("env", "prod")
	switch req.GroupBy[0] {
	case DimensionMarketer:
		qb = qb.LeftJoin(fmt.Sprintf("identity_%v.users u ON u.id = marketer_id", env))
		qb = qb.Where(squirrel.Gt{"marketer_id": 0})
	case DimensionProduct:
		//qb = qb.LeftJoin(fmt.Sprintf("order_%v.order_products op ON op.order_id = o.order_id", env))
		//qb = qb.Where(squirrel.Gt{"product_id": 0})
	case DimensionProject:
		qb = qb.LeftJoin(fmt.Sprintf("identity_%v.projects p ON p.id = project_id", env))
		qb = qb.Where(squirrel.Gt{"project_id": 0})
	}

	// specific cases
	needJoinProducts := req.GroupBy[0] == DimensionProduct || len(req.ProductIds) > 0
	if needJoinProducts {
		qb = qb.LeftJoin(fmt.Sprintf("order_%v.order_products op ON op.order_id = o.order_id", env))
		qb = qb.Where(squirrel.Gt{"product_id": 0})
	}
	return qb
}

func (t *TelesaleReport) GetQuery(req *services.AnalyticsRequest) (squirrel.SelectBuilder, error) {
	selectColumns := make([]string, 0)
	groupBy := req.GroupBy[0]
	groupKey := groupByColumn[groupBy]

	selectColumns = append(selectColumns, groupColumns[groupBy]...)
	selectColumns = append(selectColumns, rev_columns...)
	selectColumns = append(selectColumns, spendingColumns...)

	qb := squirrel.
		Select(selectColumns...).
		From("base_orders o").
		LeftJoin("base_ads a ON a.ad_id = o.ad_id")

	qb = qb.
		Where(squirrel.Eq{"company_id": req.CompanyId}).
		Where(squirrel.GtOrEq{"ts": req.StartTime}).
		Where(squirrel.Lt{"ts": req.EndTime}).
		Where(squirrel.Eq{"team_in_charge": TeamInChargeTelesale}).
		Where(squirrel.Eq{"country_id": req.CountryId})

	qb = buildDimensionWhere(req, qb, false)
	qb = addJoin(qb, req)

	qb = qb.
		GroupBy(groupKey).
		OrderBy(req.OrderBy...).
		Limit(req.Limit).
		Offset(req.Offset)

	qb = qb.Prefix(t.buildWithCTE(req))

	qb = qb.Suffix("SETTINGS join_use_nulls = 0")
	return qb, nil
}

func (t *TelesaleReport) ProcessRow(row map[string]interface{}, req *services.AnalyticsRequest) (*models.Row, error) {
	return processRow(row, req)
}

func processRow(row map[string]interface{}, req *services.AnalyticsRequest) (*models.Row, error) {
	if row == nil {
		return nil, nil
	}

	if req.IsSummary {
		newRow := Insights{
			Revenue:     cast.ToFloat64(row["revenue"]),
			OrderCount:  cast.ToInt(row["total_orders"]),
			ReturnCount: cast.ToInt(row["return_count"]),
			LeadCount:   cast.ToInt(row["lead_count"]),
			Spent:       cast.ToFloat64(row["spent"]),
			MessCount:   cast.ToInt(row["mess_count"]),
		}
		return &models.Row{Values: utils.ToJSONByte(&TelesaleInsightsResponse{Insights: newRow})}, nil
	}

	row["close_rate"] = utils.ToPercentFixed2(row["total_orders"], row["mess_count"])
	row["spent_per_lead"] = utils.ToFixed2(row["spent"], row["lead_count"])
	row["ads_per_rev"] = utils.ToPercentFixed2(row["spent"], row["revenue"])
	row["ads_per_sale"] = utils.ToPercentFixed2(row["spent"], row["sale"])
	row["sale_per_order"] = utils.ToFixed2(row["sale"], row["total_orders"])
	row["spent_per_mess"] = utils.ToFixed2(row["spent"], row["mess_count"])
	row["name"] = row[req.GroupBy[0]]
	row["count"] = row["total_orders"]

	// remove unnecessary columns (nested columns)
	for k := range row {
		if strings.Contains(k, ".") {
			delete(row, k)
		}
	}

	return &models.Row{Values: utils.ToJSONByte(row)}, nil
}

func (t *TelesaleReport) buildWithCTE(req *services.AnalyticsRequest) string {
	predefinedColumns := []string{
		fmt.Sprintf(rateCTE, req.CompanyId, req.CountryId),
		"1 AS group_by_all",
	}

	//predefinedColumns = append(predefinedColumns, nameColumns...)
	//predefinedColumns = append(predefinedColumns, spendingColumns...)
	predefinedColumns = append(predefinedColumns, revColumns...)
	predefinedColumns = append(predefinedColumns, primaryColumns...)

	return fmt.Sprintf("WITH %s", strings.Join(predefinedColumns, ",\n"))
}
