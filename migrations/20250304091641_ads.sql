-- migrate:up
CREATE TABLE fb.ads (
    id BIGINT PRIMARY KEY,
    name TEXT,
    status TEXT,
    effective_status TEXT,
    page_id TEXT,
    object_story_id TEXT,
    object_story_spec JSONB,
    image_url TEXT,
    adset_id BIGINT,
    post_id TEXT,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ,
    deleted_at TIMESTAMPTZ
);

ALTER TABLE fb.ads
ADD CONSTRAINT fk_ads_adset FOREIGN KEY (adset_id) REFERENCES fb.adsets(id) ON DELETE CASCADE;


-- migrate:down
DROP INDEX IF EXISTS idx_ads_id;
ALTER TABLE fb.ads DROP CONSTRAINT IF EXISTS fk_ads_adset;
DROP TABLE IF EXISTS fb.ads;

