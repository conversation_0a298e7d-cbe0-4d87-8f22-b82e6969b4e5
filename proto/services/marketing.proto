syntax = "proto3";

package exmsg.services;

option go_package = "gitlab.com/a7923/athena-go/proto/exmsg/services;services";

import "models/marketing.proto";
import "models/common.proto";

service MarketingService {
    rpc CreateAdsBiz (MarketingRequest) returns (MarketingResponse);
}

message MarketingRequest {
  exmsg.models.Query query = 1;
  int64 id = 2;
  repeated int64 ids = 3;
  exmsg.models.AdBiz adsbiz = 4;
  repeated exmsg.models.AdBiz list_adsbiz = 5;
}

message MarketingResponse {
  repeated exmsg.models.Marketing marketing = 1;
  exmsg.models.SQLResult exec_result = 2;
}

