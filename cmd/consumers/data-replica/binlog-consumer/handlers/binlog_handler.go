package handlers

import (
	"github.com/ThreeDotsLabs/watermill/message"
	"gitlab.com/a7923/athena-go/pkg/app"
	"gitlab.com/a7923/athena-go/pkg/logger"
)

type BinlogHandler struct {
	Publisher app.PublisherInterface
	Name      string
}

func (b *BinlogHandler) HandleMessage(msg *message.Message) error {
	logger.AthenaLogger.Debug("Received ", string(msg.Payload))
	return b.Publisher.PublishSimple(
		b.Name,
		msg.Payload,
	)
}

func (b *BinlogHandler) Init() error {
	return nil
}

func (b *BinlogHandler) SetPublisher(p app.PublisherInterface) {
	b.Publisher = p
}

func (b *BinlogHandler) Close() {}

func (b *BinlogHandler) GetName() string {
	return b.Name
}
