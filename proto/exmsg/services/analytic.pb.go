// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.21.12
// source: services/analytic.proto

package services

import (
	models "gitlab.com/a7923/athena-go/proto/exmsg/models"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AnalyticsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ViewName      string                 `protobuf:"bytes,1,opt,name=view_name,json=viewName,proto3" json:"view_name,omitempty"`
	Fields        []string               `protobuf:"bytes,2,rep,name=fields,proto3" json:"fields,omitempty"`
	Conditions    []*models.Condition    `protobuf:"bytes,3,rep,name=conditions,proto3" json:"conditions,omitempty"`
	GroupBy       []string               `protobuf:"bytes,4,rep,name=group_by,json=groupBy,proto3" json:"group_by,omitempty"`
	OrderBy       []string               `protobuf:"bytes,5,rep,name=order_by,json=orderBy,proto3" json:"order_by,omitempty"`
	Limit         uint64                 `protobuf:"varint,6,opt,name=limit,proto3" json:"limit,omitempty"`
	Offset        uint64                 `protobuf:"varint,7,opt,name=offset,proto3" json:"offset,omitempty"`
	CompanyId     int64                  `protobuf:"varint,8,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	CountryId     int64                  `protobuf:"varint,9,opt,name=country_id,json=countryId,proto3" json:"country_id,omitempty"`
	StartTime     string                 `protobuf:"bytes,10,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime       string                 `protobuf:"bytes,11,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	TeamInCharge  int64                  `protobuf:"varint,12,opt,name=team_in_charge,json=teamInCharge,proto3" json:"team_in_charge,omitempty"`
	ProjectIds    []int64                `protobuf:"varint,13,rep,packed,name=project_ids,json=projectIds,proto3" json:"project_ids,omitempty"`
	MarketerIds   []int64                `protobuf:"varint,14,rep,packed,name=marketer_ids,json=marketerIds,proto3" json:"marketer_ids,omitempty"`
	ProductIds    []int64                `protobuf:"varint,15,rep,packed,name=product_ids,json=productIds,proto3" json:"product_ids,omitempty"`
	CampaignIds   []string               `protobuf:"bytes,16,rep,name=campaign_ids,json=campaignIds,proto3" json:"campaign_ids,omitempty"`
	AdaccountIds  []string               `protobuf:"bytes,17,rep,name=adaccount_ids,json=adaccountIds,proto3" json:"adaccount_ids,omitempty"`
	AdsetIds      []string               `protobuf:"bytes,18,rep,name=adset_ids,json=adsetIds,proto3" json:"adset_ids,omitempty"`
	IsSummary     bool                   `protobuf:"varint,19,opt,name=is_summary,json=isSummary,proto3" json:"is_summary,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AnalyticsRequest) Reset() {
	*x = AnalyticsRequest{}
	mi := &file_services_analytic_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AnalyticsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnalyticsRequest) ProtoMessage() {}

func (x *AnalyticsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_analytic_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnalyticsRequest.ProtoReflect.Descriptor instead.
func (*AnalyticsRequest) Descriptor() ([]byte, []int) {
	return file_services_analytic_proto_rawDescGZIP(), []int{0}
}

func (x *AnalyticsRequest) GetViewName() string {
	if x != nil {
		return x.ViewName
	}
	return ""
}

func (x *AnalyticsRequest) GetFields() []string {
	if x != nil {
		return x.Fields
	}
	return nil
}

func (x *AnalyticsRequest) GetConditions() []*models.Condition {
	if x != nil {
		return x.Conditions
	}
	return nil
}

func (x *AnalyticsRequest) GetGroupBy() []string {
	if x != nil {
		return x.GroupBy
	}
	return nil
}

func (x *AnalyticsRequest) GetOrderBy() []string {
	if x != nil {
		return x.OrderBy
	}
	return nil
}

func (x *AnalyticsRequest) GetLimit() uint64 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *AnalyticsRequest) GetOffset() uint64 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *AnalyticsRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *AnalyticsRequest) GetCountryId() int64 {
	if x != nil {
		return x.CountryId
	}
	return 0
}

func (x *AnalyticsRequest) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *AnalyticsRequest) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

func (x *AnalyticsRequest) GetTeamInCharge() int64 {
	if x != nil {
		return x.TeamInCharge
	}
	return 0
}

func (x *AnalyticsRequest) GetProjectIds() []int64 {
	if x != nil {
		return x.ProjectIds
	}
	return nil
}

func (x *AnalyticsRequest) GetMarketerIds() []int64 {
	if x != nil {
		return x.MarketerIds
	}
	return nil
}

func (x *AnalyticsRequest) GetProductIds() []int64 {
	if x != nil {
		return x.ProductIds
	}
	return nil
}

func (x *AnalyticsRequest) GetCampaignIds() []string {
	if x != nil {
		return x.CampaignIds
	}
	return nil
}

func (x *AnalyticsRequest) GetAdaccountIds() []string {
	if x != nil {
		return x.AdaccountIds
	}
	return nil
}

func (x *AnalyticsRequest) GetAdsetIds() []string {
	if x != nil {
		return x.AdsetIds
	}
	return nil
}

func (x *AnalyticsRequest) GetIsSummary() bool {
	if x != nil {
		return x.IsSummary
	}
	return false
}

type AnalyticsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          []*models.Row          `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	Error         string                 `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AnalyticsResponse) Reset() {
	*x = AnalyticsResponse{}
	mi := &file_services_analytic_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AnalyticsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnalyticsResponse) ProtoMessage() {}

func (x *AnalyticsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_analytic_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnalyticsResponse.ProtoReflect.Descriptor instead.
func (*AnalyticsResponse) Descriptor() ([]byte, []int) {
	return file_services_analytic_proto_rawDescGZIP(), []int{1}
}

func (x *AnalyticsResponse) GetData() []*models.Row {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *AnalyticsResponse) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

type OrderDashboardRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StartTime     string                 `protobuf:"bytes,1,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime       string                 `protobuf:"bytes,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	ProjectIds    []int64                `protobuf:"varint,3,rep,packed,name=project_ids,json=projectIds,proto3" json:"project_ids,omitempty"`
	SaleReps      []int64                `protobuf:"varint,4,rep,packed,name=sale_reps,json=saleReps,proto3" json:"sale_reps,omitempty"`
	ProductIds    []int64                `protobuf:"varint,5,rep,packed,name=product_ids,json=productIds,proto3" json:"product_ids,omitempty"`
	MarketerIds   []int64                `protobuf:"varint,6,rep,packed,name=marketer_ids,json=marketerIds,proto3" json:"marketer_ids,omitempty"`
	Sources       []string               `protobuf:"bytes,7,rep,name=sources,proto3" json:"sources,omitempty"`
	CarriersCode  []string               `protobuf:"bytes,8,rep,name=carriers_code,json=carriersCode,proto3" json:"carriers_code,omitempty"`
	TagIds        []int64                `protobuf:"varint,9,rep,packed,name=tag_ids,json=tagIds,proto3" json:"tag_ids,omitempty"`
	CompanyId     int64                  `protobuf:"varint,10,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	CountryId     int64                  `protobuf:"varint,11,opt,name=country_id,json=countryId,proto3" json:"country_id,omitempty"`
	GroupBy       []string               `protobuf:"bytes,12,rep,name=group_by,json=groupBy,proto3" json:"group_by,omitempty"`
	OrderBy       []string               `protobuf:"bytes,13,rep,name=order_by,json=orderBy,proto3" json:"order_by,omitempty"`
	Limit         uint64                 `protobuf:"varint,14,opt,name=limit,proto3" json:"limit,omitempty"`
	Offset        uint64                 `protobuf:"varint,15,opt,name=offset,proto3" json:"offset,omitempty"`
	DateRangeType string                 `protobuf:"bytes,16,opt,name=date_range_type,json=dateRangeType,proto3" json:"date_range_type,omitempty"`
	IsViewDetail  bool                   `protobuf:"varint,17,opt,name=is_view_detail,json=isViewDetail,proto3" json:"is_view_detail,omitempty"`
	Action        string                 `protobuf:"bytes,18,opt,name=action,proto3" json:"action,omitempty"`
	SaleId        []int64                `protobuf:"varint,19,rep,packed,name=sale_id,json=saleId,proto3" json:"sale_id,omitempty"`
	CarePageId    []int64                `protobuf:"varint,20,rep,packed,name=care_page_id,json=carePageId,proto3" json:"care_page_id,omitempty"`
	TeamInCharge  []int64                `protobuf:"varint,21,rep,packed,name=team_in_charge,json=teamInCharge,proto3" json:"team_in_charge,omitempty"`
	OrderStatus   []int64                `protobuf:"varint,22,rep,packed,name=order_status,json=orderStatus,proto3" json:"order_status,omitempty"`
	Dids          []int64                `protobuf:"varint,23,rep,packed,name=dids,proto3" json:"dids,omitempty"`
	Departments   []string               `protobuf:"bytes,24,rep,name=departments,proto3" json:"departments,omitempty"`
	UserIdLogin   int64                  `protobuf:"varint,25,opt,name=user_id_login,json=userIdLogin,proto3" json:"user_id_login,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OrderDashboardRequest) Reset() {
	*x = OrderDashboardRequest{}
	mi := &file_services_analytic_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OrderDashboardRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderDashboardRequest) ProtoMessage() {}

func (x *OrderDashboardRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_analytic_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderDashboardRequest.ProtoReflect.Descriptor instead.
func (*OrderDashboardRequest) Descriptor() ([]byte, []int) {
	return file_services_analytic_proto_rawDescGZIP(), []int{2}
}

func (x *OrderDashboardRequest) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *OrderDashboardRequest) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

func (x *OrderDashboardRequest) GetProjectIds() []int64 {
	if x != nil {
		return x.ProjectIds
	}
	return nil
}

func (x *OrderDashboardRequest) GetSaleReps() []int64 {
	if x != nil {
		return x.SaleReps
	}
	return nil
}

func (x *OrderDashboardRequest) GetProductIds() []int64 {
	if x != nil {
		return x.ProductIds
	}
	return nil
}

func (x *OrderDashboardRequest) GetMarketerIds() []int64 {
	if x != nil {
		return x.MarketerIds
	}
	return nil
}

func (x *OrderDashboardRequest) GetSources() []string {
	if x != nil {
		return x.Sources
	}
	return nil
}

func (x *OrderDashboardRequest) GetCarriersCode() []string {
	if x != nil {
		return x.CarriersCode
	}
	return nil
}

func (x *OrderDashboardRequest) GetTagIds() []int64 {
	if x != nil {
		return x.TagIds
	}
	return nil
}

func (x *OrderDashboardRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *OrderDashboardRequest) GetCountryId() int64 {
	if x != nil {
		return x.CountryId
	}
	return 0
}

func (x *OrderDashboardRequest) GetGroupBy() []string {
	if x != nil {
		return x.GroupBy
	}
	return nil
}

func (x *OrderDashboardRequest) GetOrderBy() []string {
	if x != nil {
		return x.OrderBy
	}
	return nil
}

func (x *OrderDashboardRequest) GetLimit() uint64 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *OrderDashboardRequest) GetOffset() uint64 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *OrderDashboardRequest) GetDateRangeType() string {
	if x != nil {
		return x.DateRangeType
	}
	return ""
}

func (x *OrderDashboardRequest) GetIsViewDetail() bool {
	if x != nil {
		return x.IsViewDetail
	}
	return false
}

func (x *OrderDashboardRequest) GetAction() string {
	if x != nil {
		return x.Action
	}
	return ""
}

func (x *OrderDashboardRequest) GetSaleId() []int64 {
	if x != nil {
		return x.SaleId
	}
	return nil
}

func (x *OrderDashboardRequest) GetCarePageId() []int64 {
	if x != nil {
		return x.CarePageId
	}
	return nil
}

func (x *OrderDashboardRequest) GetTeamInCharge() []int64 {
	if x != nil {
		return x.TeamInCharge
	}
	return nil
}

func (x *OrderDashboardRequest) GetOrderStatus() []int64 {
	if x != nil {
		return x.OrderStatus
	}
	return nil
}

func (x *OrderDashboardRequest) GetDids() []int64 {
	if x != nil {
		return x.Dids
	}
	return nil
}

func (x *OrderDashboardRequest) GetDepartments() []string {
	if x != nil {
		return x.Departments
	}
	return nil
}

func (x *OrderDashboardRequest) GetUserIdLogin() int64 {
	if x != nil {
		return x.UserIdLogin
	}
	return 0
}

type TelesaleDashboardRequest struct {
	state                    protoimpl.MessageState `protogen:"open.v1"`
	StartTime                string                 `protobuf:"bytes,1,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime                  string                 `protobuf:"bytes,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	ProjectIds               []int64                `protobuf:"varint,3,rep,packed,name=project_ids,json=projectIds,proto3" json:"project_ids,omitempty"`
	SaleReps                 []int64                `protobuf:"varint,4,rep,packed,name=sale_reps,json=saleReps,proto3" json:"sale_reps,omitempty"`
	ProductIds               []int64                `protobuf:"varint,5,rep,packed,name=product_ids,json=productIds,proto3" json:"product_ids,omitempty"`
	MarketerIds              []int64                `protobuf:"varint,6,rep,packed,name=marketer_ids,json=marketerIds,proto3" json:"marketer_ids,omitempty"`
	Sources                  []string               `protobuf:"bytes,7,rep,name=sources,proto3" json:"sources,omitempty"`
	CarriersCode             []string               `protobuf:"bytes,8,rep,name=carriers_code,json=carriersCode,proto3" json:"carriers_code,omitempty"`
	TagIds                   []int64                `protobuf:"varint,9,rep,packed,name=tag_ids,json=tagIds,proto3" json:"tag_ids,omitempty"`
	CompanyId                int64                  `protobuf:"varint,10,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	CountryId                int64                  `protobuf:"varint,11,opt,name=country_id,json=countryId,proto3" json:"country_id,omitempty"`
	GroupBy                  []string               `protobuf:"bytes,12,rep,name=group_by,json=groupBy,proto3" json:"group_by,omitempty"`
	OrderBy                  []string               `protobuf:"bytes,13,rep,name=order_by,json=orderBy,proto3" json:"order_by,omitempty"`
	Limit                    uint64                 `protobuf:"varint,14,opt,name=limit,proto3" json:"limit,omitempty"`
	Offset                   uint64                 `protobuf:"varint,15,opt,name=offset,proto3" json:"offset,omitempty"`
	DateRangeType            string                 `protobuf:"bytes,16,opt,name=date_range_type,json=dateRangeType,proto3" json:"date_range_type,omitempty"`
	IsViewDetail             bool                   `protobuf:"varint,17,opt,name=is_view_detail,json=isViewDetail,proto3" json:"is_view_detail,omitempty"`
	Action                   string                 `protobuf:"bytes,18,opt,name=action,proto3" json:"action,omitempty"`
	SaleId                   []int64                `protobuf:"varint,19,rep,packed,name=sale_id,json=saleId,proto3" json:"sale_id,omitempty"`
	CarePageId               []int64                `protobuf:"varint,20,rep,packed,name=care_page_id,json=carePageId,proto3" json:"care_page_id,omitempty"`
	TeamInCharge             []int64                `protobuf:"varint,21,rep,packed,name=team_in_charge,json=teamInCharge,proto3" json:"team_in_charge,omitempty"`
	OrderStatus              []int64                `protobuf:"varint,22,rep,packed,name=order_status,json=orderStatus,proto3" json:"order_status,omitempty"`
	Dids                     []int64                `protobuf:"varint,23,rep,packed,name=dids,proto3" json:"dids,omitempty"`
	CollectMethod            []string               `protobuf:"bytes,24,rep,name=collect_method,json=collectMethod,proto3" json:"collect_method,omitempty"`
	CareState                []int64                `protobuf:"varint,25,rep,packed,name=care_state,json=careState,proto3" json:"care_state,omitempty"`
	TimeZone                 string                 `protobuf:"bytes,26,opt,name=time_zone,json=timeZone,proto3" json:"time_zone,omitempty"`
	LastCareReasonId         []int64                `protobuf:"varint,27,rep,packed,name=last_care_reason_id,json=lastCareReasonId,proto3" json:"last_care_reason_id,omitempty"`
	IsViewDetailByCareReason bool                   `protobuf:"varint,28,opt,name=is_view_detail_by_care_reason,json=isViewDetailByCareReason,proto3" json:"is_view_detail_by_care_reason,omitempty"`
	IsViewToday              bool                   `protobuf:"varint,29,opt,name=is_view_today,json=isViewToday,proto3" json:"is_view_today,omitempty"`
	IsViewPerformance        bool                   `protobuf:"varint,30,opt,name=is_view_performance,json=isViewPerformance,proto3" json:"is_view_performance,omitempty"`
	unknownFields            protoimpl.UnknownFields
	sizeCache                protoimpl.SizeCache
}

func (x *TelesaleDashboardRequest) Reset() {
	*x = TelesaleDashboardRequest{}
	mi := &file_services_analytic_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TelesaleDashboardRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TelesaleDashboardRequest) ProtoMessage() {}

func (x *TelesaleDashboardRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_analytic_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TelesaleDashboardRequest.ProtoReflect.Descriptor instead.
func (*TelesaleDashboardRequest) Descriptor() ([]byte, []int) {
	return file_services_analytic_proto_rawDescGZIP(), []int{3}
}

func (x *TelesaleDashboardRequest) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *TelesaleDashboardRequest) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

func (x *TelesaleDashboardRequest) GetProjectIds() []int64 {
	if x != nil {
		return x.ProjectIds
	}
	return nil
}

func (x *TelesaleDashboardRequest) GetSaleReps() []int64 {
	if x != nil {
		return x.SaleReps
	}
	return nil
}

func (x *TelesaleDashboardRequest) GetProductIds() []int64 {
	if x != nil {
		return x.ProductIds
	}
	return nil
}

func (x *TelesaleDashboardRequest) GetMarketerIds() []int64 {
	if x != nil {
		return x.MarketerIds
	}
	return nil
}

func (x *TelesaleDashboardRequest) GetSources() []string {
	if x != nil {
		return x.Sources
	}
	return nil
}

func (x *TelesaleDashboardRequest) GetCarriersCode() []string {
	if x != nil {
		return x.CarriersCode
	}
	return nil
}

func (x *TelesaleDashboardRequest) GetTagIds() []int64 {
	if x != nil {
		return x.TagIds
	}
	return nil
}

func (x *TelesaleDashboardRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *TelesaleDashboardRequest) GetCountryId() int64 {
	if x != nil {
		return x.CountryId
	}
	return 0
}

func (x *TelesaleDashboardRequest) GetGroupBy() []string {
	if x != nil {
		return x.GroupBy
	}
	return nil
}

func (x *TelesaleDashboardRequest) GetOrderBy() []string {
	if x != nil {
		return x.OrderBy
	}
	return nil
}

func (x *TelesaleDashboardRequest) GetLimit() uint64 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *TelesaleDashboardRequest) GetOffset() uint64 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *TelesaleDashboardRequest) GetDateRangeType() string {
	if x != nil {
		return x.DateRangeType
	}
	return ""
}

func (x *TelesaleDashboardRequest) GetIsViewDetail() bool {
	if x != nil {
		return x.IsViewDetail
	}
	return false
}

func (x *TelesaleDashboardRequest) GetAction() string {
	if x != nil {
		return x.Action
	}
	return ""
}

func (x *TelesaleDashboardRequest) GetSaleId() []int64 {
	if x != nil {
		return x.SaleId
	}
	return nil
}

func (x *TelesaleDashboardRequest) GetCarePageId() []int64 {
	if x != nil {
		return x.CarePageId
	}
	return nil
}

func (x *TelesaleDashboardRequest) GetTeamInCharge() []int64 {
	if x != nil {
		return x.TeamInCharge
	}
	return nil
}

func (x *TelesaleDashboardRequest) GetOrderStatus() []int64 {
	if x != nil {
		return x.OrderStatus
	}
	return nil
}

func (x *TelesaleDashboardRequest) GetDids() []int64 {
	if x != nil {
		return x.Dids
	}
	return nil
}

func (x *TelesaleDashboardRequest) GetCollectMethod() []string {
	if x != nil {
		return x.CollectMethod
	}
	return nil
}

func (x *TelesaleDashboardRequest) GetCareState() []int64 {
	if x != nil {
		return x.CareState
	}
	return nil
}

func (x *TelesaleDashboardRequest) GetTimeZone() string {
	if x != nil {
		return x.TimeZone
	}
	return ""
}

func (x *TelesaleDashboardRequest) GetLastCareReasonId() []int64 {
	if x != nil {
		return x.LastCareReasonId
	}
	return nil
}

func (x *TelesaleDashboardRequest) GetIsViewDetailByCareReason() bool {
	if x != nil {
		return x.IsViewDetailByCareReason
	}
	return false
}

func (x *TelesaleDashboardRequest) GetIsViewToday() bool {
	if x != nil {
		return x.IsViewToday
	}
	return false
}

func (x *TelesaleDashboardRequest) GetIsViewPerformance() bool {
	if x != nil {
		return x.IsViewPerformance
	}
	return false
}

var File_services_analytic_proto protoreflect.FileDescriptor

const file_services_analytic_proto_rawDesc = "" +
	"\n" +
	"\x17services/analytic.proto\x12\x0eexmsg.services\x1a\x13models/common.proto\"\xeb\x04\n" +
	"\x10AnalyticsRequest\x12\x1b\n" +
	"\tview_name\x18\x01 \x01(\tR\bviewName\x12\x16\n" +
	"\x06fields\x18\x02 \x03(\tR\x06fields\x127\n" +
	"\n" +
	"conditions\x18\x03 \x03(\v2\x17.exmsg.models.ConditionR\n" +
	"conditions\x12\x19\n" +
	"\bgroup_by\x18\x04 \x03(\tR\agroupBy\x12\x19\n" +
	"\border_by\x18\x05 \x03(\tR\aorderBy\x12\x14\n" +
	"\x05limit\x18\x06 \x01(\x04R\x05limit\x12\x16\n" +
	"\x06offset\x18\a \x01(\x04R\x06offset\x12\x1d\n" +
	"\n" +
	"company_id\x18\b \x01(\x03R\tcompanyId\x12\x1d\n" +
	"\n" +
	"country_id\x18\t \x01(\x03R\tcountryId\x12\x1d\n" +
	"\n" +
	"start_time\x18\n" +
	" \x01(\tR\tstartTime\x12\x19\n" +
	"\bend_time\x18\v \x01(\tR\aendTime\x12$\n" +
	"\x0eteam_in_charge\x18\f \x01(\x03R\fteamInCharge\x12\x1f\n" +
	"\vproject_ids\x18\r \x03(\x03R\n" +
	"projectIds\x12!\n" +
	"\fmarketer_ids\x18\x0e \x03(\x03R\vmarketerIds\x12\x1f\n" +
	"\vproduct_ids\x18\x0f \x03(\x03R\n" +
	"productIds\x12!\n" +
	"\fcampaign_ids\x18\x10 \x03(\tR\vcampaignIds\x12#\n" +
	"\radaccount_ids\x18\x11 \x03(\tR\fadaccountIds\x12\x1b\n" +
	"\tadset_ids\x18\x12 \x03(\tR\badsetIds\x12\x1d\n" +
	"\n" +
	"is_summary\x18\x13 \x01(\bR\tisSummary\"P\n" +
	"\x11AnalyticsResponse\x12%\n" +
	"\x04data\x18\x01 \x03(\v2\x11.exmsg.models.RowR\x04data\x12\x14\n" +
	"\x05error\x18\x02 \x01(\tR\x05error\"\x91\x06\n" +
	"\x15OrderDashboardRequest\x12\x1d\n" +
	"\n" +
	"start_time\x18\x01 \x01(\tR\tstartTime\x12\x19\n" +
	"\bend_time\x18\x02 \x01(\tR\aendTime\x12\x1f\n" +
	"\vproject_ids\x18\x03 \x03(\x03R\n" +
	"projectIds\x12\x1b\n" +
	"\tsale_reps\x18\x04 \x03(\x03R\bsaleReps\x12\x1f\n" +
	"\vproduct_ids\x18\x05 \x03(\x03R\n" +
	"productIds\x12!\n" +
	"\fmarketer_ids\x18\x06 \x03(\x03R\vmarketerIds\x12\x18\n" +
	"\asources\x18\a \x03(\tR\asources\x12#\n" +
	"\rcarriers_code\x18\b \x03(\tR\fcarriersCode\x12\x17\n" +
	"\atag_ids\x18\t \x03(\x03R\x06tagIds\x12\x1d\n" +
	"\n" +
	"company_id\x18\n" +
	" \x01(\x03R\tcompanyId\x12\x1d\n" +
	"\n" +
	"country_id\x18\v \x01(\x03R\tcountryId\x12\x19\n" +
	"\bgroup_by\x18\f \x03(\tR\agroupBy\x12\x19\n" +
	"\border_by\x18\r \x03(\tR\aorderBy\x12\x14\n" +
	"\x05limit\x18\x0e \x01(\x04R\x05limit\x12\x16\n" +
	"\x06offset\x18\x0f \x01(\x04R\x06offset\x12&\n" +
	"\x0fdate_range_type\x18\x10 \x01(\tR\rdateRangeType\x12$\n" +
	"\x0eis_view_detail\x18\x11 \x01(\bR\fisViewDetail\x12\x16\n" +
	"\x06action\x18\x12 \x01(\tR\x06action\x12\x17\n" +
	"\asale_id\x18\x13 \x03(\x03R\x06saleId\x12 \n" +
	"\fcare_page_id\x18\x14 \x03(\x03R\n" +
	"carePageId\x12$\n" +
	"\x0eteam_in_charge\x18\x15 \x03(\x03R\fteamInCharge\x12!\n" +
	"\forder_status\x18\x16 \x03(\x03R\vorderStatus\x12\x12\n" +
	"\x04dids\x18\x17 \x03(\x03R\x04dids\x12 \n" +
	"\vdepartments\x18\x18 \x03(\tR\vdepartments\x12\"\n" +
	"\ruser_id_login\x18\x19 \x01(\x03R\vuserIdLogin\"\xf5\a\n" +
	"\x18TelesaleDashboardRequest\x12\x1d\n" +
	"\n" +
	"start_time\x18\x01 \x01(\tR\tstartTime\x12\x19\n" +
	"\bend_time\x18\x02 \x01(\tR\aendTime\x12\x1f\n" +
	"\vproject_ids\x18\x03 \x03(\x03R\n" +
	"projectIds\x12\x1b\n" +
	"\tsale_reps\x18\x04 \x03(\x03R\bsaleReps\x12\x1f\n" +
	"\vproduct_ids\x18\x05 \x03(\x03R\n" +
	"productIds\x12!\n" +
	"\fmarketer_ids\x18\x06 \x03(\x03R\vmarketerIds\x12\x18\n" +
	"\asources\x18\a \x03(\tR\asources\x12#\n" +
	"\rcarriers_code\x18\b \x03(\tR\fcarriersCode\x12\x17\n" +
	"\atag_ids\x18\t \x03(\x03R\x06tagIds\x12\x1d\n" +
	"\n" +
	"company_id\x18\n" +
	" \x01(\x03R\tcompanyId\x12\x1d\n" +
	"\n" +
	"country_id\x18\v \x01(\x03R\tcountryId\x12\x19\n" +
	"\bgroup_by\x18\f \x03(\tR\agroupBy\x12\x19\n" +
	"\border_by\x18\r \x03(\tR\aorderBy\x12\x14\n" +
	"\x05limit\x18\x0e \x01(\x04R\x05limit\x12\x16\n" +
	"\x06offset\x18\x0f \x01(\x04R\x06offset\x12&\n" +
	"\x0fdate_range_type\x18\x10 \x01(\tR\rdateRangeType\x12$\n" +
	"\x0eis_view_detail\x18\x11 \x01(\bR\fisViewDetail\x12\x16\n" +
	"\x06action\x18\x12 \x01(\tR\x06action\x12\x17\n" +
	"\asale_id\x18\x13 \x03(\x03R\x06saleId\x12 \n" +
	"\fcare_page_id\x18\x14 \x03(\x03R\n" +
	"carePageId\x12$\n" +
	"\x0eteam_in_charge\x18\x15 \x03(\x03R\fteamInCharge\x12!\n" +
	"\forder_status\x18\x16 \x03(\x03R\vorderStatus\x12\x12\n" +
	"\x04dids\x18\x17 \x03(\x03R\x04dids\x12%\n" +
	"\x0ecollect_method\x18\x18 \x03(\tR\rcollectMethod\x12\x1d\n" +
	"\n" +
	"care_state\x18\x19 \x03(\x03R\tcareState\x12\x1b\n" +
	"\ttime_zone\x18\x1a \x01(\tR\btimeZone\x12-\n" +
	"\x13last_care_reason_id\x18\x1b \x03(\x03R\x10lastCareReasonId\x12?\n" +
	"\x1dis_view_detail_by_care_reason\x18\x1c \x01(\bR\x18isViewDetailByCareReason\x12\"\n" +
	"\ris_view_today\x18\x1d \x01(\bR\visViewToday\x12.\n" +
	"\x13is_view_performance\x18\x1e \x01(\bR\x11isViewPerformance2\x8b\x03\n" +
	"\x0fAnalyticService\x12R\n" +
	"\vQueryReport\x12 .exmsg.services.AnalyticsRequest\x1a!.exmsg.services.AnalyticsResponse\x12L\n" +
	"\x05Query\x12 .exmsg.services.AnalyticsRequest\x1a!.exmsg.services.AnalyticsResponse\x12g\n" +
	"\x1bQueryOrderDashboardOverview\x12%.exmsg.services.OrderDashboardRequest\x1a!.exmsg.services.AnalyticsResponse\x12m\n" +
	"\x1eQueryTelesaleDashboardOverview\x12(.exmsg.services.TelesaleDashboardRequest\x1a!.exmsg.services.AnalyticsResponseB:Z8gitlab.com/a7923/athena-go/proto/exmsg/services;servicesb\x06proto3"

var (
	file_services_analytic_proto_rawDescOnce sync.Once
	file_services_analytic_proto_rawDescData []byte
)

func file_services_analytic_proto_rawDescGZIP() []byte {
	file_services_analytic_proto_rawDescOnce.Do(func() {
		file_services_analytic_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_services_analytic_proto_rawDesc), len(file_services_analytic_proto_rawDesc)))
	})
	return file_services_analytic_proto_rawDescData
}

var file_services_analytic_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_services_analytic_proto_goTypes = []any{
	(*AnalyticsRequest)(nil),         // 0: exmsg.services.AnalyticsRequest
	(*AnalyticsResponse)(nil),        // 1: exmsg.services.AnalyticsResponse
	(*OrderDashboardRequest)(nil),    // 2: exmsg.services.OrderDashboardRequest
	(*TelesaleDashboardRequest)(nil), // 3: exmsg.services.TelesaleDashboardRequest
	(*models.Condition)(nil),         // 4: exmsg.models.Condition
	(*models.Row)(nil),               // 5: exmsg.models.Row
}
var file_services_analytic_proto_depIdxs = []int32{
	4, // 0: exmsg.services.AnalyticsRequest.conditions:type_name -> exmsg.models.Condition
	5, // 1: exmsg.services.AnalyticsResponse.data:type_name -> exmsg.models.Row
	0, // 2: exmsg.services.AnalyticService.QueryReport:input_type -> exmsg.services.AnalyticsRequest
	0, // 3: exmsg.services.AnalyticService.Query:input_type -> exmsg.services.AnalyticsRequest
	2, // 4: exmsg.services.AnalyticService.QueryOrderDashboardOverview:input_type -> exmsg.services.OrderDashboardRequest
	3, // 5: exmsg.services.AnalyticService.QueryTelesaleDashboardOverview:input_type -> exmsg.services.TelesaleDashboardRequest
	1, // 6: exmsg.services.AnalyticService.QueryReport:output_type -> exmsg.services.AnalyticsResponse
	1, // 7: exmsg.services.AnalyticService.Query:output_type -> exmsg.services.AnalyticsResponse
	1, // 8: exmsg.services.AnalyticService.QueryOrderDashboardOverview:output_type -> exmsg.services.AnalyticsResponse
	1, // 9: exmsg.services.AnalyticService.QueryTelesaleDashboardOverview:output_type -> exmsg.services.AnalyticsResponse
	6, // [6:10] is the sub-list for method output_type
	2, // [2:6] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_services_analytic_proto_init() }
func file_services_analytic_proto_init() {
	if File_services_analytic_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_services_analytic_proto_rawDesc), len(file_services_analytic_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_services_analytic_proto_goTypes,
		DependencyIndexes: file_services_analytic_proto_depIdxs,
		MessageInfos:      file_services_analytic_proto_msgTypes,
	}.Build()
	File_services_analytic_proto = out.File
	file_services_analytic_proto_goTypes = nil
	file_services_analytic_proto_depIdxs = nil
}
