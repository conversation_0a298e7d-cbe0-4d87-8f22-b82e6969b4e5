// Code generated by protoc-gen-micro. DO NOT EDIT.
// source: services/agsale.proto

package services

import (
	fmt "fmt"
	_ "gitlab.com/a7923/athena-go/proto/exmsg/models"
	proto "google.golang.org/protobuf/proto"
	math "math"
)

import (
	context "context"
	client "go-micro.dev/v5/client"
	server "go-micro.dev/v5/server"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ client.Option
var _ server.Option

// Client API for AGSaleService service

type AGSaleService interface {
	CreateLead(ctx context.Context, in *LeadRequest, opts ...client.CallOption) (*LeadResponse, error)
	GetLeads(ctx context.Context, in *LeadRequest, opts ...client.CallOption) (*LeadResponse, error)
	GetOrders(ctx context.Context, in *OrderRequest, opts ...client.CallOption) (*OrderResponse, error)
	CreateOrders(ctx context.Context, in *OrderRequest, opts ...client.CallOption) (*OrderResponse, error)
	UpdateLead(ctx context.Context, in *LeadRequest, opts ...client.CallOption) (*LeadResponse, error)
	CreateLeadCareAndItem(ctx context.Context, in *LeadRequest, opts ...client.CallOption) (*LeadResponse, error)
	GetCareReasons(ctx context.Context, in *CareReasonRequest, opts ...client.CallOption) (*CareReasonResponse, error)
	GetOrderProducts(ctx context.Context, in *OrderRequest, opts ...client.CallOption) (*OrderResponse, error)
	GetLandingPages(ctx context.Context, in *LandingPageRequest, opts ...client.CallOption) (*LandingPageResponse, error)
	GetOrderDisplayId(ctx context.Context, in *OrderDisplayIdRequest, opts ...client.CallOption) (*OrderResponse, error)
	UpdateOrder(ctx context.Context, in *OrderRequest, opts ...client.CallOption) (*OrderResponse, error)
	RemoveDuplicateOrder(ctx context.Context, in *OrderRequest, opts ...client.CallOption) (*OrderResponse, error)
	RemoveDuplicateLead(ctx context.Context, in *LeadRequest, opts ...client.CallOption) (*LeadResponse, error)
}

type aGSaleService struct {
	c    client.Client
	name string
}

func NewAGSaleService(name string, c client.Client) AGSaleService {
	return &aGSaleService{
		c:    c,
		name: name,
	}
}

func (c *aGSaleService) CreateLead(ctx context.Context, in *LeadRequest, opts ...client.CallOption) (*LeadResponse, error) {
	req := c.c.NewRequest(c.name, "AGSaleService.CreateLead", in)
	out := new(LeadResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aGSaleService) GetLeads(ctx context.Context, in *LeadRequest, opts ...client.CallOption) (*LeadResponse, error) {
	req := c.c.NewRequest(c.name, "AGSaleService.GetLeads", in)
	out := new(LeadResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aGSaleService) GetOrders(ctx context.Context, in *OrderRequest, opts ...client.CallOption) (*OrderResponse, error) {
	req := c.c.NewRequest(c.name, "AGSaleService.GetOrders", in)
	out := new(OrderResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aGSaleService) CreateOrders(ctx context.Context, in *OrderRequest, opts ...client.CallOption) (*OrderResponse, error) {
	req := c.c.NewRequest(c.name, "AGSaleService.CreateOrders", in)
	out := new(OrderResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aGSaleService) UpdateLead(ctx context.Context, in *LeadRequest, opts ...client.CallOption) (*LeadResponse, error) {
	req := c.c.NewRequest(c.name, "AGSaleService.UpdateLead", in)
	out := new(LeadResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aGSaleService) CreateLeadCareAndItem(ctx context.Context, in *LeadRequest, opts ...client.CallOption) (*LeadResponse, error) {
	req := c.c.NewRequest(c.name, "AGSaleService.CreateLeadCareAndItem", in)
	out := new(LeadResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aGSaleService) GetCareReasons(ctx context.Context, in *CareReasonRequest, opts ...client.CallOption) (*CareReasonResponse, error) {
	req := c.c.NewRequest(c.name, "AGSaleService.GetCareReasons", in)
	out := new(CareReasonResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aGSaleService) GetOrderProducts(ctx context.Context, in *OrderRequest, opts ...client.CallOption) (*OrderResponse, error) {
	req := c.c.NewRequest(c.name, "AGSaleService.GetOrderProducts", in)
	out := new(OrderResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aGSaleService) GetLandingPages(ctx context.Context, in *LandingPageRequest, opts ...client.CallOption) (*LandingPageResponse, error) {
	req := c.c.NewRequest(c.name, "AGSaleService.GetLandingPages", in)
	out := new(LandingPageResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aGSaleService) GetOrderDisplayId(ctx context.Context, in *OrderDisplayIdRequest, opts ...client.CallOption) (*OrderResponse, error) {
	req := c.c.NewRequest(c.name, "AGSaleService.GetOrderDisplayId", in)
	out := new(OrderResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aGSaleService) UpdateOrder(ctx context.Context, in *OrderRequest, opts ...client.CallOption) (*OrderResponse, error) {
	req := c.c.NewRequest(c.name, "AGSaleService.UpdateOrder", in)
	out := new(OrderResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aGSaleService) RemoveDuplicateOrder(ctx context.Context, in *OrderRequest, opts ...client.CallOption) (*OrderResponse, error) {
	req := c.c.NewRequest(c.name, "AGSaleService.RemoveDuplicateOrder", in)
	out := new(OrderResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aGSaleService) RemoveDuplicateLead(ctx context.Context, in *LeadRequest, opts ...client.CallOption) (*LeadResponse, error) {
	req := c.c.NewRequest(c.name, "AGSaleService.RemoveDuplicateLead", in)
	out := new(LeadResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for AGSaleService service

type AGSaleServiceHandler interface {
	CreateLead(context.Context, *LeadRequest, *LeadResponse) error
	GetLeads(context.Context, *LeadRequest, *LeadResponse) error
	GetOrders(context.Context, *OrderRequest, *OrderResponse) error
	CreateOrders(context.Context, *OrderRequest, *OrderResponse) error
	UpdateLead(context.Context, *LeadRequest, *LeadResponse) error
	CreateLeadCareAndItem(context.Context, *LeadRequest, *LeadResponse) error
	GetCareReasons(context.Context, *CareReasonRequest, *CareReasonResponse) error
	GetOrderProducts(context.Context, *OrderRequest, *OrderResponse) error
	GetLandingPages(context.Context, *LandingPageRequest, *LandingPageResponse) error
	GetOrderDisplayId(context.Context, *OrderDisplayIdRequest, *OrderResponse) error
	UpdateOrder(context.Context, *OrderRequest, *OrderResponse) error
	RemoveDuplicateOrder(context.Context, *OrderRequest, *OrderResponse) error
	RemoveDuplicateLead(context.Context, *LeadRequest, *LeadResponse) error
}

func RegisterAGSaleServiceHandler(s server.Server, hdlr AGSaleServiceHandler, opts ...server.HandlerOption) error {
	type aGSaleService interface {
		CreateLead(ctx context.Context, in *LeadRequest, out *LeadResponse) error
		GetLeads(ctx context.Context, in *LeadRequest, out *LeadResponse) error
		GetOrders(ctx context.Context, in *OrderRequest, out *OrderResponse) error
		CreateOrders(ctx context.Context, in *OrderRequest, out *OrderResponse) error
		UpdateLead(ctx context.Context, in *LeadRequest, out *LeadResponse) error
		CreateLeadCareAndItem(ctx context.Context, in *LeadRequest, out *LeadResponse) error
		GetCareReasons(ctx context.Context, in *CareReasonRequest, out *CareReasonResponse) error
		GetOrderProducts(ctx context.Context, in *OrderRequest, out *OrderResponse) error
		GetLandingPages(ctx context.Context, in *LandingPageRequest, out *LandingPageResponse) error
		GetOrderDisplayId(ctx context.Context, in *OrderDisplayIdRequest, out *OrderResponse) error
		UpdateOrder(ctx context.Context, in *OrderRequest, out *OrderResponse) error
		RemoveDuplicateOrder(ctx context.Context, in *OrderRequest, out *OrderResponse) error
		RemoveDuplicateLead(ctx context.Context, in *LeadRequest, out *LeadResponse) error
	}
	type AGSaleService struct {
		aGSaleService
	}
	h := &aGSaleServiceHandler{hdlr}
	return s.Handle(s.NewHandler(&AGSaleService{h}, opts...))
}

type aGSaleServiceHandler struct {
	AGSaleServiceHandler
}

func (h *aGSaleServiceHandler) CreateLead(ctx context.Context, in *LeadRequest, out *LeadResponse) error {
	return h.AGSaleServiceHandler.CreateLead(ctx, in, out)
}

func (h *aGSaleServiceHandler) GetLeads(ctx context.Context, in *LeadRequest, out *LeadResponse) error {
	return h.AGSaleServiceHandler.GetLeads(ctx, in, out)
}

func (h *aGSaleServiceHandler) GetOrders(ctx context.Context, in *OrderRequest, out *OrderResponse) error {
	return h.AGSaleServiceHandler.GetOrders(ctx, in, out)
}

func (h *aGSaleServiceHandler) CreateOrders(ctx context.Context, in *OrderRequest, out *OrderResponse) error {
	return h.AGSaleServiceHandler.CreateOrders(ctx, in, out)
}

func (h *aGSaleServiceHandler) UpdateLead(ctx context.Context, in *LeadRequest, out *LeadResponse) error {
	return h.AGSaleServiceHandler.UpdateLead(ctx, in, out)
}

func (h *aGSaleServiceHandler) CreateLeadCareAndItem(ctx context.Context, in *LeadRequest, out *LeadResponse) error {
	return h.AGSaleServiceHandler.CreateLeadCareAndItem(ctx, in, out)
}

func (h *aGSaleServiceHandler) GetCareReasons(ctx context.Context, in *CareReasonRequest, out *CareReasonResponse) error {
	return h.AGSaleServiceHandler.GetCareReasons(ctx, in, out)
}

func (h *aGSaleServiceHandler) GetOrderProducts(ctx context.Context, in *OrderRequest, out *OrderResponse) error {
	return h.AGSaleServiceHandler.GetOrderProducts(ctx, in, out)
}

func (h *aGSaleServiceHandler) GetLandingPages(ctx context.Context, in *LandingPageRequest, out *LandingPageResponse) error {
	return h.AGSaleServiceHandler.GetLandingPages(ctx, in, out)
}

func (h *aGSaleServiceHandler) GetOrderDisplayId(ctx context.Context, in *OrderDisplayIdRequest, out *OrderResponse) error {
	return h.AGSaleServiceHandler.GetOrderDisplayId(ctx, in, out)
}

func (h *aGSaleServiceHandler) UpdateOrder(ctx context.Context, in *OrderRequest, out *OrderResponse) error {
	return h.AGSaleServiceHandler.UpdateOrder(ctx, in, out)
}

func (h *aGSaleServiceHandler) RemoveDuplicateOrder(ctx context.Context, in *OrderRequest, out *OrderResponse) error {
	return h.AGSaleServiceHandler.RemoveDuplicateOrder(ctx, in, out)
}

func (h *aGSaleServiceHandler) RemoveDuplicateLead(ctx context.Context, in *LeadRequest, out *LeadResponse) error {
	return h.AGSaleServiceHandler.RemoveDuplicateLead(ctx, in, out)
}
