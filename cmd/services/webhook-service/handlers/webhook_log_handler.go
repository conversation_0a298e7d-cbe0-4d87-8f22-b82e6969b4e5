package handlers

import (
	"context"
	"github.com/Masterminds/squirrel"
	tables2 "gitlab.com/a7923/athena-go/cmd/services/webhook-service/tables"
	"gitlab.com/a7923/athena-go/pkg/dbtool"
	"gitlab.com/a7923/athena-go/pkg/logger"
	"gitlab.com/a7923/athena-go/proto/exmsg/models"
)

type WebhookHandler struct {
	db *dbtool.ConnectionManager
}

func NewWebhookHandler(db *dbtool.ConnectionManager) *WebhookHandler {
	return &WebhookHandler{db: db}
}

func (w *WebhookHandler) InsertLogs(ctx context.Context, events *models.WebhookEvents, result *models.SQLResult) error {
	sqlTool := dbtool.NewInsert(ctx, w.db.GetConnection(), tables2.GetWebhookEventsTable(), &models.WebhookEvent{})
	qb := squirrel.
		Insert(sqlTool.GetTable("")).
		Columns(sqlTool.GetQueryColumnList("")...)

	for _, event := range events.Events {
		qb = qb.Values(sqlTool.GetFilledValues(event)...)
	}

	qb = qb.Suffix("ON CONFLICT (message_uuid) DO UPDATE SET updated_at = now()")

	var err error
	result, err = sqlTool.Insert(ctx, qb)
	if err != nil {
		logger.AthenaLogger.Errorw("Failed to insert webhook logs", "error", err)
		return err
	}
	return nil
}
