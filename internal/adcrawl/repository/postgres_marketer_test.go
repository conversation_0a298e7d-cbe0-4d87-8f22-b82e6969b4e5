package repository

import (
	"context"
	"os"
	"testing"

	"github.com/jmoiron/sqlx"
)

func TestListMarketerIDByAdAccountIDs(t *testing.T) {
	db, err := sqlx.ConnectContext(context.Background(), "postgres", os.<PERSON>env("AD_DB_URL"))
	if err != nil {
		t.<PERSON>al(err)
	}
	defer db.Close()
	postgresMarketerRepository := NewPostgresMarketerRepository(db)
	res, err := postgresMarketerRepository.ListByAdAccountIDs(context.Background(), 2, []int64{****************, ***************})
	if err != nil {
		t.<PERSON><PERSON>(err)
	}
	for _, pair := range res.Pairs {
		t.Logf("%+v", pair)
	}
}
