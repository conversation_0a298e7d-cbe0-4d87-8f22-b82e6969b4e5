package repository

import (
	"context"

	"github.com/Masterminds/squirrel"
	"github.com/jmoiron/sqlx"
	"gitlab.com/a7923/athena-go/internal/adcrawl/domain"
	"gitlab.com/a7923/athena-go/pkg/logger"
)

var _ domain.AdAccountRepository = (*PostgresAdAccountRepository)(nil)

type PostgresAdAccountRepository struct {
	db           *sqlx.DB
	queryBuilder squirrel.StatementBuilderType
}

func NewPostgresAdAccountRepository(db *sqlx.DB) *PostgresAdAccountRepository {
	return &PostgresAdAccountRepository{
		db:           db,
		queryBuilder: squirrel.StatementBuilder.PlaceholderFormat(squirrel.Dollar),
	}
}

const (
	adaccountsTable = "fb.adaccounts"
)

var (
	insertedAdAccountColumns = []string{
		"id",
		"company_id",
		"name",
		"status",
		"currency",
		"timezone_offset_hours_utc",
		"country_id",
		"marketer_id",
		"analytic_account_id",
		"updated_by_id",
	}
)

func (par *PostgresAdAccountRepository) BatchInsert(ctx context.Context, accounts []domain.AdAccount, scheduled bool) error {
	qb := par.queryBuilder.Insert(adaccountsTable).Columns(insertedAdAccountColumns...)
	if scheduled {
		qb = qb.SuffixExpr(squirrel.Expr(`
			ON CONFLICT (company_id, id) DO UPDATE SET
				name = EXCLUDED.name,
				status = EXCLUDED.status,
				currency = EXCLUDED.currency,
				timezone_offset_hours_utc = EXCLUDED.timezone_offset_hours_utc,
				country_id = EXCLUDED.country_id,
				analytic_account_id = EXCLUDED.analytic_account_id,
				updated_at = NOW()
			WHERE fb.adaccounts.deleted_at IS NULL
		`))
	} else {
		qb = qb.SuffixExpr(squirrel.Expr(`
			ON CONFLICT (company_id, id) DO UPDATE SET
				name = EXCLUDED.name,
				status = EXCLUDED.status,
				currency = EXCLUDED.currency,
				timezone_offset_hours_utc = EXCLUDED.timezone_offset_hours_utc,
				country_id = EXCLUDED.country_id,
				analytic_account_id = EXCLUDED.analytic_account_id,
				updated_by_id = EXCLUDED.updated_by_id,
				created_at = NOW(),
				updated_at = NOW()
			WHERE fb.adaccounts.deleted_at IS NULL
		`))
	}

	var countryID *int
	for i := range accounts {
		if accounts[i].CountryID != -1 {
			countryID = &accounts[i].CountryID
		} else {
			countryID = nil
		}
		qb = qb.Values(
			accounts[i].ID,
			accounts[i].CompanyID,
			accounts[i].Name,
			accounts[i].Status,
			accounts[i].Currency,
			accounts[i].TimezoneOffsetHoursUTC,
			countryID,
			accounts[i].MarketerID,
			accounts[i].AnalyticAccountID,
			accounts[i].MarketerID, // Default update_by_id is the marketer_id who has fetched it
		)
	}

	insertQuery, args, err := qb.ToSql()
	if err != nil {
		logger.AthenaLogger.Errorw("failed to build query", "error", err)
		return err
	}
	if _, err := par.db.ExecContext(ctx, insertQuery, args...); err != nil {
		logger.AthenaLogger.Errorw("failed to execute query", "error", err)
		return err
	}

	return nil
}

func (par *PostgresAdAccountRepository) GetAllAdAccountIDs(ctx context.Context, analyticAccountID int64) ([]int64, error) {
	qb := par.queryBuilder.Select("DISTINCT id").
		From(adaccountsTable).
		Where(squirrel.And{
			squirrel.Eq{"analytic_account_id": analyticAccountID},
			squirrel.Eq{"deleted_at": nil},
			squirrel.NotEq{"company_id": nil},
		})
	stmt, args, err := qb.ToSql()
	if err != nil {
		return nil, err
	}

	rows, err := par.db.QueryxContext(ctx, stmt, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	adAccountIDs := make([]int64, 0)
	for rows.Next() {
		var adAccountID int64
		err := rows.Scan(&adAccountID)
		if err != nil {
			return nil, err
		}
		adAccountIDs = append(adAccountIDs, adAccountID)
	}
	return adAccountIDs, nil
}
