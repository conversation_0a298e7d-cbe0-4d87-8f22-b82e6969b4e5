package repository

import (
	"context"
	"os"
	"testing"

	"github.com/jmoiron/sqlx"
	_ "github.com/lib/pq"
	"gitlab.com/a7923/athena-go/internal/adcrawl/domain"
)

func TestAdsetBatchInsertAfterDelete(t *testing.T) {
	db, err := sqlx.ConnectContext(context.Background(), "postgres", os.Getenv("AD_DB_URL"))
	if err != nil {
		t.Fatal(err)
	}
	defer db.Close()
	postgresAdsetRepository := NewPostgresAdsetRepository(db)
	adsets := []domain.AdSet{
		{
			ID:               int64(120213785046200279),
			Name:             "19 - 0712 - BREAST - THAI - NAC - Bản sao",
			Status:           "ACTIVE",
			EffectiveStatus:  "ACTIVE",
			OptimizationGoal: "MESSAGING_PURCHASE_CONVERSION",
			CampaignID:       int64(120213784491730279),
			AdAccountID:      int64(***************),
		},
		{
			ID:               int64(120213785046190279),
			Name:             "19 - 0712 - BREAST - THAI - NAC - Bản sao",
			Status:           "ACTIVE",
			EffectiveStatus:  "ACTIVE",
			OptimizationGoal: "MESSAGING_PURCHASE_CONVERSION",
			CampaignID:       int64(120213784491730279),
			AdAccountID:      int64(***************),
		},
	}

	withElapsedTime(t, func() {
		t.Run("trivial", func(t *testing.T) {
			if err := postgresAdsetRepository.BatchInsert(context.Background(), adsets, false); err != nil {
				t.Fatal(err)
			}
		})
	})
}

func TestBatchInsertAdset(t *testing.T) {
	db, err := sqlx.ConnectContext(context.Background(), "postgres", os.Getenv("AD_DB_URL"))
	if err != nil {
		t.Fatal(err)
	}
	defer db.Close()
	postgresAdsetRepository := NewPostgresAdsetRepository(db)
	adsets := []domain.AdSet{
		{
			ID:               *********,
			Name:             "example1",
			Status:           "ACTIVE",
			EffectiveStatus:  "ACTIVE",
			OptimizationGoal: "MESSAGING_PURCHASE_CONVERSION",
			DestinationType:  "WEBSITE",
			CampaignID:       *********,
			MarketName:       "VN",
			MarketID:         84,
		},
		{
			ID:               *********,
			Name:             "example2",
			Status:           "ACTIVE",
			EffectiveStatus:  "ACTIVE",
			OptimizationGoal: "MESSAGING_PURCHASE_CONVERSION",
			DestinationType:  "WEBSITE",
			CampaignID:       *********,
		},
	}

	withElapsedTime(t, func() {
		t.Run("trivial", func(t *testing.T) {
			if err := postgresAdsetRepository.BatchInsert(context.Background(), adsets, false); err != nil {
				t.Fatal(err)
			}
		})
	})
}
