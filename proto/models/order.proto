syntax = "proto3";

package exmsg.models;

option go_package = "gitlab.com/a7923/athena-go/proto/exmsg/models;models";

import "google/protobuf/timestamp.proto";
import "google/protobuf/struct.proto";

// Order represents an order in the system
message Order {
  // Base fields
  int64 id = 1;
  google.protobuf.Timestamp created_at = 2;
  google.protobuf.Timestamp updated_at = 3;

  // Related IDs
  int64 sale_id = 4;
  int64 source_id = 5;
  int64 creator_id = 6;
  int64 care_page_id = 7;
  int64 country_id = 8;
  int64 project_id = 9;
  int64 last_updated_by = 10;
  int64 marketer_id = 11;
  int64 company_id = 12;
  int64 channel_id = 13;
  int64 external_source_id = 14;

  // Financial fields
  double discount = 15;
  double surcharge = 16;
  double shipping_fee = 17;
  double paid = 18;
  double total_price = 19;
  double discount_type = 20;

  // Customer information
  string customer_name = 21;
  string customer_phone = 22;

  // Address fields
  string address_text = 23;
  string address_note = 24;
  string address_ward = 25;
  string address_district = 26;
  string address_province = 27;
  string address_ward_id = 28;
  string address_district_id = 29;
  string address_province_id = 30;
  string post_code = 31;

  // Status and state
  int64 status = 32;
  int64 team_in_charge = 33;
  string cancel_reason_text = 34;
  google.protobuf.Timestamp packing_at = 35;
  string display_id = 36;
  bool is_checked_after_return = 37;

  // Partner information
  string partner_id = 38;
  string partner_code = 39;
  string partner_display_id = 40;

  // Social media and external IDs
  int64 post_code_old = 41;
  int64 page_id = 42;
  google.protobuf.Struct source_detail = 43;
  google.protobuf.Timestamp confirmed_at = 44;
  int64 pancake_conversation_id = 45;
  int64 fb_global_id = 46;
  google.protobuf.Timestamp expect_delivery_at = 47;
  string fb_receipt_message_id = 48;

  // Additional information
  string print_note_text = 49;
  string note = 50;
  google.protobuf.Timestamp last_update_status = 51;
  string ffm_display_id = 52;
  string ffm_company_id = 53;
  int64 fb_scoped_user_id = 54;
  int64 ffm_partner_client_id = 55;
  bool ignore_duplicate_warning = 56;

  // Status change information
  string status_change_reason = 57;
  string status_change_description = 58;
  string status_change_actor = 59;

  // Sync timestamps
  google.protobuf.Timestamp last_synced_ffm_status_at = 60;
  google.protobuf.Timestamp last_synced_lead_user_id_at = 61;
  google.protobuf.Timestamp last_synced_ffm_info_at = 62;
  google.protobuf.Timestamp last_synced_ffm_tags_at = 63;

  // Additional flags
  bool cross_care = 64;
  string utm_link = 65;

  int64 source_project_id = 66;
  optional int64 type = 67;
}

// Message for order_products
enum EditedPriceStatus {
  NONE = 0;
  ACCEPTED = 1;
  REJECTED = 2;
}

message OrderProduct {
  int64 id = 1;                      // Primary key
  int64 order_id = 2;                // Foreign key to orders table
  double quantity = 3;               // Quantity of product
  double price = 4;                  // Original price of the product
  int64 product_id = 5;              // Product ID
  google.protobuf.Struct product_detail = 6;         // JSONB data as a string
//  double editedPrice = 7;            // Edited price (optional)
//  int64 priceEditedBy = 8;           // User ID of who edited the price (optional)
//  int64 priceAcceptedBy = 9;         // User ID of who accepted the price (optional)
}

message LandingPage {
  google.protobuf.Timestamp created_at = 1;
  google.protobuf.Timestamp updated_at = 2;
  string id = 3;
  string name = 4;
  string link = 5;
  int64 country_id = 6;
  int64 project_id = 7;
  int64 user_id = 8;
//  google.protobuf.Timestamp deleted_at = 9;
  int64 company_id = 10;
  int64 creator_id = 11;
  int64 updated_by = 12;
  int64 status = 13;
  int64 product_id = 14;
}