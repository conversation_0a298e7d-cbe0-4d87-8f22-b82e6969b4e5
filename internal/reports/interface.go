package reports

import (
	"github.com/Masterminds/squirrel"
	"gitlab.com/a7923/athena-go/proto/exmsg/models"
	"gitlab.com/a7923/athena-go/proto/exmsg/services"
)

type ReportInterface interface {
	GetQuery(req *services.AnalyticsRequest) (squirrel.SelectBuilder, error)
	ProcessRow(row map[string]interface{}, req *services.AnalyticsRequest) (*models.Row, error)
}

type OrderReportInterface interface {
	GetQuery(req *services.OrderDashboardRequest) (squirrel.SelectBuilder, error)
	ProcessRow(row map[string]interface{}, req *services.OrderDashboardRequest) (*models.Row, error)
}

type TelesaleDashboardInterface interface {
	GetQuery(req *services.TelesaleDashboardRequest) (squirrel.SelectBuilder, error)
	ProcessRow(row map[string]interface{}, req *services.TelesaleDashboardRequest) (*models.Row, error)
}

func GetViewHandler(req *services.AnalyticsRequest) ReportInterface {
	//return viewHandler[viewName]
	if req.TeamInCharge == TeamInChargeTelesale {
		//return &TelesaleReport{}
	}
	return &TableByReport{}
}

func GetOrderDashboardViewHandler(req *services.OrderDashboardRequest) OrderReportInterface {
	return &OrderReport{}
}

func GetTelesaleDashboardViewHandler(req *services.TelesaleDashboardRequest) TelesaleDashboardInterface {
	return &TelesaleDashboard{}
}
