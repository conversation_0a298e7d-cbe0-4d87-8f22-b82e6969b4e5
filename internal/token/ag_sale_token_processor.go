package token

import (
	"context"
	"errors"
	"fmt"
	"github.com/go-redis/redis/v8"
	"github.com/golang-jwt/jwt/v5"
	"github.com/sourcegraph/conc/pool"
	"github.com/spf13/cast"
	"github.com/spf13/viper"
	"gitlab.com/a7923/athena-go/internal/permissions"
	"gitlab.com/a7923/athena-go/pkg/common"
	"gitlab.com/a7923/athena-go/pkg/dbtool"
	"gitlab.com/a7923/athena-go/pkg/logger"
	"gitlab.com/a7923/athena-go/pkg/utils"
	"gitlab.com/a7923/athena-go/proto/exmsg/models"
	"gitlab.com/a7923/athena-go/proto/exmsg/services"
	"time"
)

type AGSaleTokenProcessor struct {
	rd                   *redis.Client
	userService          services.UserService
	jwtSecret            []byte
	enableSignatureCheck bool
	acceptLegacyToken    bool
}

type AGSaleClaim struct {
	Sub                   int64       `json:"sub"`
	Fullname              string      `json:"fullname"`
	CompanyId             int         `json:"companyId"`
	Type                  string      `json:"type"`
	Warehouses            interface{} `json:"warehouses"`
	DisplayId             interface{} `json:"displayId"`
	IsAdmin               bool        `json:"isAdmin"`
	Sid                   string      `json:"sid"`
	Service               string      `json:"service"`
	Permissions           []string    `json:"p"`
	MarketerDescendantIds []int64     `json:"marketerDids"`
	SaleDescendantIds     []int64     `json:"saleDids"`
	CarePageDescendantIds []int64     `json:"carePageDids"`
	Iat                   int         `json:"iat"`
	Exp                   int         `json:"exp"`
	jwt.MapClaims
}
type UserRelation struct {
	Profiles      []*models.UserProfile
	Roles         []*models.Role
	DataSetScopes []*models.DataSetScope
	SubUserIds    []int64
}

func NewAGSaleTokenProcessor(rd *redis.Client, userService services.UserService) *AGSaleTokenProcessor {
	return &AGSaleTokenProcessor{
		rd:                   rd,
		userService:          userService,
		jwtSecret:            []byte(viper.GetString("jwt.secret")),
		enableSignatureCheck: viper.GetBool("jwt.enable_signature_check"),
		acceptLegacyToken:    viper.GetBool("jwt.accept_legacy_token"),
	}
}

func (p *AGSaleTokenProcessor) GetToken(ctx context.Context, tokenString string) (token *Token, err error) {
	rawClaim := AGSaleClaim{}
	tk, err := jwt.ParseWithClaims(tokenString, &rawClaim, func(token *jwt.Token) (interface{}, error) {
		return p.jwtSecret, nil
	})

	if err != nil || !tk.Valid {
		return nil, errors.New("invalid token")
	}

	//claims := &models.JWTClaim{}
	//_ = proto.Unmarshal(rawClaim.UserInfo, claims)
	//if claims.UserId < 1 {
	//	return nil, common.UnauthorizedError
	//}

	// check signature here
	if p.enableSignatureCheck {
		signature := rawClaim.Sid
		if len(signature) == 0 {
			return nil, common.UnauthorizedError
		}

		sig, err := p.getSignatureFromUser(ctx, rawClaim.Sub)
		if err != nil {
			return nil, err
		}

		if signature != sig {
			return nil, common.UnauthorizedError
		}
	}

	return &Token{Token: tk}, nil
}

func (p *AGSaleTokenProcessor) ExtractMetadata(token *Token) map[string]string {
	metadata := make(map[string]string)
	// Extract the claims
	claim := GetJWTClaimFromToken(token)
	if claim.Sub < 1 {
		return metadata
	}

	metadata["sub"] = fmt.Sprintf("%d", claim.Sub)
	metadata["fullname"] = claim.Fullname
	metadata["companyId"] = fmt.Sprintf("%d", claim.CompanyId)
	metadata["type"] = claim.Type
	metadata["warehouses"] = fmt.Sprintf("%v", claim.Warehouses)
	metadata["displayId"] = fmt.Sprintf("%d", claim.DisplayId)
	metadata["isAdmin"] = fmt.Sprintf("%v", claim.IsAdmin)
	metadata["sid"] = claim.Sid
	metadata["service"] = claim.Service
	metadata["p"] = fmt.Sprintf("%v", claim.Permissions)
	dids := make([]int64, 0)
	dids = append(dids, claim.MarketerDescendantIds...)
	dids = append(dids, claim.SaleDescendantIds...)
	dids = append(dids, claim.CarePageDescendantIds...)
	metadata["dids"] = utils.Join(dids, ",")
	metadata["marketerDids"] = utils.Join(claim.MarketerDescendantIds, ",")
	metadata["saleDids"] = utils.Join(claim.SaleDescendantIds, ",")
	metadata["carePageDids"] = utils.Join(claim.CarePageDescendantIds, ",")

	return metadata
}

func (p *AGSaleTokenProcessor) CheckPermissions(token *Token, requirePermissions map[int64]int64) bool {
	if len(requirePermissions) == 0 {
		return true
	}

	claim := GetJWTClaimFromToken(token)
	if claim.Sub < 1 {
		return false
	}

	if claim.IsAdmin {
		return true
	}

	if len(claim.Permissions) == 0 {
		if p.acceptLegacyToken {
			return true
		}

		return false
	}

	for permissionGroupId, permissionSum := range requirePermissions {
		if !permissions.HasPermission(cast.ToInt64(claim.Permissions[permissionGroupId]), permissionSum) {
			return false
		}
	}

	return true
}

func (p *AGSaleTokenProcessor) GenerateToken(ctx context.Context, user *models.User) (string, error) {
	// dont need to check user/company active, only use this function if user and company activated
	//userRelationData, err := p.getUserRelationData(ctx, user)
	//if err != nil {
	//	return "", err
	//}
	//
	//// merge permissions
	//
	//// Create the JWT claim structure and set the required permissions
	//exp := config.ViperGetDurationWithDefault("jwt.exp", time.Hour*24*7)
	//
	//userInfo := &models.JWTClaim{
	//	UserId:      user.Id,
	//	Permissions: mergePermissions(userRelationData.Roles),
	//	DataSets:    generateDataSetScopes(userRelationData.DataSetScopes),
	//	SubUserIds:  userRelationData.SubUserIds,
	//	Signature:   user.GetSessionId(), // using session_id as signature, todo using other fields
	//}
	//
	//userInfoBytes, _ := proto.Marshal(userInfo)
	////claim := AGSaleClaim{
	////	MapClaims: jwt.MapClaims{
	////		"exp": jwt.NewNumericDate(time.Now().Add(exp)), // 7 days
	////	},
	////}
	//
	//token := jwt.NewWithClaims(jwt.SigningMethodHS256, claim)
	//signedToken, err := token.SignedString(p.jwtSecret)
	//if err != nil {
	//	return "", err
	//}
	//
	//return signedToken, nil
	return "", nil
}

func (p *AGSaleTokenProcessor) getUserRelationData(ctx context.Context, user *models.User) (*UserRelation, error) {
	userProfiles, err := p.userService.GetProfiles(ctx, &services.ProfileRequest{
		UserId: user.Id,
		Status: int64(models.CommonStatus_ACTIVATED),
	}, dbtool.WithFieldSelect("role_id", "department_id"))
	if err != nil {
		logger.AthenaLogger.Errorw("Could not get user profiles", "user_id", user.Id, "error", err.Error())
		return nil, err
	}

	if len(userProfiles.GetProfiles()) == 0 {
		logger.AthenaLogger.Errorw("User does not have any profiles", "user_id", user.Id)
		return nil, common.UserHasNoProfile
	}

	roleIds := make([]int64, 0)
	departmentIds := make([]int64, 0)
	for _, profile := range userProfiles.GetProfiles() {
		roleIds = append(roleIds, profile.RoleId)
		departmentIds = append(departmentIds, profile.DepartmentId)
	}

	roles := &services.RoleResponse{}
	dataSetScopes := &services.DataSetScopeResponse{}
	descendants := make([]int64, 0)

	wg := pool.New().WithErrors()
	wg.Go(func() error {
		roles, err = p.userService.GetRoles(ctx, &services.RoleRequest{
			RoleIds: roleIds,
		}, dbtool.WithFieldSelect("new_permissions"))
		if err != nil {
			logger.AthenaLogger.Errorw("Could not get roles", "error", err.Error())
			return fmt.Errorf("getting roles: %w", err)
		}
		return nil
	})

	wg.Go(func() error {
		dataSetScopes, err = p.userService.GetDataSetScopes(ctx, &services.DataSetScopeRequest{
			DepartmentIds: departmentIds,
		}, dbtool.WithFieldSelect("country_id", "entity_id"))
		if err != nil {
			logger.AthenaLogger.Errorw("Could not get data set scopes", "error", err.Error())
			return fmt.Errorf("getting data set scopes: %w", err)
		}
		return nil
	})

	wg.Go(func() error {
		// fetch child user ids
		childProfiles, err := p.userService.GetProfiles(ctx, &services.ProfileRequest{
			AncestorDepartmentIds: departmentIds,
		}, dbtool.WithFieldSelect("user_id"))
		if err != nil {
			logger.AthenaLogger.Errorw("Could not get descendants", "error", err.Error())
			return fmt.Errorf("getting descendants: %w", err)
		}

		for _, profile := range childProfiles.GetProfiles() {
			descendants = append(descendants, profile.UserId)
		}

		descendants = utils.DistinctInt64Slice(descendants)
		return nil
	})

	if err = wg.Wait(); err != nil {
		return nil, fmt.Errorf("fetching roles and data set scopes: %w", err)
	}

	return &UserRelation{
		Profiles:      userProfiles.GetProfiles(),
		Roles:         roles.GetRoles(),
		DataSetScopes: dataSetScopes.GetScopes(),
		SubUserIds:    descendants,
	}, nil
}

func (p *AGSaleTokenProcessor) getSignatureFromUser(ctx context.Context, id int64) (string, error) {
	cacheKey := fmt.Sprintf("ag_sale_token_processor_uid_%v", id)
	signature, _ := p.rd.Get(ctx, cacheKey).Result()
	if len(signature) > 0 {
		return signature, nil
	}

	user, err := p.userService.GetUserByID(ctx, &services.UserRequest{
		UserId: id,
	}, dbtool.WithFieldSelect("session_id")) // use session_id as signature
	if err != nil {
		return "", err
	}
	ssId := user.User.GetSessionId()
	if len(ssId) == 0 {
		return "", fmt.Errorf("user does not have session_id")
	}
	_ = p.rd.Set(ctx, cacheKey, ssId, 24*time.Hour) // handle delete this key if ssid changed

	return ssId, nil
}

func generateDataSetScopes(scopes []*models.DataSetScope) []*models.JWTDataSet {
	resp := make([]*models.JWTDataSet, 0)
	for _, scope := range scopes {
		resp = append(resp, &models.JWTDataSet{
			CountryId: scope.CountryId,
			ProjectId: scope.EntityId,
		})
	}
	return resp
}

func mergePermissions(roles []*models.Role) []int64 {
	if len(roles) == 0 {
		return []int64{}
	}

	resp := roles[0].NewPermission
	if len(roles) == 1 {
		return resp
	}

	n := len(resp)
	for _, role := range roles[1:] {
		newPermission := role.NewPermission
		for i := 0; i < n; i++ {
			resp[i] |= newPermission[i]
		}

		if len(newPermission) > n {
			// append len - n item to resp
			resp = append(resp, newPermission[n:]...)
			n = len(resp)
		}
	}

	return resp
}

func GetJWTClaimFromToken(t *Token) *AGSaleClaim {
	rawClaim, ok := t.Claims.(*AGSaleClaim)
	if !ok {
		return &AGSaleClaim{}
	}
	return rawClaim
}
