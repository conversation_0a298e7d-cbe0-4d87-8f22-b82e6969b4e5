package handlers

import (
	"context"
	"sync"
	"testing"

	"gitlab.com/a7923/athena-go/proto/exmsg/services"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
)

var (
	userServiceClientOnce sync.Once
	userServiceClient     services.UserServiceClient
)

func TestGetUserPermission(t *testing.T) {
	userServiceClient = getUserServiceClient()
	if userServiceClient == nil {
		t.Fatalf("Failed to get user service client")
	}
	testcases := []struct {
		userID                    int64
		expectedHasFullPermission bool
	}{
		{userID: 574, expectedHasFullPermission: true},
		{userID: 344, expectedHasFullPermission: true},
		{userID: 1, expectedHasFullPermission: false},
	}

	for _, tc := range testcases {
		resp, err := userServiceClient.GetUserPermission(context.Background(), &services.GetUserPermissionRequest{
			UserId: tc.userID,
		})
		if err != nil {
			t.Fatalf("Failed to get user permission: %v", err)
		}
		if resp.HasFullPermission != tc.expectedHasFullPermission {
			t.<PERSON><PERSON>("Expected HasFullPermission to be %v, got %v", tc.expectedHasFullPermission, resp.HasFullPermission)
		}
	}

}

func getUserServiceClient() services.UserServiceClient {
	userServiceClientOnce.Do(func() {
		conn, err := grpc.NewClient("localhost:31000", grpc.WithTransportCredentials(insecure.NewCredentials()))
		if err != nil {
			panic(err)
		}
		userServiceClient = services.NewUserServiceClient(conn)
	})
	return userServiceClient
}
