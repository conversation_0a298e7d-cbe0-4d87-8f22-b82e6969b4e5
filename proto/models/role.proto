syntax = "proto3";

package exmsg.models;

option go_package = "gitlab.com/a7923/athena-go/proto/exmsg/models;models";

message Role {
  int64 id = 1;                     // Primary key
  int64 created_at = 2;             // Created timestamp (Unix time in seconds)
  int64 updated_at = 3;             // Updated timestamp (Unix time in seconds)
  string name = 4;                  // Name of the role
  string description = 5;           // Description of the role
//  double permission = 6;            // Permission (numeric type)
  int64 deleted_at = 7;             // Deleted timestamp (Unix time in seconds), nullable
  int32 status = 8;                 // Status (e.g., active/inactive)
  int64 company_id = 9;             // Foreign key to companies table
  int64 market_id = 10;             // Foreign key to markets table
  int64 updated_by = 11;            // ID of the user who last updated the record
  int64 parent_id = 12;             // Foreign key to parent role
  repeated int64 new_permission = 13; // Array of int64 for new permissions
  repeated int32 module_in_charge = 14; // Array of int32 for modules in charge
  int32 data_access_level = 15;     // Data access level
  int64 creator_id = 16;            // ID of the user who created the role
}