package http

import (
	"github.com/spf13/cast"
	"net/http"
	"strings"
)

/**
metadata["sub"] = fmt.Sprintf("%d", claim.Sub)
metadata["fullname"] = claim.Fullname
metadata["companyId"] = fmt.Sprintf("%d", claim.CompanyId)
metadata["type"] = claim.Type
metadata["warehouses"] = fmt.Sprintf("%v", claim.Warehouses)
metadata["displayId"] = fmt.Sprintf("%d", claim.DisplayId)
metadata["isAdmin"] = fmt.Sprintf("%v", claim.IsAdmin)
metadata["sid"] = claim.Sid
metadata["service"] = claim.Service
metadata["p"] = fmt.Sprintf("%v", claim.Permissions)
metadata["dids"] = utils.Join(claim.DescendantIds, ",")
*/

func GetDescendantIdsFromRequest(r *http.Request) []int64 {
	resp := make([]int64, 0)
	descendantIds := r.Header.Get("X-Gw-Dids")
	for _, descendantId := range strings.Split(descendantIds, ",") {
		resp = append(resp, cast.ToInt64(descendantId))
	}
	return resp
}

func GetSaleDescendantIdsFromRequest(r *http.Request) []int64 {
	resp := make([]int64, 0)
	descendantIds := r.Header.Get("X-Gw-Saledids")
	for _, descendantId := range strings.Split(descendantIds, ",") {
		resp = append(resp, cast.ToInt64(descendantId))
	}
	return resp
}

func GetCarePageDescendantIdsFromRequest(r *http.Request) []int64 {
	resp := make([]int64, 0)
	descendantIds := r.Header.Get("X-Gw-Carepagedids")
	for _, descendantId := range strings.Split(descendantIds, ",") {
		resp = append(resp, cast.ToInt64(descendantId))
	}
	return resp
}

func GetCompanyIdFromRequest(r *http.Request) int64 {
	return cast.ToInt64(r.Header.Get("X-Gw-CompanyId"))
}

func GetUserIDFromRequest(r *http.Request) int64 {
	return cast.ToInt64(r.Header.Get("X-Gw-Sub"))
}

func GetIsAdminFromRequest(r *http.Request) bool {
	return cast.ToBool(r.Header.Get("X-Gw-isAdmin"))
}

func GetIdsFromRequestQuery(r *http.Request, queryName string) []int64 {
	resp := make([]int64, 0)
	query := r.URL.Query()
	ids := query[queryName]
	if len(ids) == 0 {
		return resp
	}

	for _, id := range ids {
		resp = append(resp, cast.ToInt64(id))
	}

	return resp
}
