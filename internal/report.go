package internal

import "strings"

const (
	ReportViewTelesaleInsights = "telesale_insights_view"
	ReportViewCarePageInsights = "carepage_insights_view"

	ReportViewTelesaleTableByDate      = "telesale_table_by_date"
	ReportViewTelesaleTableByProject   = "telesale_table_by_project"
	ReportViewTelesaleTableByProduct   = "telesale_table_by_product"
	ReportViewTelesaleTableByMarketer  = "telesale_table_by_marketer"
	ReportViewTelesaleTableByAdaccount = "telesale_table_by_adaccount"
	ReportViewTelesaleTableByCampaign  = "telesale_table_by_campaign"
	ReportViewTelesaleTableByAdset     = "telesale_table_by_adset"

	ReportViewCarePageTableByDate      = "carepage_table_by_date"
	ReportViewCarePageTableByProject   = "carepage_table_by_project"
	ReportViewCarePageTableByProduct   = "carepage_table_by_product"
	ReportViewCarePageTableByMarketer  = "carepage_table_by_marketer"
	ReportViewCarePageTableByAdaccount = "carepage_table_by_adaccount"
	ReportViewCarePageTableByCampaign  = "carepage_table_by_campaign"
	ReportViewCarePageTableByAdset     = "carepage_table_by_adset"

	ReportCarePagePrefix = "carepage_"
	ReportTelesalePrefix = "telesale_"
)

func IsCarePageReport(viewName string) bool {
	return strings.HasPrefix(viewName, ReportCarePagePrefix)
}

func IsTelesaleReport(viewName string) bool {
	return strings.HasPrefix(viewName, ReportTelesalePrefix)
}
