package app

import (
	"github.com/justinas/alice"
	"github.com/spf13/viper"
	"gitlab.com/a7923/athena-go/cmd/apis/webhook-api/app/handlers"
	"gitlab.com/a7923/athena-go/pkg/app"
	"gitlab.com/a7923/athena-go/pkg/micro/web"
	middleware "gitlab.com/a7923/athena-go/pkg/middlewares"
	transhttp "gitlab.com/a7923/athena-go/pkg/transport"
	"go-micro.dev/v5/client"
	"net/http"
	"strings"
)

type Server struct {
	Name     string
	client   client.Client
	producer app.PublisherInterface
}

func NewServer(publisher app.PublisherInterface) *Server {
	return &Server{
		Name:     "webhook-api",
		producer: publisher,
	}
}

func (s *Server) SetGRPCClient(client client.Client) {
	s.client = client
}

func (s *Server) GetBasePath() string {
	return "/webhooks"
}

func (s *Server) GetName() string {
	return s.Name
}

type LogHeaderHandler struct{}

func (l LogHeaderHandler) ServeHTTP(writer http.ResponseWriter, request *http.Request) {
	// return headers as json with status code
	// todo hide sentinel headers
	headers := make(map[string]string)
	for k, v := range request.Header {
		headers[k] = strings.Join(v, ",")
	}
	transhttp.RespondJSON(writer, http.StatusOK, headers)
}

func (s *Server) GetRoutes() web.Routes {
	mdws := []alice.Constructor{}

	if viper.GetBool("logging.enable") {
		mdws = append(mdws, middleware.LoggingMiddleware)
	}

	return []web.Route{
		{
			Name:        "Handle Facebook Webhook",
			Method:      http.MethodGet,
			Pattern:     "/headers",
			Handler:     &LogHeaderHandler{},
			Middlewares: mdws,
			AuthInfo: web.AuthInfo{
				Enable: true,
			},
			Timeout: 10000, // 10 seconds
		},
		{
			Name:    "Handle Facebook Webhook",
			Method:  http.MethodPost,
			Pattern: "/facebook",
			Handler: &handlers.FacebookWebhookHandler{
				Producer: s.producer,
			},
			Middlewares: mdws,
			AuthInfo: web.AuthInfo{
				Enable: false,
			},
			Timeout: 10000, // 10 seconds
		},
		{
			Name:    "Tracking",
			Method:  http.MethodPost,
			Pattern: "/tk",
			Handler: &handlers.TrackingHandler{
				Producer: s.producer,
			},
			Middlewares: mdws,
			AuthInfo: web.AuthInfo{
				Enable: false,
			},
			Timeout: 10000, // 10 seconds
		},
	}
}
