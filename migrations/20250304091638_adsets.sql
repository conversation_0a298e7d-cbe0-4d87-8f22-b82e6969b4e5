-- migrate:up
CREATE TABLE fb.adsets (
    id BIGINT PRIMARY KEY,
    name TEXT,
    status TEXT,
    effective_status TEXT,
    optimization_goal TEXT,
    market TEXT,
    market_id INT,
    adaccount_id BIGINT,
    campaign_id BIGINT,
    destination_type TEXT,
    target_type TEXT,
    company_id BIGINT,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ,
    deleted_at TIMESTAMPTZ
);

-- migrate:down
DROP INDEX IF EXISTS fb.idx_adsets_id;
ALTER TABLE fb.adsets DROP CONSTRAINT IF EXISTS fk_adsets_campaign;
DROP TABLE IF EXISTS fb.adsets;

