package handlers

import (
	"context"
	"encoding/json"
	"testing"

	"gitlab.com/a7923/athena-go/proto/exmsg/models"
	"gitlab.com/a7923/athena-go/proto/exmsg/services"
)

// Must run gRPC server before running this test

func TestBatchInsertAdAccounts(t *testing.T) {
	client = getClient()
	adAccounts := []*models.AdAccount{
		{
			Id:                     123,
			Name:                   "Test Account A",
			Status:                 "ACTIVE",
			Currency:               "USD",
			TimezoneOffsetHoursUtc: -4.0,
			MarketerId:             int64Ptr(10),
			AnalyticAccountId:      123123,
		},
		{
			Id:                     456,
			Name:                   "Campaign X",
			Status:                 "PAUSED",
			Currency:               "EUR",
			TimezoneOffsetHoursUtc: 2.0,
			MarketerId:             int64Ptr(10),
			AnalyticAccountId:      123123,
		},
		{
			Id:                     222,
			Name:                   "Global Campaign",
			Status:                 "ACTIVE",
			Currency:               "AUD",
			TimezoneOffsetHoursUtc: 10.0,
			MarketerId:             int64Ptr(40),
			AnalyticAccountId:      123123,
		},
		{
			Id:                     1011,
			Name:                   "Account Y",
			Status:                 "ACTIVE",
			Currency:               "CAD",
			TimezoneOffsetHoursUtc: -5.0,
			MarketerId:             int64Ptr(10),
			AnalyticAccountId:      123123,
		},
	}

	req := &services.BatchInsertAdAccountsRequest{
		Adaccounts: adAccounts,
	}

	if resp, err := client.BatchInsertAdAccounts(context.Background(), req); err != nil {
		t.Errorf("BatchInsertAdAccounts failed: %v", err)
	} else {
		t.Logf("BatchInsertAdAccounts response: %v", resp)
	}
}

func TestBatchInsertCampaigns(t *testing.T) {
	client = getClient()
	campaigns := []*models.Campaign{
		{
			Id:              155,
			Name:            "Campaign A",
			Status:          "ACTIVE",
			EffectiveStatus: "ACTIVE",
			AdaccountId:     123,
			Objective:       stringPtr("OUTCOME_ENGAGEMENT"),
			MarketerId:      int64Ptr(10),
		},
		{
			Id:              156,
			Name:            "Campaign B - Paused",
			Status:          "ACTIVE",
			EffectiveStatus: "PAUSED",
			AdaccountId:     123,
			Objective:       stringPtr("OUTCOME_AWARENESS"),
			MarketerId:      int64Ptr(10),
		},
		{
			Id:              888,
			Name:            "Campaign D - Pending",
			Status:          "ACTIVE",
			EffectiveStatus: "ACTIVE",
			AdaccountId:     456,
			Objective:       stringPtr("MESSAGES"),
			MarketerId:      int64Ptr(118),
		},
	}
	req := &services.BatchInsertCampaignsRequest{
		Campaigns: campaigns,
	}
	if resp, err := client.BatchInsertCampaigns(context.Background(), req); err != nil {
		t.Errorf("BatchInsertCampaigns failed: %v", err)
	} else {
		t.Logf("BatchInsertCampaigns response: %v", resp)
	}
}

func TestBatchInsertAdsets(t *testing.T) {
	client = getClient()
	adsets := []*models.Adset{
		{
			Id:               756,
			Name:             "Adset Example A",
			Status:           "ACTIVE",
			EffectiveStatus:  "ACTIVE",
			OptimizationGoal: "MESSAGING_PURCHASE_CONVERSION",
			CampaignId:       888,
			AdaccountId:      456,
		},
		{
			Id:               666,
			Name:             "Adset Example B",
			Status:           "ACTIVE",
			EffectiveStatus:  "CAMPAIGN_PAUSED",
			OptimizationGoal: "CONVERSATIONS",
			CampaignId:       156,
			AdaccountId:      222,
		},
	}
	req := &services.BatchInsertAdsetsRequest{
		Adsets: adsets,
	}
	if resp, err := client.BatchInsertAdsets(context.Background(), req); err != nil {
		t.Errorf("BatchInsertAdsets failed: %v", err)
	} else {
		t.Logf("BatchInsertAdsets response: %v", resp)
	}
}

func TestBatchInsertAds(t *testing.T) {
	client = getClient()

	exampleSpec, _ := json.Marshal(map[string]interface{}{
		"page_id": "***************",
	})
	ads := []*models.Ad{
		{
			Id:              999,
			Name:            "Ad Example A",
			Status:          "ACTIVE",
			EffectiveStatus: "ADSET_PAUSED",
			AdsetId:         666,
			PageId:          "***************",
			ObjectStoryId:   stringPtr("***************_122110800842677873"),
			ObjectStorySpec: exampleSpec,
			ImageUrl:        stringPtr("https://example.com/image.jpg"),
		},
		{
			Id:              2222,
			Name:            "Ad Example BDE",
			Status:          "ACTIVE",
			EffectiveStatus: "DISAPPROVED",
			AdsetId:         666,
			PageId:          "***************",
			ObjectStoryId:   stringPtr("***************_122110800842677873"),
			ObjectStorySpec: nil,
			ImageUrl:        stringPtr("https://example.com/image.jpg"),
		},
		{
			Id:              3333,
			Name:            "Ad Example LLLL",
			Status:          "PAUSED",
			EffectiveStatus: "WITH_ISSUES",
			AdsetId:         666,
			PageId:          "***************",
			ObjectStoryId:   stringPtr("***************_122110800842677873"),
			ObjectStorySpec: nil,
			ImageUrl:        stringPtr("https://example.com/image.jpg"),
		},
	}
	req := &services.BatchInsertAdsRequest{
		Ads: ads,
	}
	if resp, err := client.BatchInsertAds(context.Background(), req); err != nil {
		t.Errorf("BatchInsertAds failed: %v", err)
	} else {
		t.Logf("BatchInsertAds response: %v", resp)
	}
}

func TestBatchInsertSpendings(t *testing.T) {
	client = getClient()
	spendings := []*models.Spending{
		{
			AdId:         3333,
			DateStart:    "2025-03-25",
			HourlyStat:   1,
			RawNumeric:   538.00,
			Cpm:          8242.86,
			Cpc:          205.00,
			Ctr:          11.9,
			Clicks:       35.00,
			Frequency:    0.5,
			P25:          12.0,
			P50:          15.0,
			P75:          22.5,
			P95:          30.0,
			VideoAverage: 25,
			Impressions:  16.00,
		},
		{
			AdId:         2222,
			DateStart:    "2025-03-25",
			HourlyStat:   5,
			RawNumeric:   2228.00,
			Cpm:          122.15,
			Cpc:          225.00,
			Ctr:          11.9,
			Clicks:       35.00,
			Frequency:    0.5,
			P25:          12.0,
			P50:          15.0,
			P75:          22.5,
			P95:          30.0,
			VideoAverage: 26,
			Impressions:  16.00,
		},
	}
	req := &services.BatchInsertSpendingsRequest{
		Spendings: spendings,
	}
	if resp, err := client.BatchInsertSpendings(context.Background(), req); err != nil {
		t.Errorf("BatchInsertSpendings failed: %v", err)
	} else {
		t.Logf("BatchInsertSpendings response: %v", resp)
	}
}

func int64Ptr(a int64) *int64 {
	return &a
}

func stringPtr(a string) *string {
	return &a
}
