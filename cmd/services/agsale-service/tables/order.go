package tables

import (
	"gitlab.com/a7923/athena-go/pkg/dbtool"
	"time"
)

func GetOrderTable() *dbtool.Table {
	return &dbtool.Table{
		Name:          "orders",
		AIColumns:     []string{"id"},
		ColumnMapper:  map[string]string{},
		IgnoreColumns: []string{},
		DefaultAlias:  "l",
		NotNullColumns: map[string]interface{}{
			// Timestamp fields with NOW() default
			"created_at": func() interface{} { return time.Now() },
			"updated_at": func() interface{} { return time.Now() },

			// Integer fields
			"id":             nil,      // Skip as it's handled by sequence
			"country_id":     0,        // No default in schema, but NOT NULL
			"status":         int16(0), // smallint default '0'
			"team_in_charge": int16(1), // smallint default '1'

			// Float fields
			"discount_type": float64(0), // double precision default '0'

			// Boolean fields
			"ignore_duplicate_warning": false,
			"cross_care":               false,
		},
	}
}

func GetOrderProductTable() *dbtool.Table {
	return &dbtool.Table{
		Name:           "order_products",
		AIColumns:      []string{"id"},
		ColumnMapper:   map[string]string{},
		IgnoreColumns:  []string{},
		DefaultAlias:   "l",
		NotNullColumns: map[string]interface{}{},
	}
}

func GetLandingPagesTable() *dbtool.Table {
	return &dbtool.Table{
		Name:           "landing_pages",
		AIColumns:      []string{},
		ColumnMapper:   map[string]string{},
		IgnoreColumns:  []string{},
		DefaultAlias:   "l",
		NotNullColumns: map[string]interface{}{},
	}
}
