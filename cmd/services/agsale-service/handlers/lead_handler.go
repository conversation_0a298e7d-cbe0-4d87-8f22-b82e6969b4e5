package handlers

import (
	"context"
	"errors"
	"github.com/Masterminds/squirrel"
	"gitlab.com/a7923/athena-go/cmd/services/agsale-service/tables"
	"gitlab.com/a7923/athena-go/cmd/services/agsale-service/utils"
	"gitlab.com/a7923/athena-go/pkg/dbtool"
	"gitlab.com/a7923/athena-go/pkg/logger"
	"gitlab.com/a7923/athena-go/proto/exmsg/models"
	"gitlab.com/a7923/athena-go/proto/exmsg/services"
)

func (a *AGSaleHandler) CreateLead(ctx context.Context, request *services.LeadRequest, response *services.LeadResponse) error {
	if request.UsingTransaction {
		return a.createLeadWithTx(ctx, request, response)
	}

	sqlTool := dbtool.NewInsert(ctx, a.db.GetConnection(), tables.GetLeadTable(), request.Lead)
	qb := squirrel.
		Insert(sqlTool.GetTable("")).
		Columns(sqlTool.GetQueryColumnList("")...).
		Values(sqlTool.GetFilledValues(request.Lead)...)

	res, err := sqlTool.Insert(ctx, qb)
	if err != nil {
		logger.AthenaLogger.Errorw("Failed to insert lead", "error", err)
		return err
	}
	response.ExecResult = res
	return nil
}

func (a *AGSaleHandler) GetLeads(ctx context.Context, request *services.LeadRequest, response *services.LeadResponse) error {
	sqlTool := dbtool.NewSelect(ctx, a.db.GetConnection(), tables.GetLeadTable(), &models.Lead{})

	limit := dbtool.GetQueryLimit(request.Query.GetLimit(), 50, 250)
	offset := dbtool.GetQueryOffset(limit, request.Query.GetPage())

	qb := squirrel.
		Select(sqlTool.GetQueryColumnList("")...).
		From(sqlTool.GetTable("")).
		Limit(limit).
		Offset(offset)

	qb, _ = utils.GetLeadRequest(qb, request)

	response.Leads = make([]*models.Lead, 0)
	err := sqlTool.Select(ctx, &response.Leads, qb)
	if err != nil {
		logger.AthenaLogger.Errorw("Failed to scan row", "error", err)
		return err
	}
	return nil
}

func (a *AGSaleHandler) createLeadWithTx(ctx context.Context, request *services.LeadRequest, response *services.LeadResponse) error {
	var err error
	tx, err := dbtool.NewTransaction(ctx, a.db.GetConnection())
	if err != nil {
		logger.AthenaLogger.Errorw("Failed to start transaction", "error", err)
		return err
	}
	defer func() {
		if err != nil {
			rerr := tx.RollbackTransactions()
			if rerr != nil {
				logger.AthenaLogger.Errorw("Failed to rollback transaction", "error", rerr)
			}
		}
	}()

	if request.Order != nil {
		// create order
		tx.PrepareInsert(ctx, tables.GetOrderTable(), request.Order)
		qb := squirrel.Insert(tx.GetTable("")).
			Columns(tx.GetQueryColumnList("")...).
			Values(tx.GetFilledValues(request.Order)...)

		result, err := tx.Insert(ctx, qb)
		if err != nil {
			logger.AthenaLogger.Errorw("Failed to insert order", "error", err)
			return err
		}

		orderId := result.GetLastInsertIds()[0]
		request.Lead.OrderId = orderId
	}
	// insert lead
	tx.PrepareInsert(ctx, tables.GetLeadTable(), request.Lead)
	qb := squirrel.Insert(tx.GetTable("")).
		Columns(tx.GetQueryColumnList("")...).
		Values(tx.GetFilledValues(request.Lead)...)

	res, err := tx.Insert(ctx, qb)
	if err != nil {
		logger.AthenaLogger.Errorw("Failed to insert lead", "error", err)
		return err
	}

	err = tx.CommitTransactions()
	if err != nil {
		logger.AthenaLogger.Errorw("Failed to commit transaction", "error", err)
		return err
	}

	response.ExecResult = res
	return nil
}

func (a *AGSaleHandler) UpdateLead(ctx context.Context, request *services.LeadRequest, response *services.LeadResponse) error {
	if request.Lead == nil {
		return errors.New("lead is required")
	}

	if len(request.UpdateFields) == 0 {
		return errors.New("update fields are required")
	}

	if request.Id < 1 {
		return errors.New("id is required")
	}

	sqlTool := dbtool.NewUpdate(ctx, a.db.GetConnection(), tables.GetLeadTable(), request.Lead)
	qb := squirrel.
		Update(sqlTool.GetTable("")).
		SetMap(sqlTool.GetUpdateMap(request.Lead, request.UpdateFields...)).
		Where(squirrel.Eq{"id": request.Id})

	// define more conditions here
	res, err := sqlTool.Update(ctx, qb)
	if err != nil {
		logger.AthenaLogger.Errorw("Failed to update lead", "error", err)
		return err
	}
	response.ExecResult = res
	return nil
}

func (a *AGSaleHandler) RemoveDuplicateLead(ctx context.Context, request *services.LeadRequest, response *services.LeadResponse) error {
	if request.Id < 1 {
		return errors.New("id is required")
	}
	sqlTool := dbtool.NewDelete(ctx, a.db.GetConnection(), tables.GetDuplicateLeads(), &models.DuplicateLeads{})
	qb := squirrel.Delete(sqlTool.GetTable("")).Where(squirrel.Or{
		squirrel.Eq{"lead_id": request.Id},
		squirrel.Eq{"duplicate_lead_id": request.Id},
	})
	_, err := sqlTool.Delete(ctx, qb)
	if err != nil {
		logger.AthenaLogger.Errorw("Failed to delete duplicate lead", "error", err)
		return err
	}
	logger.AthenaLogger.Infow("Deleted duplicate lead", "id", request.Id)
	response.ExecResult = &models.SQLResult{
		RowsAffected: 0,
	}
	return nil
}
