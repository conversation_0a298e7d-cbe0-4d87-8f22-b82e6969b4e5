syntax = "proto3";

package exmsg.models;
option go_package = "gitlab.com/a7923/athena-go/proto/exmsg/models;models";

import "google/protobuf/timestamp.proto";
import "google/protobuf/struct.proto";

message Project {
  int64 id = 1;
  string name = 2;
  string image = 3;
  google.protobuf.Timestamp created_at = 4;
  google.protobuf.Timestamp updated_at = 5;
  google.protobuf.Timestamp deleted_at = 6;
  int32 status = 7;
  string short_name = 8;
  int64 company_id = 9;
  int64 leader_id = 10;
  repeated string productIds = 11;
  int64 updated_by = 12;
  google.protobuf.Struct settings = 43;
}