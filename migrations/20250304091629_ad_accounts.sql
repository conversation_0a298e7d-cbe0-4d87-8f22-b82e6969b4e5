-- migrate:up
CREATE TABLE IF NOT EXISTS fb.adaccounts (
    id BIGINT NOT NULL,
    company_id BIGINT NOT NULL,

    name TEXT,
    status SMALLINT,
    status_name TEXT,
    currency TEXT,
    is_hidden BOOLEAN,
    timezone_offset_hours_utc REAL,
    available BOOLEAN,
    analytic_account_id BIGINT,
    country_id INTEGER,
    marketer_id BIGINT,
    updated_by_id BIGINT,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ
);

ALTER TABLE fb.adaccounts ADD CONSTRAINT fk_adaccounts_analytic_accounts FOREIGN KEY (company_id, analytic_account_id) REFERENCES fb.analytic_accounts(company_id, id) ON DELETE CASCADE;
CREATE INDEX IF NOT EXISTS idx_adaccounts_analytic_account ON fb.adaccounts (company_id, analytic_account_id);
CREATE INDEX IF NOT EXISTS idx_adaccounts_company_id_id ON fb.adaccounts (company_id, id);
ALTER TABLE fb.adaccounts ADD CONSTRAINT unique_company_id_id_2 UNIQUE (company_id, id);

-- migrate:down
DROP INDEX IF EXISTS fb.idx_adaccounts_company_id_id;
DROP INDEX IF EXISTS fb.idx_adaccounts_analytic_account;
ALTER TABLE fb.adaccounts DROP CONSTRAINT IF EXISTS unique_company_id_id;
DROP TABLE IF EXISTS fb.adaccounts;

