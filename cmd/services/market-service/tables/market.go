package tables

import "gitlab.com/a7923/athena-go/pkg/dbtool"

const (
	AnalyticAccountTable = "fb.analytic_accounts"
	AdAccountTable       = "fb.adaccounts"
	CampaignTable        = "fb.campaigns"
	AdSetTable           = "fb.adsets"
	AdTable              = "fb.ads"
	SpendingTable        = "fb.spendings"
)

func GetAnalyticAccountTable() *dbtool.Table {
	return &dbtool.Table{
		Name:         AnalyticAccountTable,
		DefaultAlias: "acc",
	}
}

func GetAdAccountTable() *dbtool.Table {
	return &dbtool.Table{
		Name:         AdAccountTable,
		DefaultAlias: "ada",
	}
}

func GetCampaignTable() *dbtool.Table {
	return &dbtool.Table{
		Name:         CampaignTable,
		DefaultAlias: "cam",
	}
}

func GetAdSetTable() *dbtool.Table {
	return &dbtool.Table{
		Name:         AdSetTable,
		DefaultAlias: "adset",
	}
}

func GetAdTable() *dbtool.Table {
	return &dbtool.Table{
		Name:         AdTable,
		DefaultAlias: "ad",
	}
}

func GetSpendingTable() *dbtool.Table {
	return &dbtool.Table{
		Name:         SpendingTable,
		DefaultAlias: "sp",
	}
}
