// Code generated by protoc-gen-micro. DO NOT EDIT.
// source: services/user.proto

package services

import (
	fmt "fmt"
	_ "gitlab.com/a7923/athena-go/proto/exmsg/models"
	proto "google.golang.org/protobuf/proto"
	math "math"
)

import (
	context "context"
	client "go-micro.dev/v5/client"
	server "go-micro.dev/v5/server"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ client.Option
var _ server.Option

// Client API for UserService service

type UserService interface {
	GetUserByID(ctx context.Context, in *UserRequest, opts ...client.CallOption) (*UserResponse, error)
	GetProfiles(ctx context.Context, in *ProfileRequest, opts ...client.CallOption) (*ProfileResponse, error)
	GetDepartments(ctx context.Context, in *DepartmentRequest, opts ...client.CallOption) (*DepartmentResponse, error)
	GetRoles(ctx context.Context, in *RoleRequest, opts ...client.CallOption) (*RoleResponse, error)
	GetDataSetScopes(ctx context.Context, in *DataSetScopeRequest, opts ...client.CallOption) (*DataSetScopeResponse, error)
	GetProjects(ctx context.Context, in *ProjectRequest, opts ...client.CallOption) (*ProjectResponse, error)
	GetUsersByOptions(ctx context.Context, in *GetUsersByOptionsRequest, opts ...client.CallOption) (*GetUsersByOptionsResponse, error)
	GetUserPermission(ctx context.Context, in *GetUserPermissionRequest, opts ...client.CallOption) (*GetUserPermissionResponse, error)
	GetUserProfile(ctx context.Context, in *GetUserPermissionRequest, opts ...client.CallOption) (*GetUserProfileResponse, error)
}

type userService struct {
	c    client.Client
	name string
}

func NewUserService(name string, c client.Client) UserService {
	return &userService{
		c:    c,
		name: name,
	}
}

func (c *userService) GetUserByID(ctx context.Context, in *UserRequest, opts ...client.CallOption) (*UserResponse, error) {
	req := c.c.NewRequest(c.name, "UserService.GetUserByID", in)
	out := new(UserResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userService) GetProfiles(ctx context.Context, in *ProfileRequest, opts ...client.CallOption) (*ProfileResponse, error) {
	req := c.c.NewRequest(c.name, "UserService.GetProfiles", in)
	out := new(ProfileResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userService) GetDepartments(ctx context.Context, in *DepartmentRequest, opts ...client.CallOption) (*DepartmentResponse, error) {
	req := c.c.NewRequest(c.name, "UserService.GetDepartments", in)
	out := new(DepartmentResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userService) GetRoles(ctx context.Context, in *RoleRequest, opts ...client.CallOption) (*RoleResponse, error) {
	req := c.c.NewRequest(c.name, "UserService.GetRoles", in)
	out := new(RoleResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userService) GetDataSetScopes(ctx context.Context, in *DataSetScopeRequest, opts ...client.CallOption) (*DataSetScopeResponse, error) {
	req := c.c.NewRequest(c.name, "UserService.GetDataSetScopes", in)
	out := new(DataSetScopeResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userService) GetProjects(ctx context.Context, in *ProjectRequest, opts ...client.CallOption) (*ProjectResponse, error) {
	req := c.c.NewRequest(c.name, "UserService.GetProjects", in)
	out := new(ProjectResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userService) GetUsersByOptions(ctx context.Context, in *GetUsersByOptionsRequest, opts ...client.CallOption) (*GetUsersByOptionsResponse, error) {
	req := c.c.NewRequest(c.name, "UserService.GetUsersByOptions", in)
	out := new(GetUsersByOptionsResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userService) GetUserPermission(ctx context.Context, in *GetUserPermissionRequest, opts ...client.CallOption) (*GetUserPermissionResponse, error) {
	req := c.c.NewRequest(c.name, "UserService.GetUserPermission", in)
	out := new(GetUserPermissionResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userService) GetUserProfile(ctx context.Context, in *GetUserPermissionRequest, opts ...client.CallOption) (*GetUserProfileResponse, error) {
	req := c.c.NewRequest(c.name, "UserService.GetUserProfile", in)
	out := new(GetUserProfileResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for UserService service

type UserServiceHandler interface {
	GetUserByID(context.Context, *UserRequest, *UserResponse) error
	GetProfiles(context.Context, *ProfileRequest, *ProfileResponse) error
	GetDepartments(context.Context, *DepartmentRequest, *DepartmentResponse) error
	GetRoles(context.Context, *RoleRequest, *RoleResponse) error
	GetDataSetScopes(context.Context, *DataSetScopeRequest, *DataSetScopeResponse) error
	GetProjects(context.Context, *ProjectRequest, *ProjectResponse) error
	GetUsersByOptions(context.Context, *GetUsersByOptionsRequest, *GetUsersByOptionsResponse) error
	GetUserPermission(context.Context, *GetUserPermissionRequest, *GetUserPermissionResponse) error
	GetUserProfile(context.Context, *GetUserPermissionRequest, *GetUserProfileResponse) error
}

func RegisterUserServiceHandler(s server.Server, hdlr UserServiceHandler, opts ...server.HandlerOption) error {
	type userService interface {
		GetUserByID(ctx context.Context, in *UserRequest, out *UserResponse) error
		GetProfiles(ctx context.Context, in *ProfileRequest, out *ProfileResponse) error
		GetDepartments(ctx context.Context, in *DepartmentRequest, out *DepartmentResponse) error
		GetRoles(ctx context.Context, in *RoleRequest, out *RoleResponse) error
		GetDataSetScopes(ctx context.Context, in *DataSetScopeRequest, out *DataSetScopeResponse) error
		GetProjects(ctx context.Context, in *ProjectRequest, out *ProjectResponse) error
		GetUsersByOptions(ctx context.Context, in *GetUsersByOptionsRequest, out *GetUsersByOptionsResponse) error
		GetUserPermission(ctx context.Context, in *GetUserPermissionRequest, out *GetUserPermissionResponse) error
		GetUserProfile(ctx context.Context, in *GetUserPermissionRequest, out *GetUserProfileResponse) error
	}
	type UserService struct {
		userService
	}
	h := &userServiceHandler{hdlr}
	return s.Handle(s.NewHandler(&UserService{h}, opts...))
}

type userServiceHandler struct {
	UserServiceHandler
}

func (h *userServiceHandler) GetUserByID(ctx context.Context, in *UserRequest, out *UserResponse) error {
	return h.UserServiceHandler.GetUserByID(ctx, in, out)
}

func (h *userServiceHandler) GetProfiles(ctx context.Context, in *ProfileRequest, out *ProfileResponse) error {
	return h.UserServiceHandler.GetProfiles(ctx, in, out)
}

func (h *userServiceHandler) GetDepartments(ctx context.Context, in *DepartmentRequest, out *DepartmentResponse) error {
	return h.UserServiceHandler.GetDepartments(ctx, in, out)
}

func (h *userServiceHandler) GetRoles(ctx context.Context, in *RoleRequest, out *RoleResponse) error {
	return h.UserServiceHandler.GetRoles(ctx, in, out)
}

func (h *userServiceHandler) GetDataSetScopes(ctx context.Context, in *DataSetScopeRequest, out *DataSetScopeResponse) error {
	return h.UserServiceHandler.GetDataSetScopes(ctx, in, out)
}

func (h *userServiceHandler) GetProjects(ctx context.Context, in *ProjectRequest, out *ProjectResponse) error {
	return h.UserServiceHandler.GetProjects(ctx, in, out)
}

func (h *userServiceHandler) GetUsersByOptions(ctx context.Context, in *GetUsersByOptionsRequest, out *GetUsersByOptionsResponse) error {
	return h.UserServiceHandler.GetUsersByOptions(ctx, in, out)
}

func (h *userServiceHandler) GetUserPermission(ctx context.Context, in *GetUserPermissionRequest, out *GetUserPermissionResponse) error {
	return h.UserServiceHandler.GetUserPermission(ctx, in, out)
}

func (h *userServiceHandler) GetUserProfile(ctx context.Context, in *GetUserPermissionRequest, out *GetUserProfileResponse) error {
	return h.UserServiceHandler.GetUserProfile(ctx, in, out)
}
