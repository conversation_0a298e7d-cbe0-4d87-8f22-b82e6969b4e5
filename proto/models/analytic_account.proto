syntax = "proto3";

package exmsg.models;

option go_package = "gitlab.com/a7923/athena-go/proto/exmsg/models;models";

import "google/protobuf/timestamp.proto";

message AnalyticAccount {
    int64 id = 1;
    string username = 2;
    string profile_pic_url = 3;
    int64 company_id = 4;
    int64 marketer_id = 5;
    string marketer_name = 6;
    bool available = 7;
    string token = 8;
    optional int64 count_ad_accounts = 9;
    google.protobuf.Timestamp created_at = 10;
}