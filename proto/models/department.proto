syntax = "proto3";

package exmsg.models;

option go_package = "gitlab.com/a7923/athena-go/proto/exmsg/models;models";

message Department {
  int64 id = 1;                 // Primary key
  int64 created_at = 2;         // Created timestamp (Unix time in seconds)
  int64 updated_at = 3;         // Updated timestamp (Unix time in seconds)
  string name = 4;              // Name of the department
  int64 data_set_id = 5;        // Foreign key to data sets table
  int64 leader_profile_id = 6;  // Foreign key to leader's profile
  int64 role_id = 7;            // Foreign key to roles table
  int32 level = 8;              // Department level
  int64 parent_id = 9;          // Foreign key to parent department
  int64 company_id = 10;        // Foreign key to companies table
  int64 creator_id = 11;        // ID of the user who created the record
  int64 updated_by = 12;        // ID of the user who last updated the record
  int64 deleted_at = 13;        // Deleted timestamp (Unix time in seconds), nullable
}

message DataSetScope {
  int64 id = 1;                 // Primary key
  int64 created_at = 2;         // Created timestamp (Unix time in seconds)
  int64 updated_at = 3;         // Updated timestamp (Unix time in seconds)
  int64 data_set_id = 4;        // Foreign key to data sets table
  int64 entity_id = 5;          // Project ID for sale system or warehouse ID for FFM system
  int64 country_id = 6;         // Country ID
}