// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v3.12.4
// source: services/user.proto

package services

import (
	models "gitlab.com/a7923/athena-go/proto/exmsg/models"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UserRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        int64                  `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserRequest) Reset() {
	*x = UserRequest{}
	mi := &file_services_user_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserRequest) ProtoMessage() {}

func (x *UserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_user_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserRequest.ProtoReflect.Descriptor instead.
func (*UserRequest) Descriptor() ([]byte, []int) {
	return file_services_user_proto_rawDescGZIP(), []int{0}
}

func (x *UserRequest) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type ProfileRequest struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	UserId                int64                  `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Query                 *models.Query          `protobuf:"bytes,2,opt,name=query,proto3" json:"query,omitempty"`
	Status                int64                  `protobuf:"varint,4,opt,name=status,proto3" json:"status,omitempty"`
	DepartmentIds         []int64                `protobuf:"varint,5,rep,packed,name=department_ids,json=departmentIds,proto3" json:"department_ids,omitempty"`
	AncestorDepartmentIds []int64                `protobuf:"varint,6,rep,packed,name=ancestor_department_ids,json=ancestorDepartmentIds,proto3" json:"ancestor_department_ids,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *ProfileRequest) Reset() {
	*x = ProfileRequest{}
	mi := &file_services_user_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProfileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProfileRequest) ProtoMessage() {}

func (x *ProfileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_user_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProfileRequest.ProtoReflect.Descriptor instead.
func (*ProfileRequest) Descriptor() ([]byte, []int) {
	return file_services_user_proto_rawDescGZIP(), []int{1}
}

func (x *ProfileRequest) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *ProfileRequest) GetQuery() *models.Query {
	if x != nil {
		return x.Query
	}
	return nil
}

func (x *ProfileRequest) GetStatus() int64 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *ProfileRequest) GetDepartmentIds() []int64 {
	if x != nil {
		return x.DepartmentIds
	}
	return nil
}

func (x *ProfileRequest) GetAncestorDepartmentIds() []int64 {
	if x != nil {
		return x.AncestorDepartmentIds
	}
	return nil
}

type RoleRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RoleId        int64                  `protobuf:"varint,1,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	RoleIds       []int64                `protobuf:"varint,2,rep,packed,name=role_ids,json=roleIds,proto3" json:"role_ids,omitempty"`
	UserId        int64                  `protobuf:"varint,3,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Query         *models.Query          `protobuf:"bytes,4,opt,name=query,proto3" json:"query,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RoleRequest) Reset() {
	*x = RoleRequest{}
	mi := &file_services_user_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RoleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RoleRequest) ProtoMessage() {}

func (x *RoleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_user_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RoleRequest.ProtoReflect.Descriptor instead.
func (*RoleRequest) Descriptor() ([]byte, []int) {
	return file_services_user_proto_rawDescGZIP(), []int{2}
}

func (x *RoleRequest) GetRoleId() int64 {
	if x != nil {
		return x.RoleId
	}
	return 0
}

func (x *RoleRequest) GetRoleIds() []int64 {
	if x != nil {
		return x.RoleIds
	}
	return nil
}

func (x *RoleRequest) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *RoleRequest) GetQuery() *models.Query {
	if x != nil {
		return x.Query
	}
	return nil
}

type DepartmentRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DepartmentId  int64                  `protobuf:"varint,1,opt,name=department_id,json=departmentId,proto3" json:"department_id,omitempty"`
	DepartmentIds []int64                `protobuf:"varint,2,rep,packed,name=department_ids,json=departmentIds,proto3" json:"department_ids,omitempty"`
	AncestorIds   []int64                `protobuf:"varint,3,rep,packed,name=ancestor_ids,json=ancestorIds,proto3" json:"ancestor_ids,omitempty"`
	Query         *models.Query          `protobuf:"bytes,4,opt,name=query,proto3" json:"query,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DepartmentRequest) Reset() {
	*x = DepartmentRequest{}
	mi := &file_services_user_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DepartmentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DepartmentRequest) ProtoMessage() {}

func (x *DepartmentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_user_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DepartmentRequest.ProtoReflect.Descriptor instead.
func (*DepartmentRequest) Descriptor() ([]byte, []int) {
	return file_services_user_proto_rawDescGZIP(), []int{3}
}

func (x *DepartmentRequest) GetDepartmentId() int64 {
	if x != nil {
		return x.DepartmentId
	}
	return 0
}

func (x *DepartmentRequest) GetDepartmentIds() []int64 {
	if x != nil {
		return x.DepartmentIds
	}
	return nil
}

func (x *DepartmentRequest) GetAncestorIds() []int64 {
	if x != nil {
		return x.AncestorIds
	}
	return nil
}

func (x *DepartmentRequest) GetQuery() *models.Query {
	if x != nil {
		return x.Query
	}
	return nil
}

type DataSetScopeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DataSetId     int64                  `protobuf:"varint,1,opt,name=data_set_id,json=dataSetId,proto3" json:"data_set_id,omitempty"`
	DataSetIds    []int64                `protobuf:"varint,2,rep,packed,name=data_set_ids,json=dataSetIds,proto3" json:"data_set_ids,omitempty"`
	DepartmentIds []int64                `protobuf:"varint,3,rep,packed,name=department_ids,json=departmentIds,proto3" json:"department_ids,omitempty"`
	Query         *models.Query          `protobuf:"bytes,4,opt,name=query,proto3" json:"query,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DataSetScopeRequest) Reset() {
	*x = DataSetScopeRequest{}
	mi := &file_services_user_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DataSetScopeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataSetScopeRequest) ProtoMessage() {}

func (x *DataSetScopeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_user_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataSetScopeRequest.ProtoReflect.Descriptor instead.
func (*DataSetScopeRequest) Descriptor() ([]byte, []int) {
	return file_services_user_proto_rawDescGZIP(), []int{4}
}

func (x *DataSetScopeRequest) GetDataSetId() int64 {
	if x != nil {
		return x.DataSetId
	}
	return 0
}

func (x *DataSetScopeRequest) GetDataSetIds() []int64 {
	if x != nil {
		return x.DataSetIds
	}
	return nil
}

func (x *DataSetScopeRequest) GetDepartmentIds() []int64 {
	if x != nil {
		return x.DepartmentIds
	}
	return nil
}

func (x *DataSetScopeRequest) GetQuery() *models.Query {
	if x != nil {
		return x.Query
	}
	return nil
}

type UserResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	User          *models.User           `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	CacheHit      bool                   `protobuf:"varint,2,opt,name=cache_hit,json=cacheHit,proto3" json:"cache_hit,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserResponse) Reset() {
	*x = UserResponse{}
	mi := &file_services_user_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserResponse) ProtoMessage() {}

func (x *UserResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_user_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserResponse.ProtoReflect.Descriptor instead.
func (*UserResponse) Descriptor() ([]byte, []int) {
	return file_services_user_proto_rawDescGZIP(), []int{5}
}

func (x *UserResponse) GetUser() *models.User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *UserResponse) GetCacheHit() bool {
	if x != nil {
		return x.CacheHit
	}
	return false
}

type ProfileResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Profiles      []*models.UserProfile  `protobuf:"bytes,1,rep,name=profiles,proto3" json:"profiles,omitempty"`
	TotalCount    uint64                 `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ProfileResponse) Reset() {
	*x = ProfileResponse{}
	mi := &file_services_user_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProfileResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProfileResponse) ProtoMessage() {}

func (x *ProfileResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_user_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProfileResponse.ProtoReflect.Descriptor instead.
func (*ProfileResponse) Descriptor() ([]byte, []int) {
	return file_services_user_proto_rawDescGZIP(), []int{6}
}

func (x *ProfileResponse) GetProfiles() []*models.UserProfile {
	if x != nil {
		return x.Profiles
	}
	return nil
}

func (x *ProfileResponse) GetTotalCount() uint64 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

type DepartmentResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Department    []*models.Department   `protobuf:"bytes,1,rep,name=department,proto3" json:"department,omitempty"`
	TotalCount    uint64                 `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DepartmentResponse) Reset() {
	*x = DepartmentResponse{}
	mi := &file_services_user_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DepartmentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DepartmentResponse) ProtoMessage() {}

func (x *DepartmentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_user_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DepartmentResponse.ProtoReflect.Descriptor instead.
func (*DepartmentResponse) Descriptor() ([]byte, []int) {
	return file_services_user_proto_rawDescGZIP(), []int{7}
}

func (x *DepartmentResponse) GetDepartment() []*models.Department {
	if x != nil {
		return x.Department
	}
	return nil
}

func (x *DepartmentResponse) GetTotalCount() uint64 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

type RoleResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Roles         []*models.Role         `protobuf:"bytes,1,rep,name=roles,proto3" json:"roles,omitempty"`
	TotalCount    uint64                 `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RoleResponse) Reset() {
	*x = RoleResponse{}
	mi := &file_services_user_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RoleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RoleResponse) ProtoMessage() {}

func (x *RoleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_user_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RoleResponse.ProtoReflect.Descriptor instead.
func (*RoleResponse) Descriptor() ([]byte, []int) {
	return file_services_user_proto_rawDescGZIP(), []int{8}
}

func (x *RoleResponse) GetRoles() []*models.Role {
	if x != nil {
		return x.Roles
	}
	return nil
}

func (x *RoleResponse) GetTotalCount() uint64 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

type DataSetScopeResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Scopes        []*models.DataSetScope `protobuf:"bytes,1,rep,name=scopes,proto3" json:"scopes,omitempty"`
	TotalCount    uint64                 `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DataSetScopeResponse) Reset() {
	*x = DataSetScopeResponse{}
	mi := &file_services_user_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DataSetScopeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataSetScopeResponse) ProtoMessage() {}

func (x *DataSetScopeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_user_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataSetScopeResponse.ProtoReflect.Descriptor instead.
func (*DataSetScopeResponse) Descriptor() ([]byte, []int) {
	return file_services_user_proto_rawDescGZIP(), []int{9}
}

func (x *DataSetScopeResponse) GetScopes() []*models.DataSetScope {
	if x != nil {
		return x.Scopes
	}
	return nil
}

func (x *DataSetScopeResponse) GetTotalCount() uint64 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

type ProjectRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProjectIds    []int64                `protobuf:"varint,1,rep,packed,name=project_ids,json=projectIds,proto3" json:"project_ids,omitempty"`
	Query         *models.Query          `protobuf:"bytes,2,opt,name=query,proto3" json:"query,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ProjectRequest) Reset() {
	*x = ProjectRequest{}
	mi := &file_services_user_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProjectRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProjectRequest) ProtoMessage() {}

func (x *ProjectRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_user_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProjectRequest.ProtoReflect.Descriptor instead.
func (*ProjectRequest) Descriptor() ([]byte, []int) {
	return file_services_user_proto_rawDescGZIP(), []int{10}
}

func (x *ProjectRequest) GetProjectIds() []int64 {
	if x != nil {
		return x.ProjectIds
	}
	return nil
}

func (x *ProjectRequest) GetQuery() *models.Query {
	if x != nil {
		return x.Query
	}
	return nil
}

type ProjectResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Projects      []*models.Project      `protobuf:"bytes,1,rep,name=projects,proto3" json:"projects,omitempty"`
	TotalCount    uint64                 `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ProjectResponse) Reset() {
	*x = ProjectResponse{}
	mi := &file_services_user_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProjectResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProjectResponse) ProtoMessage() {}

func (x *ProjectResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_user_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProjectResponse.ProtoReflect.Descriptor instead.
func (*ProjectResponse) Descriptor() ([]byte, []int) {
	return file_services_user_proto_rawDescGZIP(), []int{11}
}

func (x *ProjectResponse) GetProjects() []*models.Project {
	if x != nil {
		return x.Projects
	}
	return nil
}

func (x *ProjectResponse) GetTotalCount() uint64 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

type GetUsersByOptionsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CompanyId     int64                  `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	SortField     string                 `protobuf:"bytes,2,opt,name=sort_field,json=sortField,proto3" json:"sort_field,omitempty"`
	IsAsc         bool                   `protobuf:"varint,3,opt,name=is_asc,json=isAsc,proto3" json:"is_asc,omitempty"`
	Ids           []int64                `protobuf:"varint,4,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUsersByOptionsRequest) Reset() {
	*x = GetUsersByOptionsRequest{}
	mi := &file_services_user_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUsersByOptionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUsersByOptionsRequest) ProtoMessage() {}

func (x *GetUsersByOptionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_user_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUsersByOptionsRequest.ProtoReflect.Descriptor instead.
func (*GetUsersByOptionsRequest) Descriptor() ([]byte, []int) {
	return file_services_user_proto_rawDescGZIP(), []int{12}
}

func (x *GetUsersByOptionsRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *GetUsersByOptionsRequest) GetSortField() string {
	if x != nil {
		return x.SortField
	}
	return ""
}

func (x *GetUsersByOptionsRequest) GetIsAsc() bool {
	if x != nil {
		return x.IsAsc
	}
	return false
}

func (x *GetUsersByOptionsRequest) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

type GetUsersByOptionsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Users         []*models.User         `protobuf:"bytes,1,rep,name=users,proto3" json:"users,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUsersByOptionsResponse) Reset() {
	*x = GetUsersByOptionsResponse{}
	mi := &file_services_user_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUsersByOptionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUsersByOptionsResponse) ProtoMessage() {}

func (x *GetUsersByOptionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_user_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUsersByOptionsResponse.ProtoReflect.Descriptor instead.
func (*GetUsersByOptionsResponse) Descriptor() ([]byte, []int) {
	return file_services_user_proto_rawDescGZIP(), []int{13}
}

func (x *GetUsersByOptionsResponse) GetUsers() []*models.User {
	if x != nil {
		return x.Users
	}
	return nil
}

type GetUserPermissionRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        int64                  `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserPermissionRequest) Reset() {
	*x = GetUserPermissionRequest{}
	mi := &file_services_user_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserPermissionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserPermissionRequest) ProtoMessage() {}

func (x *GetUserPermissionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_user_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserPermissionRequest.ProtoReflect.Descriptor instead.
func (*GetUserPermissionRequest) Descriptor() ([]byte, []int) {
	return file_services_user_proto_rawDescGZIP(), []int{14}
}

func (x *GetUserPermissionRequest) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type GetUserPermissionResponse struct {
	state                  protoimpl.MessageState `protogen:"open.v1"`
	HasFullPermission      bool                   `protobuf:"varint,1,opt,name=has_full_permission,json=hasFullPermission,proto3" json:"has_full_permission,omitempty"`
	AllowAccessDescendants bool                   `protobuf:"varint,2,opt,name=allow_access_descendants,json=allowAccessDescendants,proto3" json:"allow_access_descendants,omitempty"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *GetUserPermissionResponse) Reset() {
	*x = GetUserPermissionResponse{}
	mi := &file_services_user_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserPermissionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserPermissionResponse) ProtoMessage() {}

func (x *GetUserPermissionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_user_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserPermissionResponse.ProtoReflect.Descriptor instead.
func (*GetUserPermissionResponse) Descriptor() ([]byte, []int) {
	return file_services_user_proto_rawDescGZIP(), []int{15}
}

func (x *GetUserPermissionResponse) GetHasFullPermission() bool {
	if x != nil {
		return x.HasFullPermission
	}
	return false
}

func (x *GetUserPermissionResponse) GetAllowAccessDescendants() bool {
	if x != nil {
		return x.AllowAccessDescendants
	}
	return false
}

type GetUserProfileResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Profiles      []string               `protobuf:"bytes,1,rep,name=profiles,proto3" json:"profiles,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserProfileResponse) Reset() {
	*x = GetUserProfileResponse{}
	mi := &file_services_user_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserProfileResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserProfileResponse) ProtoMessage() {}

func (x *GetUserProfileResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_user_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserProfileResponse.ProtoReflect.Descriptor instead.
func (*GetUserProfileResponse) Descriptor() ([]byte, []int) {
	return file_services_user_proto_rawDescGZIP(), []int{16}
}

func (x *GetUserProfileResponse) GetProfiles() []string {
	if x != nil {
		return x.Profiles
	}
	return nil
}

var File_services_user_proto protoreflect.FileDescriptor

var file_services_user_proto_rawDesc = string([]byte{
	0x0a, 0x13, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x1a, 0x11, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x75, 0x73,
	0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x11, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72, 0x6f, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x26, 0x0a, 0x0b, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17,
	0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0xcb, 0x01, 0x0a, 0x0e, 0x50, 0x72, 0x6f, 0x66,
	0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x13, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x12, 0x16,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0d,
	0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x12, 0x36, 0x0a,
	0x17, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x74, 0x6f, 0x72, 0x5f, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x03, 0x52, 0x15,
	0x61, 0x6e, 0x63, 0x65, 0x73, 0x74, 0x6f, 0x72, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x49, 0x64, 0x73, 0x22, 0x85, 0x01, 0x0a, 0x0b, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x72, 0x6f, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x19,
	0x0a, 0x08, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03,
	0x52, 0x07, 0x72, 0x6f, 0x6c, 0x65, 0x49, 0x64, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x29, 0x0a, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x13, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x22, 0xad, 0x01,
	0x0a, 0x11, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x64, 0x65, 0x70, 0x61,
	0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x64, 0x65, 0x70, 0x61,
	0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03,
	0x52, 0x0d, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x12,
	0x21, 0x0a, 0x0c, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0b, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x74, 0x6f, 0x72, 0x49,
	0x64, 0x73, 0x12, 0x29, 0x0a, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x13, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x22, 0xa9, 0x01,
	0x0a, 0x13, 0x44, 0x61, 0x74, 0x61, 0x53, 0x65, 0x74, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1e, 0x0a, 0x0b, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x73, 0x65,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x64, 0x61, 0x74, 0x61,
	0x53, 0x65, 0x74, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0c, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x73, 0x65,
	0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0a, 0x64, 0x61, 0x74,
	0x61, 0x53, 0x65, 0x74, 0x49, 0x64, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x64, 0x65, 0x70, 0x61, 0x72,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x52,
	0x0d, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x12, 0x29,
	0x0a, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e,
	0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x52, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x22, 0x53, 0x0a, 0x0c, 0x55, 0x73, 0x65,
	0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x26, 0x0a, 0x04, 0x75, 0x73, 0x65,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x04, 0x75, 0x73, 0x65,
	0x72, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x61, 0x63, 0x68, 0x65, 0x5f, 0x68, 0x69, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x63, 0x61, 0x63, 0x68, 0x65, 0x48, 0x69, 0x74, 0x22, 0x69,
	0x0a, 0x0f, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x35, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x08,
	0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x6f, 0x0a, 0x12, 0x44, 0x65, 0x70,
	0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x38, 0x0a, 0x0a, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0a, 0x64,
	0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x59, 0x0a, 0x0c, 0x52, 0x6f,
	0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x05, 0x72, 0x6f,
	0x6c, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x65, 0x78, 0x6d, 0x73,
	0x67, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x05, 0x72,
	0x6f, 0x6c, 0x65, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x6b, 0x0a, 0x14, 0x44, 0x61, 0x74, 0x61, 0x53, 0x65, 0x74,
	0x53, 0x63, 0x6f, 0x70, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x32, 0x0a,
	0x06, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x44, 0x61, 0x74,
	0x61, 0x53, 0x65, 0x74, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x52, 0x06, 0x73, 0x63, 0x6f, 0x70, 0x65,
	0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x22, 0x5c, 0x0a, 0x0e, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f,
	0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x49, 0x64, 0x73, 0x12, 0x29, 0x0a, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79,
	0x22, 0x65, 0x0a, 0x0f, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x31, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x08, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x81, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x55,
	0x73, 0x65, 0x72, 0x73, 0x42, 0x79, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x6f, 0x72, 0x74, 0x5f, 0x66, 0x69, 0x65, 0x6c,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x6f, 0x72, 0x74, 0x46, 0x69, 0x65,
	0x6c, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x69, 0x73, 0x5f, 0x61, 0x73, 0x63, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x05, 0x69, 0x73, 0x41, 0x73, 0x63, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73,
	0x18, 0x04, 0x20, 0x03, 0x28, 0x03, 0x52, 0x03, 0x69, 0x64, 0x73, 0x22, 0x45, 0x0a, 0x19, 0x47,
	0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x73, 0x42, 0x79, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x05, 0x75, 0x73, 0x65, 0x72,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x05, 0x75, 0x73, 0x65,
	0x72, 0x73, 0x22, 0x33, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x50, 0x65, 0x72,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17,
	0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0x85, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x55,
	0x73, 0x65, 0x72, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x68, 0x61, 0x73, 0x5f, 0x66, 0x75, 0x6c,
	0x6c, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x11, 0x68, 0x61, 0x73, 0x46, 0x75, 0x6c, 0x6c, 0x50, 0x65, 0x72, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x38, 0x0a, 0x18, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x61,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x65, 0x6e, 0x64, 0x61, 0x6e, 0x74,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x16, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x41, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x44, 0x65, 0x73, 0x63, 0x65, 0x6e, 0x64, 0x61, 0x6e, 0x74, 0x73, 0x22,
	0x34, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x70, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x73, 0x32, 0xae, 0x06, 0x0a, 0x0b, 0x55, 0x73, 0x65, 0x72, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x48, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72,
	0x42, 0x79, 0x49, 0x44, 0x12, 0x1b, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x1c, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x4e, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x12, 0x1e,
	0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e,
	0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f,
	0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e,
	0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x57, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x73, 0x12, 0x21, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x2e, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x45, 0x0a, 0x08, 0x47, 0x65, 0x74, 0x52,
	0x6f, 0x6c, 0x65, 0x73, 0x12, 0x1b, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x1c, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x2e, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x5d, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x53, 0x65, 0x74, 0x53, 0x63, 0x6f,
	0x70, 0x65, 0x73, 0x12, 0x23, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x53, 0x65, 0x74, 0x53, 0x63, 0x6f, 0x70,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x53, 0x65,
	0x74, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4e,
	0x0a, 0x0b, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x12, 0x1e, 0x2e,
	0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x50,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e,
	0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x50,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x68,
	0x0a, 0x11, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x73, 0x42, 0x79, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x12, 0x28, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x73, 0x42, 0x79, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e,
	0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x47,
	0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x73, 0x42, 0x79, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x68, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x55,
	0x73, 0x65, 0x72, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x28, 0x2e,
	0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x47,
	0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72,
	0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x62, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x12, 0x28, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x50, 0x65, 0x72,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26,
	0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e,
	0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x3a, 0x5a, 0x38, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x61, 0x37, 0x39, 0x32, 0x33, 0x2f, 0x61, 0x74, 0x68, 0x65, 0x6e,
	0x61, 0x2d, 0x67, 0x6f, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x65, 0x78, 0x6d, 0x73, 0x67,
	0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x3b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_services_user_proto_rawDescOnce sync.Once
	file_services_user_proto_rawDescData []byte
)

func file_services_user_proto_rawDescGZIP() []byte {
	file_services_user_proto_rawDescOnce.Do(func() {
		file_services_user_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_services_user_proto_rawDesc), len(file_services_user_proto_rawDesc)))
	})
	return file_services_user_proto_rawDescData
}

var file_services_user_proto_msgTypes = make([]protoimpl.MessageInfo, 17)
var file_services_user_proto_goTypes = []any{
	(*UserRequest)(nil),               // 0: exmsg.services.UserRequest
	(*ProfileRequest)(nil),            // 1: exmsg.services.ProfileRequest
	(*RoleRequest)(nil),               // 2: exmsg.services.RoleRequest
	(*DepartmentRequest)(nil),         // 3: exmsg.services.DepartmentRequest
	(*DataSetScopeRequest)(nil),       // 4: exmsg.services.DataSetScopeRequest
	(*UserResponse)(nil),              // 5: exmsg.services.UserResponse
	(*ProfileResponse)(nil),           // 6: exmsg.services.ProfileResponse
	(*DepartmentResponse)(nil),        // 7: exmsg.services.DepartmentResponse
	(*RoleResponse)(nil),              // 8: exmsg.services.RoleResponse
	(*DataSetScopeResponse)(nil),      // 9: exmsg.services.DataSetScopeResponse
	(*ProjectRequest)(nil),            // 10: exmsg.services.ProjectRequest
	(*ProjectResponse)(nil),           // 11: exmsg.services.ProjectResponse
	(*GetUsersByOptionsRequest)(nil),  // 12: exmsg.services.GetUsersByOptionsRequest
	(*GetUsersByOptionsResponse)(nil), // 13: exmsg.services.GetUsersByOptionsResponse
	(*GetUserPermissionRequest)(nil),  // 14: exmsg.services.GetUserPermissionRequest
	(*GetUserPermissionResponse)(nil), // 15: exmsg.services.GetUserPermissionResponse
	(*GetUserProfileResponse)(nil),    // 16: exmsg.services.GetUserProfileResponse
	(*models.Query)(nil),              // 17: exmsg.models.Query
	(*models.User)(nil),               // 18: exmsg.models.User
	(*models.UserProfile)(nil),        // 19: exmsg.models.UserProfile
	(*models.Department)(nil),         // 20: exmsg.models.Department
	(*models.Role)(nil),               // 21: exmsg.models.Role
	(*models.DataSetScope)(nil),       // 22: exmsg.models.DataSetScope
	(*models.Project)(nil),            // 23: exmsg.models.Project
}
var file_services_user_proto_depIdxs = []int32{
	17, // 0: exmsg.services.ProfileRequest.query:type_name -> exmsg.models.Query
	17, // 1: exmsg.services.RoleRequest.query:type_name -> exmsg.models.Query
	17, // 2: exmsg.services.DepartmentRequest.query:type_name -> exmsg.models.Query
	17, // 3: exmsg.services.DataSetScopeRequest.query:type_name -> exmsg.models.Query
	18, // 4: exmsg.services.UserResponse.user:type_name -> exmsg.models.User
	19, // 5: exmsg.services.ProfileResponse.profiles:type_name -> exmsg.models.UserProfile
	20, // 6: exmsg.services.DepartmentResponse.department:type_name -> exmsg.models.Department
	21, // 7: exmsg.services.RoleResponse.roles:type_name -> exmsg.models.Role
	22, // 8: exmsg.services.DataSetScopeResponse.scopes:type_name -> exmsg.models.DataSetScope
	17, // 9: exmsg.services.ProjectRequest.query:type_name -> exmsg.models.Query
	23, // 10: exmsg.services.ProjectResponse.projects:type_name -> exmsg.models.Project
	18, // 11: exmsg.services.GetUsersByOptionsResponse.users:type_name -> exmsg.models.User
	0,  // 12: exmsg.services.UserService.GetUserByID:input_type -> exmsg.services.UserRequest
	1,  // 13: exmsg.services.UserService.GetProfiles:input_type -> exmsg.services.ProfileRequest
	3,  // 14: exmsg.services.UserService.GetDepartments:input_type -> exmsg.services.DepartmentRequest
	2,  // 15: exmsg.services.UserService.GetRoles:input_type -> exmsg.services.RoleRequest
	4,  // 16: exmsg.services.UserService.GetDataSetScopes:input_type -> exmsg.services.DataSetScopeRequest
	10, // 17: exmsg.services.UserService.GetProjects:input_type -> exmsg.services.ProjectRequest
	12, // 18: exmsg.services.UserService.GetUsersByOptions:input_type -> exmsg.services.GetUsersByOptionsRequest
	14, // 19: exmsg.services.UserService.GetUserPermission:input_type -> exmsg.services.GetUserPermissionRequest
	14, // 20: exmsg.services.UserService.GetUserProfile:input_type -> exmsg.services.GetUserPermissionRequest
	5,  // 21: exmsg.services.UserService.GetUserByID:output_type -> exmsg.services.UserResponse
	6,  // 22: exmsg.services.UserService.GetProfiles:output_type -> exmsg.services.ProfileResponse
	7,  // 23: exmsg.services.UserService.GetDepartments:output_type -> exmsg.services.DepartmentResponse
	8,  // 24: exmsg.services.UserService.GetRoles:output_type -> exmsg.services.RoleResponse
	9,  // 25: exmsg.services.UserService.GetDataSetScopes:output_type -> exmsg.services.DataSetScopeResponse
	11, // 26: exmsg.services.UserService.GetProjects:output_type -> exmsg.services.ProjectResponse
	13, // 27: exmsg.services.UserService.GetUsersByOptions:output_type -> exmsg.services.GetUsersByOptionsResponse
	15, // 28: exmsg.services.UserService.GetUserPermission:output_type -> exmsg.services.GetUserPermissionResponse
	16, // 29: exmsg.services.UserService.GetUserProfile:output_type -> exmsg.services.GetUserProfileResponse
	21, // [21:30] is the sub-list for method output_type
	12, // [12:21] is the sub-list for method input_type
	12, // [12:12] is the sub-list for extension type_name
	12, // [12:12] is the sub-list for extension extendee
	0,  // [0:12] is the sub-list for field type_name
}

func init() { file_services_user_proto_init() }
func file_services_user_proto_init() {
	if File_services_user_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_services_user_proto_rawDesc), len(file_services_user_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   17,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_services_user_proto_goTypes,
		DependencyIndexes: file_services_user_proto_depIdxs,
		MessageInfos:      file_services_user_proto_msgTypes,
	}.Build()
	File_services_user_proto = out.File
	file_services_user_proto_goTypes = nil
	file_services_user_proto_depIdxs = nil
}
