package handlers

import (
	"encoding/json"
	"net/http"
	"time"

	"gitlab.com/a7923/athena-go/internal/adcrawl/domain"
	http2 "gitlab.com/a7923/athena-go/internal/http"
	"gitlab.com/a7923/athena-go/internal/middlewares"
	"gitlab.com/a7923/athena-go/pkg/logger"
	transhttp "gitlab.com/a7923/athena-go/pkg/transport"
	"gitlab.com/a7923/athena-go/proto/exmsg/services"
)

type ListLandingPagesHandler struct {
	AGSaleClient services.AGSaleService
}

func NewListLandingPagesHandler(AGSaleClient services.AGSaleService) *ListLandingPagesHandler {
	return &ListLandingPagesHandler{
		AGSaleClient: AGSaleClient,
	}
}

type ListLandingPagesResponse struct {
	LandingPages []domain.LandingPage `json:"landing_pages"`
	Count        int                  `json:"count"`
}

func (h *ListLandingPagesHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	defer func() {
		logger.AthenaLogger.Debugw("Processed request", "url", r.URL.Path, "took", time.Since(start).Milliseconds())
	}()

	isAdminVal := r.Header.Get("X-Gw-Isadmin")
	isAdmin := false
	if isAdminVal == "true" {
		isAdmin = true
	}

	grpcGetLandingPagesReq := &services.LandingPageRequest{
		GetAll:                   true,
		LpCompositeKeys:          extractLandingPageCompositeKeys(r),
		IsAdmin:                  isAdmin,
		IsSearchWithCompositeKey: true,
	}
	queryParams := r.URL.Query()
	search := queryParams.Get("search")
	if search != "" {
		grpcGetLandingPagesReq.Search = &search
	}
	grpcGetLandingPagesResp, err := h.AGSaleClient.GetLandingPages(r.Context(), grpcGetLandingPagesReq)
	if err != nil {
		logger.AthenaLogger.Errorf("Failed to get landing pages from agsale service: %v", err)
		transhttp.RespondError(w, http.StatusInternalServerError, "Failed to get landing pages")
		return
	}

	landingPages := make([]domain.LandingPage, len(grpcGetLandingPagesResp.LandingPages))
	for i := range grpcGetLandingPagesResp.LandingPages {
		landingPages[i] = domain.LandingPage{
			ID:   grpcGetLandingPagesResp.LandingPages[i].Id,
			Name: grpcGetLandingPagesResp.LandingPages[i].Name,
		}
	}
	response := ListLandingPagesResponse{
		LandingPages: landingPages,
		Count:        len(landingPages),
	}

	transhttp.RespondJSON(w, http.StatusOK, response)
}

// LandingPageCompositeKey decides how we filter "visible" landing pages for a user
type LandingPageCompositeKey struct {
	CountryID int
	ProjectID int
}

func extractLandingPageCompositeKeys(r *http.Request) []*services.LandingPageCompositeKey {
	scopesHeader := r.Header.Get("X-Gw-Scopes")
	scopes := middlewares.ScopeResponse{}
	if err := json.Unmarshal([]byte(scopesHeader), &scopes); err != nil {
		logger.AthenaLogger.Errorw("Failed to unmarshal scopes", "error", err.Error(), "scopes", scopesHeader,
			"user_id", http2.GetUserIDFromRequest(r))
		return nil
	}
	if len(scopes.Data) == 0 {
		return nil
	}

	keys := make([]*services.LandingPageCompositeKey, 0)
	for _, scope := range scopes.Data {
		if len(scope) < 2 {
			continue
		}
		keys = append(keys, &services.LandingPageCompositeKey{
			CountryId: int32(scope[0]),
			ProjectId: int32(scope[1]),
		})
	}
	return keys
}
