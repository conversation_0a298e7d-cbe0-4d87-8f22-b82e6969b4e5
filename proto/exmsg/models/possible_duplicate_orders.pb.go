// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v3.12.4
// source: possible_duplicate_orders.proto

package models

import (
	timestamp "github.com/golang/protobuf/ptypes/timestamp"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PossibleDuplicateOrders struct {
	state                           protoimpl.MessageState `protogen:"open.v1"`
	CreatedAt                       *timestamp.Timestamp   `protobuf:"bytes,1,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt                       *timestamp.Timestamp   `protobuf:"bytes,2,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	Id                              int32                  `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty"`
	OrderId                         int64                  `protobuf:"varint,4,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	PossibleDuplicateOrderId        int64                  `protobuf:"varint,5,opt,name=possible_duplicate_order_id,json=possibleDuplicateOrderId,proto3" json:"possible_duplicate_order_id,omitempty"`
	PossibleDuplicateOrderDisplayId string                 `protobuf:"bytes,6,opt,name=possible_duplicate_order_display_id,json=possibleDuplicateOrderDisplayId,proto3" json:"possible_duplicate_order_display_id,omitempty"`
	unknownFields                   protoimpl.UnknownFields
	sizeCache                       protoimpl.SizeCache
}

func (x *PossibleDuplicateOrders) Reset() {
	*x = PossibleDuplicateOrders{}
	mi := &file_possible_duplicate_orders_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PossibleDuplicateOrders) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PossibleDuplicateOrders) ProtoMessage() {}

func (x *PossibleDuplicateOrders) ProtoReflect() protoreflect.Message {
	mi := &file_possible_duplicate_orders_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PossibleDuplicateOrders.ProtoReflect.Descriptor instead.
func (*PossibleDuplicateOrders) Descriptor() ([]byte, []int) {
	return file_possible_duplicate_orders_proto_rawDescGZIP(), []int{0}
}

func (x *PossibleDuplicateOrders) GetCreatedAt() *timestamp.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *PossibleDuplicateOrders) GetUpdatedAt() *timestamp.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *PossibleDuplicateOrders) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PossibleDuplicateOrders) GetOrderId() int64 {
	if x != nil {
		return x.OrderId
	}
	return 0
}

func (x *PossibleDuplicateOrders) GetPossibleDuplicateOrderId() int64 {
	if x != nil {
		return x.PossibleDuplicateOrderId
	}
	return 0
}

func (x *PossibleDuplicateOrders) GetPossibleDuplicateOrderDisplayId() string {
	if x != nil {
		return x.PossibleDuplicateOrderDisplayId
	}
	return ""
}

var File_possible_duplicate_orders_proto protoreflect.FileDescriptor

var file_possible_duplicate_orders_proto_rawDesc = string([]byte{
	0x0a, 0x1f, 0x70, 0x6f, 0x73, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x5f, 0x64, 0x75, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x74, 0x65, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x0c, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x1a,
	0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0xc7, 0x02, 0x0a, 0x17, 0x50, 0x6f, 0x73, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x44, 0x75, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x12, 0x39, 0x0a, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x3d, 0x0a,
	0x1b, 0x70, 0x6f, 0x73, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x5f, 0x64, 0x75, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x65, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x18, 0x70, 0x6f, 0x73, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x44, 0x75, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x4c, 0x0a, 0x23,
	0x70, 0x6f, 0x73, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x5f, 0x64, 0x75, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x74, 0x65, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1f, 0x70, 0x6f, 0x73, 0x73, 0x69,
	0x62, 0x6c, 0x65, 0x44, 0x75, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x49, 0x64, 0x42, 0x36, 0x5a, 0x34, 0x67, 0x69,
	0x74, 0x6c, 0x61, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x61, 0x37, 0x39, 0x32, 0x33, 0x2f, 0x61,
	0x74, 0x68, 0x65, 0x6e, 0x61, 0x2d, 0x67, 0x6f, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x65,
	0x78, 0x6d, 0x73, 0x67, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x3b, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_possible_duplicate_orders_proto_rawDescOnce sync.Once
	file_possible_duplicate_orders_proto_rawDescData []byte
)

func file_possible_duplicate_orders_proto_rawDescGZIP() []byte {
	file_possible_duplicate_orders_proto_rawDescOnce.Do(func() {
		file_possible_duplicate_orders_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_possible_duplicate_orders_proto_rawDesc), len(file_possible_duplicate_orders_proto_rawDesc)))
	})
	return file_possible_duplicate_orders_proto_rawDescData
}

var file_possible_duplicate_orders_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_possible_duplicate_orders_proto_goTypes = []any{
	(*PossibleDuplicateOrders)(nil), // 0: exmsg.models.PossibleDuplicateOrders
	(*timestamp.Timestamp)(nil),     // 1: google.protobuf.Timestamp
}
var file_possible_duplicate_orders_proto_depIdxs = []int32{
	1, // 0: exmsg.models.PossibleDuplicateOrders.created_at:type_name -> google.protobuf.Timestamp
	1, // 1: exmsg.models.PossibleDuplicateOrders.updated_at:type_name -> google.protobuf.Timestamp
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_possible_duplicate_orders_proto_init() }
func file_possible_duplicate_orders_proto_init() {
	if File_possible_duplicate_orders_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_possible_duplicate_orders_proto_rawDesc), len(file_possible_duplicate_orders_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_possible_duplicate_orders_proto_goTypes,
		DependencyIndexes: file_possible_duplicate_orders_proto_depIdxs,
		MessageInfos:      file_possible_duplicate_orders_proto_msgTypes,
	}.Build()
	File_possible_duplicate_orders_proto = out.File
	file_possible_duplicate_orders_proto_goTypes = nil
	file_possible_duplicate_orders_proto_depIdxs = nil
}
