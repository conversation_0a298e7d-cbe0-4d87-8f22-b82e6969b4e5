package repository

import (
	"context"

	"github.com/Masterminds/squirrel"
	"github.com/jmoiron/sqlx"
	"gitlab.com/a7923/athena-go/internal/adcrawl/domain"
	"gitlab.com/a7923/athena-go/pkg/logger"
)

var _ domain.AdSetRepository = (*PostgresAdSetRepository)(nil)

type PostgresAdSetRepository struct {
	db           *sqlx.DB
	queryBuilder squirrel.StatementBuilderType
}

func NewPostgresAdsetRepository(db *sqlx.DB) *PostgresAdSetRepository {
	return &PostgresAdSetRepository{
		db:           db,
		queryBuilder: squirrel.StatementBuilder.PlaceholderFormat(squirrel.Dollar),
	}
}

const (
	adsetsTable = "fb.adsets"
)

var (
	insertedAdsetColumns = []string{
		"id",
		"name",
		"status",
		"effective_status",
		"optimization_goal",
		"campaign_id",
		"adaccount_id",
		"market",
		"market_id",
	}
)

func (pr *PostgresAdSetRepository) BatchInsert(ctx context.Context, adsets []domain.AdSet, scheduled bool) error {
	qb := pr.queryBuilder.Insert(adsetsTable).Columns(insertedAdsetColumns...).
		SuffixExpr(squirrel.Expr(`
			ON CONFLICT (id) DO UPDATE SET
			name = EXCLUDED.name,
			status = EXCLUDED.status,
			effective_status = EXCLUDED.effective_status,
			optimization_goal = EXCLUDED.optimization_goal,
			campaign_id = EXCLUDED.campaign_id,
			adaccount_id = EXCLUDED.adaccount_id,
			market = EXCLUDED.market,
			market_id = EXCLUDED.market_id,
			updated_at = NOW(),
			deleted_at = NULL
		`))

	var marketName *string
	var marketID *int
	for _, adset := range adsets {
		if adset.MarketName != "" {
			marketName = &adset.MarketName
			marketID = &adset.MarketID
		} else {
			marketName = nil
			marketID = nil
		}
		qb = qb.Values(
			adset.ID,
			adset.Name,
			adset.Status,
			adset.EffectiveStatus,
			adset.OptimizationGoal,
			adset.CampaignID,
			adset.AdAccountID,
			marketName,
			marketID,
		)
	}

	insertQuery, args, err := qb.ToSql()
	if err != nil {
		logger.AthenaLogger.Errorf("failed to build insert query: %v", err)
		return err
	}

	_, err = pr.db.ExecContext(ctx, insertQuery, args...)
	if err != nil {
		logger.AthenaLogger.Errorf("failed to insert adsets: %v", err)
		return err
	}

	return nil
}
