package handlers

import (
	"context"
	"fmt"
	"github.com/ThreeDotsLabs/watermill"
	"github.com/ThreeDotsLabs/watermill/message"
	"github.com/goccy/go-json"
	"github.com/spf13/viper"
	"gitlab.com/a7923/athena-go/internal"
	"gitlab.com/a7923/athena-go/pkg/app"
	"gitlab.com/a7923/athena-go/pkg/logger"
	"gitlab.com/a7923/athena-go/pkg/utils"
	"gitlab.com/a7923/athena-go/proto/exmsg/models"
	"gitlab.com/a7923/athena-go/proto/exmsg/services"
	"go-micro.dev/v5/client"
	"google.golang.org/protobuf/types/known/structpb"
	"strings"
)

type WebhookHandler struct {
	Publisher                         app.PublisherInterface
	WebhookClient                     services.WebhookService
	Name                              string
	outputPublishWebhookKind2Exchange map[string]string
	exchangeRoutingKey2WebhookKind    map[string]string
	enableLog                         bool
}

func (h *WebhookHandler) GetName() string {
	return h.Name
}

func (h *WebhookHandler) Init() error {
	h.outputPublishWebhookKind2Exchange = viper.GetStringMapString("output_webhook_kind_to_exchange")
	h.exchangeRoutingKey2WebhookKind = make(map[string]string)

	if h.outputPublishWebhookKind2Exchange == nil {
		h.outputPublishWebhookKind2Exchange = make(map[string]string)
	}

	for kind, listExRouting := range viper.GetStringMapStringSlice("exchange_routing_key_to_webhook_kind") {
		for _, exRouting := range listExRouting {
			h.exchangeRoutingKey2WebhookKind[exRouting] = kind
		}
	}

	h.enableLog = viper.GetBool("persist_log.enable")
	h.WebhookClient = internal.CreateWebhookClient(nil)
	return nil
}

func (h *WebhookHandler) HandleMessage(msg *message.Message) error {
	webhook := models.WebhookEvent{}
	_ = json.Unmarshal(msg.Payload, &webhook)

	if len(webhook.MessageUuid) == 0 {
		webhook.MessageUuid = msg.UUID
	}

	if len(webhook.MessageUuid) == 0 {
		webhook.MessageUuid = watermill.NewUUID()
	}

	if len(webhook.Kind) == 0 {
		webhook.Kind = h.getWebhookKind(msg)
	}

	if webhook.Payload == nil {
		webhook.Payload = &structpb.Struct{}
		_ = webhook.Payload.UnmarshalJSON(msg.Payload)
	}

	// cast msg.Metadata to map[string]string
	meta := map[string]string(msg.Metadata)
	interfaceMap := map[string]interface{}{}
	for k, v := range meta {
		interfaceMap[k] = v
	}

	webhook.Meta, _ = structpb.NewStruct(interfaceMap)

	// save log to db
	if h.enableLog || webhook.IsRetry {
		_, err := h.WebhookClient.InsertLogs(
			context.Background(),
			&models.WebhookEvents{Events: []*models.WebhookEvent{&webhook}},
			client.WithRetries(5),
		)

		if err != nil {
			webhook.IsRetry = true
			perr := h.Publisher.PublishRouting(h.Name, "", utils.ToJSONByte(webhook))
			if perr != nil {
				logger.AthenaLogger.Errorw("Could not push message to rmq",
					"exchange", h.Name,
					"routing_key", "",
					"error", perr.Error(),
				)
				return perr
			}
			logger.AthenaLogger.Errorw("Could not insert webhook logs", "error", err.Error())
			return nil
		}
		logger.AthenaLogger.Debugw("Insert webhook logs successfully", "webhook", webhook.MessageUuid)
	}

	// forwarder
	exWithRoutingKey := strings.Split(h.outputPublishWebhookKind2Exchange[webhook.Kind], ":")
	if len(exWithRoutingKey) == 0 {
		return nil
	}

	exchange := exWithRoutingKey[0]
	if len(exchange) == 0 {
		return nil
	}

	routingKey := ""
	if len(exWithRoutingKey) > 1 {
		routingKey = exWithRoutingKey[1]
	}

	payload, _ := webhook.Payload.MarshalJSON()
	err := h.Publisher.PublishRouting(exchange, routingKey, payload)
	if err != nil {
		logger.AthenaLogger.Errorw("Could not push message to rmq",
			"exchange", exchange,
			"routing_key", routingKey,
			"error", err.Error(),
		)
		return err
	}

	return nil
}

func (h *WebhookHandler) SetPublisher(p app.PublisherInterface) {
	h.Publisher = p
}

func (h *WebhookHandler) Close() {
}

func (h *WebhookHandler) getWebhookKind(m *message.Message) string {
	key := fmt.Sprintf("%s:%s", m.Metadata["exchange"], m.Metadata["routing_key"])
	keyAll := fmt.Sprintf("%s:*", m.Metadata["exchange"])
	kind, ok := h.exchangeRoutingKey2WebhookKind[key]
	if ok {
		return kind
	}

	kind, ok = h.exchangeRoutingKey2WebhookKind[keyAll]
	if ok {
		return kind
	}

	return internal.WebhookKindUnknown
}
