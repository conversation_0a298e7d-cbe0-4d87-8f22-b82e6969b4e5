package permissions

// Permissions with their relationships for modules 15 to 30
var ModuleRelations = map[Permission][]Permission{
	Dashboard: {
		DashboardOrderShipmentOverview,
		DashboardOrderShipmentCarrier,
		DashboardOrderShipmentSaleReps,
		DashboardOrderShipmentFilter,
		DashboardCarePageOverview,
		DashboardCarePagePerformance,
		DashboardCarePageReasons,
		DashboardCarePageFilter,
		DashboardTelesalesOverview,
		DashboardTelesalesPerformance,
		DashboardTelesalesReasons,
		DashboardTelesalesFilter,
		DashboardMarketingCarePage,
		DashboardMarketingTelesales,
	},
	Order: {
		OrderFetchMany,
		OrderBasicSearch,
		OrderAdvanceSearch,
		OrderDuplicateFilter,
		OrderExportOrders,
		OrderCreate,
		OrderBulkCreate,
		OrderFetchOne,
		OrderEditProductsAndFees,
		OrderEditGeneralInformation,
		OrderEditCustomerInformation,
		OrderEditDeliveryInformation,
		OrderEditTags,
		OrderUpdateStatus,
		OrderCancel,
		OrderReadHistories,
		OrderBulkUpdateStatus,
		OrderBulkUpdateSaleReps,
		OrderBulkUpdateTags,
		OrderBulkUpdateSource,
		OrderBulkSyncOrderToFFM,
	},
	Customer: {
		CustomerFetchMany,
		CustomerFetchOne,
		CustomerUpdate,
		CustomerCreate,
	},
	Product: {
		ProductFetchMany,
		ProductExport,
		ProductImport,
		ProductCreate,
		ProductCreateCombo,
		ProductFetchOne,
		ProductUpdate,
	},
	Supplier: {
		SupplierFetchMany,
		SupplierFetchOne,
		SupplierCreate,
		SupplierUpdate,
	},
	Inventory: {
		InventoryFetchMany,
	},
	Inbound: {
		InboundFetchMany,
		InboundFetchOne,
		InboundCreate,
		InboundUpdate,
		InboundUpdateStatus,
	},
	Outbound: {
		OutboundFetchMany,
		OutboundFetchOne,
		OutboundCreate,
		OutboundUpdate,
		OutboundUpdateStatus,
	},
	Stocktaking: {
		StocktakingFetchMany,
		StocktakingFetchOne,
		StocktakingCreate,
		StocktakingUpdate,
		StocktakingUpdateStatus,
	},
	ReturnHandling: {
		ReturnHandlingFetchMany,
		ReturnHandlingFetchOne,
		ReturnHandlingCreate,
		ReturnHandlingUpdate,
		ReturnHandlingUpdateStatus,
	},
	Telesales: {
		TelesalesFetchAssignedLeads,
		TelesalesAssignedLeadsFilter,
		TelesalesTakeCareLeads,
		TelesalesAppointments,
		TelesalesFetchOne,
		TelesalesEditProductsAndFees,
		TelesalesEditGeneralInformation,
		TelesalesEditCustomerInformation,
		TelesalesEditDeliveryInformation,
		TelesalesEditTags,
		TelesalesCreateCareReason,
		TelesalesEditSource,
		TelesalesActionLogs,
		TelesalesFetchLeads,
		TelesalesLeadsFilter,
		TelesalesManualDistribute,
		TelesalesManualRevoke,
		TelesalesExportExcel,
		TelesalesImportExcel,
		TelesalesDistributeConfig,
		TelesalesProcessingProcedureConfig,
	},
	CarePage: {
		CarePageFetchPageGroups,
		CarePageCreatePageGroup,
		CarePageUpdatePageGroup,
		CarePagePageGroupsAdvanceFilter,
		CarePageManualDistribute,
		CarePageManualRevoke,
		CarePageBulkUpdate,
		CarePageProcess,
		CarePageCreateOrder,
		CarePageCreateAppointment,
		CarePageActionLogs,
		CarePageFetchConfigGroups,
		CarePageCreateConfigGroup,
		CarePageUpdateConfigGroup,
		CarePageRemoveConfigGroup,
		CarePageLimitationSettings,
		CarePageAIConfigurations,
		CarePageAIProductConfigurations,
	},
	BotManagement: {
		BotManagementFetchMany,
		BotManagementCreate,
		BotManagementUpdate,
		BotManagementCrawlBotList,
		BotManagementBotAdsManager,
	},
	FanPages: {
		FanPagesFetchMany,
		FanPagesLink,
	},
	Campaigns: {
		CampaignsFetchMany,
		CampaignsFetchOne,
		CampaignsCreate,
	},
	Projects: {
		ProjectsFetchMany,
		ProjectsFetchOne,
		ProjectsCreate,
		ProjectsUpdate,
		ProjectsUpdateStatus,
		ProjectsFFMIntegrate,
		ProjectsActionLogs,
	},
	Countries: {
		CountriesFetchMany,
		CountriesCreate,
		CountriesUpdateStatus,
	},
	Accounts: {
		AccountsFetchMany,
		AccountsFetchOne,
		AccountsCreate,
		AccountsUpdate,
		AccountsUpdateStatus,
		AccountsActionLogs,
	},
	Roles: {
		RolesFetchMany,
		RolesFetchOne,
		RolesCreate,
		RolesUpdate,
		RolesUpdateStatus,
		RolesActionLogs,
	},
	Org: {
		OrgFetch,
		OrgUpdate,
	},
	Shift: {
		ShiftFetchMany,
		ShiftFetchOne,
		ShiftCreate,
		ShiftUpdate,
		ShiftSchedules,
		ShiftAssign,
	},
	Tags: {
		TagsFetchMany,
		TagsCreate,
		TagsUpdate,
	},
	OrderSource: {
		OrderSourceFetchMany,
		OrderSourceCreate,
		OrderSourceUpdate,
	},
	ReportReason: {
		ReportReasonFetchMany,
		ReportReasonCreate,
		ReportReasonUpdate,
	},
	CancelReason: {
		CancelReasonFetchMany,
		CancelReasonCreate,
		CancelReasonUpdate,
	},
	PrintNotes: {
		PrintNotesFetchMany,
		PrintNotesCreate,
		PrintNotesUpdate,
	},
	Translation: {
		TranslationFetchMany,
		TranslationUpdate,
	},
	Fulfillment: {
		FulfillmentFetchMany,
		FulfillmentFetchOne,
		FulfillmentIntegrate,
		FulfillmentDisintegrate,
	},
	Marketplace: {
		MarketplaceFetchMany,
		MarketplaceUpdate,
		MarketplaceCreate,
	},
	Currency: {
		CurrencyFetchMany,
		CurrencyUpdate,
		CurrencyCreate,
	},
	DeviceManagement: {
		DeviceManagementFetchMany,
		DeviceManagementCreate,
	},
}
