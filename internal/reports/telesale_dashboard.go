package reports

import (
	"fmt"
	"github.com/Masterminds/squirrel"
	"gitlab.com/a7923/athena-go/internal/enums"
	"gitlab.com/a7923/athena-go/pkg/utils"
	"gitlab.com/a7923/athena-go/proto/exmsg/models"
	"gitlab.com/a7923/athena-go/proto/exmsg/services"
	"strconv"
	"strings"
	"time"
)

type TelesaleDashboard struct{}

func (t *TelesaleDashboard) GetQuery(req *services.TelesaleDashboardRequest) (squirrel.SelectBuilder, error) {
	if req.IsViewPerformance {
		return getPerformance(req), nil
	}

	// Pre grouping data before add condition base on what does request param have
	selectColumns := getSelectColumnsTelesaleDashboard(req)
	groupBy := req.GroupBy

	qb := squirrel.Select(selectColumns...)

	if len(groupBy) > 0 && groupBy[0] == string(ProductId) {
		qb = qb.From("base_leads l ARRAY JOIN l.product_items as product_items")
	} else {
		qb = qb.From("base_leads l")
	}
	qb = qb.
		Where(squirrel.Eq{"company_id": req.CompanyId}).
		Where(squirrel.Eq{"country_id": req.CountryId})

	qb = buildTelesaleDashboardDimensionWhere(req, qb)

	if len(groupBy) > 0 && groupBy[0] == string(ProductId) {
		qb = qb.GroupBy("product_id")
	} else if len(groupBy) > 0 && groupBy[0] != string(Overview) {
		if groupBy[0] == string(CreatedAt) || groupBy[0] == string(ProcessingTime) {
			qb = qb.GroupBy("date")
		} else if groupBy[0] == string(SaleRepId) {
			qb = qb.GroupBy("sale_id")
		} else {
			qb = qb.GroupBy(groupBy...)
		}
	}
	qb = qb.OrderBy(req.OrderBy...)
	// get all data to return full option data
	if len(req.Action) == 0 {
		qb = qb.Limit(req.Limit)
	}
	qb = qb.Offset(req.Offset)
	return qb, nil
}

func (t *TelesaleDashboard) ProcessRow(row map[string]interface{}, req *services.TelesaleDashboardRequest) (*models.Row, error) {
	if row == nil {
		return nil, nil
	}
	return &models.Row{Values: utils.ToJSONByte(row)}, nil
}

func buildTelesaleDashboardDimensionWhere(req *services.TelesaleDashboardRequest, qb squirrel.SelectBuilder) squirrel.SelectBuilder {
	loc := getTimeZone(req.TimeZone)
	now := time.Now().In(loc)

	// Lấy ngày 128 ngày trước (11/3/2025)
	daysBefore128 := now.AddDate(0, 0, -128)

	// startToday và endToday của ngày 128 ngày trước (11/3/2025)
	startToday := time.Date(daysBefore128.Year(), daysBefore128.Month(), daysBefore128.Day(), 0, 0, 0, 0, loc).Format(LayoutTime)
	endToday := time.Date(daysBefore128.Year(), daysBefore128.Month(), daysBefore128.Day(), 23, 59, 59, 0, loc).Format(LayoutTime)
	if len(req.ProjectIds) > 0 {
		qb = qb.Where(squirrel.Expr("project_id IN (?)", req.ProjectIds))
	}

	if len(req.ProductIds) > 0 {
		qb = qb.Where(squirrel.Expr(`hasAny(product_ids, ?)`, req.ProductIds))
	}
	if len(req.MarketerIds) > 0 {
		qb = qb.Where(squirrel.Expr("marketer_id IN (?)", req.MarketerIds))
	}
	if len(req.SaleReps) > 0 {
		qb = qb.Where(squirrel.Expr("sale_id IN (?)", req.SaleReps))
	}

	if len(req.CareState) > 0 {
		qb = qb.Where(squirrel.Eq{"l.state ": req.CareState})
	}

	if len(req.Sources) > 0 {
		qb = qb.Where(squirrel.Expr(`hasAny(external_sources,?)`, req.Sources))
	}

	if len(req.TagIds) > 0 {
		qb = qb.Where(squirrel.Expr(`hasAny(tag_ids, ?)`, req.TagIds))
	}
	if len(req.SaleId) > 0 && len(req.CarePageId) > 0 {
		qb = qb.Where(squirrel.Or{
			squirrel.Expr("sale_id IN (?)", req.SaleId),
			squirrel.Expr("care_page_id IN (?)", req.CarePageId),
		})
	}
	if len(req.SaleId) > 0 && len(req.CarePageId) == 0 {
		qb = qb.Where(squirrel.Expr("sale_id IN (?)", req.SaleId))
	}
	if len(req.CarePageId) > 0 && len(req.SaleId) == 0 {
		qb = qb.Where(squirrel.Expr("care_page_id IN (?)", req.CarePageId))
	}
	if len(req.TeamInCharge) > 0 {
		conditions := make([]squirrel.Sqlizer, 0)
		for _, v := range req.TeamInCharge {
			conditions = append(conditions, squirrel.Eq{"team_in_charge": v})
			if v == TeamInChargeTelesale {
				conditions = append(conditions, squirrel.Expr("sale_id IS NOT NULL"))
			}
			if v == TeamInChargeCarePage {
				conditions = append(conditions, squirrel.Expr("care_page_id IS NOT NULL"))
			}
		}
		qb = qb.Where(squirrel.Or(conditions))
	}

	if len(req.OrderStatus) > 0 {
		qb = qb.Where(squirrel.Expr("order_status IN (?)", req.OrderStatus))
	}

	if len(req.GroupBy) > 0 && req.GroupBy[0] == string(SaleRepId) {
		qb = qb.Where(squirrel.Expr("sale_id IN (?)", req.Dids))
		if req.IsViewToday {
			qb = qb.Where(squirrel.GtOrEq{"l.current_care_time": startToday})
			qb = qb.Where(squirrel.Lt{"l.current_care_time": endToday})
		}
	}

	if len(req.CollectMethod) > 0 {
		qb = qb.Where(squirrel.Expr("l.collect_type IN (?)", req.CollectMethod))
	}

	switch req.DateRangeType {
	case CreatedTime:
		if len(req.StartTime) > 0 {
			qb = qb.Where(squirrel.GtOrEq{"l.created_at": req.StartTime})
		}
		if len(req.EndTime) > 0 {
			qb = qb.Where(squirrel.Lt{"l.created_at": req.EndTime})
		}
	case ProcessingTime:
		if len(req.StartTime) > 0 {
			qb = qb.Where(squirrel.GtOrEq{"l.processing_time": req.StartTime})
		}
		if len(req.EndTime) > 0 {
			qb = qb.Where(squirrel.Lt{"l.processing_time": req.EndTime})
		}
	}

	if len(req.LastCareReasonId) > 0 {
		qb = qb.Where(squirrel.Expr("l.last_care_reason_id IN (?)", req.LastCareReasonId))
	}
	return qb
}

func OrderStatusListToSQLString(statuses []enums.OrderStatus) string {
	result := make([]string, len(statuses))
	for i, s := range statuses {
		result[i] = strconv.Itoa(int(s))
	}
	return strings.Join(result, ", ")
}

func CareStateListToSQLString(statuses []enums.CareState) string {
	result := make([]string, len(statuses))
	for i, s := range statuses {
		result[i] = strconv.Itoa(int(s))
	}
	return strings.Join(result, ", ")
}

func getTimeZone(tz string) *time.Location {
	loc, err := time.LoadLocation(tz)
	if err != nil {
		loc, _ = time.LoadLocation("Asia/Ho_Chi_Minh")
	}
	return loc
}

func quoteStrings(s []string) []string {
	quoted := make([]string, len(s))
	for i, v := range s {
		quoted[i] = fmt.Sprintf("'%s'", v)
	}
	return quoted
}

func getPerformance(req *services.TelesaleDashboardRequest) squirrel.SelectBuilder {
	subQueryBuilder := squirrel.Select(
		"toDate(toTimeZone(processing_time, 'Asia/Ho_Chi_Minh')) AS date",
		"state",
		"sale_id",
		"count() AS cnt",
		"toDate(toTimeZone(created_at, 'Asia/Ho_Chi_Minh')) = toDate(toTimeZone(processing_time, 'Asia/Ho_Chi_Minh')) AS is_new",
		"toDate(toTimeZone(created_at, 'Asia/Ho_Chi_Minh')) < toDate(toTimeZone(processing_time, 'Asia/Ho_Chi_Minh')) AS is_old",
	).
		From("base_leads as l").
		GroupBy("date", "state", "is_new", "is_old", "sale_id")

	subQueryBuilder = buildTelesaleDashboardDimensionWhere(req, subQueryBuilder)

	performanceViewDetail := []string{
		//fmt.Sprintf("date"),
		//fmt.Sprintf("sale_id"),
		fmt.Sprintf("countIf(state IN (%s) AND is_new = 1) AS new_assigned_leads", CareStateListToSQLString(enums.AssignedLead)),
		fmt.Sprintf("countIf(state IN (%s) AND is_new = 1) AS new_processed_leads", CareStateListToSQLString(enums.ProcessedLead)),
		fmt.Sprintf("countIf(state IN (%s) AND is_new = 1) AS new_confirmed_leads", CareStateListToSQLString(enums.ConfirmedLead)),

		fmt.Sprintf("countIf(state IN (%s) AND is_old = 1) AS old_assigned_leads", CareStateListToSQLString(enums.AssignedLead)),
		fmt.Sprintf("countIf(state IN (%s) AND is_old = 1) AS old_processed_leads", CareStateListToSQLString(enums.ProcessedLead)),
		fmt.Sprintf("countIf(state IN (%s) AND is_old = 1) AS old_confirmed_leads", CareStateListToSQLString(enums.ConfirmedLead)),

		fmt.Sprintf("countIf(state IN (%s)) AS total_assigned_leads", CareStateListToSQLString(enums.AssignedLead)),
		fmt.Sprintf("countIf(state IN (%s)) AS total_processed_leads", CareStateListToSQLString(enums.ProcessedLead)),
		fmt.Sprintf("countIf(state IN (%s)) AS total_confirmed_leads", CareStateListToSQLString(enums.ConfirmedLead)),

		"sumIf(cnt, state = 0 AND is_new = 1) AS new_carestate_new_total",
		"sumIf(cnt, state = 1 AND is_new = 1) AS new_carestate_unassign_attempted",
		"sumIf(cnt, state = 2 AND is_new = 1) AS new_carestate_assigned",
		"sumIf(cnt, state = 3 AND is_new = 1) AS new_carestate_noattempt",
		"sumIf(cnt, state = 4 AND is_new = 1) AS new_carestate_attempted",
		"sumIf(cnt, state = 5 AND is_new = 1) AS new_carestate_potential",
		"sumIf(cnt, state = 6 AND is_new = 1) AS new_carestate_awaiting_stock",
		"sumIf(cnt, state = 7 AND is_new = 1) AS new_carestate_reconfirm",
		"sumIf(cnt, state = 8 AND is_new = 1) AS new_carestate_confirmed",
		"sumIf(cnt, state = 9 AND is_new = 1) AS new_carestate_failed",
		"sumIf(cnt, state = 10 AND is_new = 1) AS new_carestate_lost",
		"sumIf(cnt, state = 11 AND is_new = 1) AS new_carestate_junk",
		"sumIf(cnt, state = 12 AND is_new = 1) AS new_carestate_temp",

		"sumIf(cnt, state = 0 AND is_old = 1) AS old_carestate_new_total",
		"sumIf(cnt, state = 1 AND is_old = 1) AS old_carestate_unassign_attempted",
		"sumIf(cnt, state = 2 AND is_old = 1) AS old_carestate_assigned",
		"sumIf(cnt, state = 3 AND is_old = 1) AS old_carestate_noattempt",
		"sumIf(cnt, state = 4 AND is_old = 1) AS old_carestate_attempted",
		"sumIf(cnt, state = 5 AND is_old = 1) AS old_carestate_potential",
		"sumIf(cnt, state = 6 AND is_old = 1) AS old_carestate_awaiting_stock",
		"sumIf(cnt, state = 7 AND is_old = 1) AS old_carestate_reconfirm",
		"sumIf(cnt, state = 8 AND is_old = 1) AS old_carestate_confirmed",
		"sumIf(cnt, state = 9 AND is_old = 1) AS old_carestate_failed",
		"sumIf(cnt, state = 10 AND is_old = 1) AS old_carestate_lost",
		"sumIf(cnt, state = 11 AND is_old = 1) AS old_carestate_junk",
		"sumIf(cnt, state = 12 AND is_old = 1) AS old_carestate_temp",
	}

	if req.GroupBy[0] == string(CreatedTime) {
		performanceViewDetail = append(performanceViewDetail, fmt.Sprintf("date"))
	} else {
		performanceViewDetail = append(performanceViewDetail, fmt.Sprintf("sale_id"))
	}

	mainQueryBuilder := squirrel.Select(performanceViewDetail...).
		FromSelect(subQueryBuilder, "subquery").
		Limit(50).
		Offset(0)

	if req.GroupBy[0] == string(CreatedTime) {
		mainQueryBuilder = mainQueryBuilder.
			GroupBy("date").
			OrderBy("date DESC")
	} else {
		mainQueryBuilder = mainQueryBuilder.
			GroupBy("sale_id")
	}

	return mainQueryBuilder
}

func getSelectColumnsTelesaleDashboard(req *services.TelesaleDashboardRequest) []string {
	loc := getTimeZone(req.TimeZone)
	now := time.Now().In(loc)
	startToday := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, loc).Format(LayoutTime)
	endToday := time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 0, loc).Format(LayoutTime)
	timezone := loc.String()
	selectColumns := make([]string, 0)
	if len(req.Action) > 0 {
		selectColumns = append(selectColumns, []string{
			"groupArray(DISTINCT project_id) AS project_ids_data",
			"groupArray(DISTINCT sale_id) AS sale_id_data",
			"arrayDistinct(arrayFlatten(groupArray(product_ids))) AS product_ids_data",
			"arrayDistinct(arrayFlatten(groupArray(external_sources))) AS external_sources_data",
			"arrayDistinct(arrayFlatten(groupArray(tag_ids))) AS tag_ids_data",
			"groupArray(DISTINCT marketer_id) AS marketer_ids_data",
			"groupArray(DISTINCT state) AS state_data",
		}...)
		return selectColumns
	}

	selectByLeadState := []string{
		fmt.Sprintf("countIf(l.state = %d) AS carestate_new_total", enums.CareState_New),
		fmt.Sprintf("countIf(l.state  = %d) AS carestate_unassign_attempted", enums.CareState_UnassignAttempted),
		fmt.Sprintf("countIf(l.state  = %d) AS carestate_assigned", enums.CareState_Assigned),
		fmt.Sprintf("countIf(l.state  = %d) AS carestate_noattempt", enums.CareState_NoAttempt),
		fmt.Sprintf("countIf(l.state  = %d) AS carestate_attempted", enums.CareState_Attempted),
		fmt.Sprintf("countIf(l.state  = %d) AS carestate_potential", enums.CareState_Potential),
		fmt.Sprintf("countIf(l.state  = %d) AS carestate_awaiting_stock", enums.CareState_AwaitingStock),
		fmt.Sprintf("countIf(l.state  = %d) AS carestate_reconfirm", enums.CareState_Reconfirm),
		fmt.Sprintf("countIf(l.state  = %d) AS carestate_confirmed", enums.CareState_Confirmed),
		fmt.Sprintf("countIf(l.state  = %d) AS carestate_failed", enums.CareState_Failed),
		fmt.Sprintf("countIf(l.state  = %d) AS carestate_lost", enums.CareState_Lost),
		fmt.Sprintf("countIf(l.state  = %d) AS carestate_junk", enums.CareState_Junk),
		fmt.Sprintf("countIf(l.state  = %d) AS carestate_temp", enums.CareState_Temp),
	}

	selectByRevenue := []string{
		fmt.Sprintf("sum(l.revenue) as revenue"),
		fmt.Sprintf("sum(l.discount) as discount"),
		fmt.Sprintf("sum(l.shipping_fee) as shipping_fee"),
		fmt.Sprintf("sum(l.surcharge) as surcharge"),
	}

	selectQuantityByOrderStatus := []string{
		"sum(toFloat64(splitByChar(':', product_items)[2])) AS sold_quantity",
		fmt.Sprintf("sumIf(toFloat64(splitByChar(':', product_items)[2]), order_status = %d) AS order_new_quantity_total", enums.New),
		fmt.Sprintf("sumIf(toFloat64(splitByChar(':', product_items)[2]), order_status = %d) AS order_awaiting_stock_quantity_total", enums.AwaitingStock),
		fmt.Sprintf("sumIf(toFloat64(splitByChar(':', product_items)[2]), order_status = %d) AS order_reconfirm_quantity_total", enums.Reconfirm),
		fmt.Sprintf("sumIf(toFloat64(splitByChar(':', product_items)[2]), order_status = %d) AS order_confirmed_quantity_total", enums.Confirmed),
		fmt.Sprintf("sumIf(toFloat64(splitByChar(':', product_items)[2]), order_status = %d) AS order_preparing_quantity_total", enums.Preparing),
		fmt.Sprintf("sumIf(toFloat64(splitByChar(':', product_items)[2]), order_status = %d) AS order_handling_over_quantity_total", enums.HandlingOver),
		fmt.Sprintf("sumIf(toFloat64(splitByChar(':', product_items)[2]), order_status = %d) AS order_in_transit_quantity_total", enums.InTransit),
		fmt.Sprintf("sumIf(toFloat64(splitByChar(':', product_items)[2]), order_status = %d) AS order_in_delivery_quantity_total", enums.InDelivery),
		fmt.Sprintf("sumIf(toFloat64(splitByChar(':', product_items)[2]), order_status = %d) AS order_delivered_quantity_total", enums.Delivered),
		fmt.Sprintf("sumIf(toFloat64(splitByChar(':', product_items)[2]), order_status = %d) AS order_delivered_completed_quantity_total", enums.DeliveredCompleted),
		fmt.Sprintf("sumIf(toFloat64(splitByChar(':', product_items)[2]), order_status = %d) AS order_failed_delivery_quantity_total", enums.FailedDelivery),
		fmt.Sprintf("sumIf(toFloat64(splitByChar(':', product_items)[2]), order_status = %d) AS order_awaiting_return_quantity_total", enums.AwaitingReturn),
		fmt.Sprintf("sumIf(toFloat64(splitByChar(':', product_items)[2]), order_status = %d) AS order_in_return_quantity_total", enums.InReturn),
		fmt.Sprintf("sumIf(toFloat64(splitByChar(':', product_items)[2]), order_status = %d) AS order_returned_stocked_quantity_total", enums.ReturnedStocked),
		fmt.Sprintf("sumIf(toFloat64(splitByChar(':', product_items)[2]), order_status = %d) AS order_returned_completed_quantity_total", enums.ReturnedCompleted),
		fmt.Sprintf("sumIf(toFloat64(splitByChar(':', product_items)[2]), order_status = %d) AS order_damaged_quantity_total", enums.Damaged),
		fmt.Sprintf("sumIf(toFloat64(splitByChar(':', product_items)[2]), order_status = %d) AS order_damaged_completed_quantity_total", enums.DamagedCompleted),
		fmt.Sprintf("sumIf(toFloat64(splitByChar(':', product_items)[2]), order_status = %d) AS order_canceled_quantity_total", enums.Canceled),
		fmt.Sprintf("sumIf(toFloat64(splitByChar(':', product_items)[2]), order_status = %d) AS order_lost_quantity_total", enums.Lost),
		fmt.Sprintf("sumIf(toFloat64(splitByChar(':', product_items)[2]), order_status = %d) AS order_lost_completed_quantity_total", enums.LostCompleted),
	}

	viewDetail := []string{
		fmt.Sprintf("sumIf(toFloat64(splitByChar(':', product_items)[2]), l.state IN (%s)) AS confirmed_quantity_total",
			CareStateListToSQLString(enums.ConfirmedLead)),
		// Doanh số
		fmt.Sprintf("sumIf(toFloat64(splitByChar(':', product_items)[3]), l.state IN (%s)) AS confirmed_sales_total",
			CareStateListToSQLString(enums.ConfirmedLead)),
		// Doanh thu
		fmt.Sprintf("sumIf(toFloat64(splitByChar(':', product_items)[3]), (l.state IN (%s) AND l.order_status IN (%d, %d))) AS confirmed_revenue_total",
			CareStateListToSQLString(enums.ConfirmedLead),
			enums.Delivered,
			enums.DeliveredCompleted,
		),
		fmt.Sprintf("sumIf(toFloat64(splitByChar(':', product_items)[2]), l.order_status IN (%d, %d)) AS delivered_quantity_total",
			enums.Delivered,
			enums.DeliveredCompleted),
		fmt.Sprintf("sumIf(toFloat64(splitByChar(':', product_items)[2]), l.order_status IN (%s)) AS junk_quantity_total", OrderStatusListToSQLString(enums.JunkOrderStatus)),
		fmt.Sprintf("countIf(l.order_status = %d) as canceled_quantity_total", enums.Canceled),
	}

	TodayPerformanceBySaleReps := []string{
		fmt.Sprintf("countIf(l.state IN(%s)) AS all_incharged_leads", CareStateListToSQLString(enums.AssignedLead)),
		// ...
	}

	CVRBySaleReps := []string{
		fmt.Sprintf("countIf(l.state IN (%s)) AS all_processed_leads", CareStateListToSQLString(enums.ProcessedLead)),
		fmt.Sprintf("countIf(l.state IN (%s)) AS all_confirmed_leads", CareStateListToSQLString(enums.ConfirmedLead)),
		fmt.Sprintf("countIf(l.state IN (%s) AND (l.created_at >= '%v' AND l.created_at <= '%v')) AS new_processed_leads", CareStateListToSQLString(enums.ProcessedLead), startToday, endToday),
		fmt.Sprintf("countIf(l.state IN (%s) AND (l.created_at >= '%v' AND l.created_at <= '%v')) AS new_confirmed_leads", CareStateListToSQLString(enums.ConfirmedLead), startToday, endToday),
		fmt.Sprintf("countIf(l.state IN (%s) AND l.created_at <= '%v') AS old_processed_leads", CareStateListToSQLString(enums.ProcessedLead), startToday),
		fmt.Sprintf("countIf(l.state IN (%s) AND l.created_at <= '%v') AS old_confirmed_leads", CareStateListToSQLString(enums.ConfirmedLead), startToday),
		fmt.Sprintf("if((new_processed_leads + old_confirmed_leads) = 0, 0, all_confirmed_leads / (new_processed_leads + old_confirmed_leads)) as sales_rate"),
		fmt.Sprintf("if(new_processed_leads = 0, 0, new_confirmed_leads / new_processed_leads) as cvr_new"),
		fmt.Sprintf("if(old_processed_leads = 0, 0, old_confirmed_leads / old_processed_leads) as cvr_old"),
		//New confirmed leads => FE Calculate
	}

	ableToProcessReason := []string{
		enums.ConfirmedReason,
		enums.NoResponse,
		string(enums.CannotReach),
		enums.CallBackLater,
		enums.AwaitingConfirmation,
		enums.WrongNumber,
		enums.WrongOwner,
		enums.PriceFactor,
		enums.MindChanging,
		enums.PurchaseElsewhere,
		enums.AccidentalPurchase,
		enums.Joking,
		enums.UrgentProblem,
		enums.NoMoney,
		enums.ProductDoesNotMeetExpectation,
	}

	connectedReason := []string{
		enums.ConfirmedReason,
		enums.CallBackLater,
		enums.AwaitingConfirmation,
		enums.WrongOwner,
		enums.PriceFactor,
		enums.MindChanging,
		enums.PurchaseElsewhere,
		enums.AccidentalPurchase,
		enums.Joking,
		enums.UrgentProblem,
		enums.NoMoney,
		enums.ProductDoesNotMeetExpectation,
	}

	notConnectedReason := []string{
		enums.NoResponse,
		string(enums.CannotReach),
		enums.WrongNumber,
	}

	trashReason := []string{
		enums.DuplicateOrder,
		enums.TestOrder,
		enums.WrongPhoneNumberFormat,
	}

	viewCareReasonDetail := []string{
		fmt.Sprintf("countIf(l.state IS NOT NULL AND l.state != %d) AS processable_total", enums.CareState_Junk),
		fmt.Sprintf(
			"countIf(l.state IS NOT NULL AND l.state != %d AND last_care_reason_key IN (%s)) AS processed_total",
			enums.CareState_Junk,
			strings.Join(quoteStrings(ableToProcessReason), ","),
		),
		fmt.Sprintf(
			"countIf(l.state IS NOT NULL AND l.state != %d AND last_care_reason_key IN (%s)) AS connected_total",
			enums.CareState_Junk,
			strings.Join(quoteStrings(connectedReason), ","),
		),
		fmt.Sprintf(
			"countIf(l.state IS NOT NULL AND l.state != %d AND last_care_reason_key IN (%s)) AS not_connected_total",
			enums.CareState_Junk,
			strings.Join(quoteStrings(notConnectedReason), ","),
		),
		fmt.Sprintf(
			"countIf(l.state IS NOT NULL AND l.state != %d AND last_care_reason_key IN (%s)) AS trash_total",
			enums.CareState_Junk,
			strings.Join(quoteStrings(trashReason), ","),
		),
		fmt.Sprintf(
			"countIf(l.state IS NOT NULL AND l.state != %d AND last_care_reason_key = '%s') AS cannot_reach_total",
			enums.CareState_Junk,
			enums.CannotReach,
		),
		fmt.Sprintf(
			"countIf(l.state IS NOT NULL AND l.state != %d AND last_care_reason_key = '%s') AS no_response_total",
			enums.CareState_Junk,
			enums.NoResponse,
		),
		fmt.Sprintf(
			"countIf(l.state IS NOT NULL AND l.state != %d AND last_care_reason_key = '%s') AS confirmed_reason_total",
			enums.CareState_Junk,
			enums.ConfirmedReason,
		),
		fmt.Sprintf(
			"countIf(l.state IS NOT NULL AND l.state != %d AND last_care_reason_key = '%s') AS wrong_number_total",
			enums.CareState_Junk,
			enums.WrongNumber,
		),
		fmt.Sprintf(
			"countIf(l.state IS NOT NULL AND l.state != %d AND last_care_reason_key = '%s') AS mind_changing_total",
			enums.CareState_Junk,
			enums.MindChanging,
		),
		fmt.Sprintf(
			"countIf(l.state IS NOT NULL AND l.state != %d AND last_care_reason_key = '%s') AS urgent_problem_total",
			enums.CareState_Junk,
			enums.UrgentProblem,
		),
		fmt.Sprintf(
			"countIf(l.state IS NOT NULL AND l.state != %d AND last_care_reason_key = '%s') AS no_money_total",
			enums.CareState_Junk,
			enums.NoMoney,
		),
		fmt.Sprintf(
			"countIf(l.state IS NOT NULL AND l.state != %d AND last_care_reason_key = '%s') AS product_does_not_meet_expectation_total",
			enums.CareState_Junk,
			enums.ProductDoesNotMeetExpectation,
		),
		fmt.Sprintf(
			"countIf(l.state IS NOT NULL AND l.state != %d AND last_care_reason_key = '%s') AS purchase_elsewhere_total",
			enums.CareState_Junk,
			enums.PurchaseElsewhere,
		),
		fmt.Sprintf(
			"countIf(l.state IS NOT NULL AND l.state != %d AND last_care_reason_key = '%s') AS accidental_purchase_total",
			enums.CareState_Junk,
			enums.AccidentalPurchase,
		),
		fmt.Sprintf(
			"countIf(l.state IS NOT NULL AND l.state != %d AND last_care_reason_key = '%s') AS joking_total",
			enums.CareState_Junk,
			enums.Joking,
		),
		fmt.Sprintf(
			"countIf(l.state IS NOT NULL AND l.state != %d AND last_care_reason_key = '%s') AS duplicate_order_total",
			enums.CareState_Junk,
			enums.DuplicateOrder,
		),
		fmt.Sprintf(
			"countIf(l.state IS NOT NULL AND l.state != %d AND last_care_reason_key = '%s') AS test_order_total",
			enums.CareState_Junk,
			enums.TestOrder,
		),
		fmt.Sprintf(
			"countIf(l.state IS NOT NULL AND l.state != %d AND last_care_reason_key = '%s') AS wrong_phone_number_format_total",
			enums.CareState_Junk,
			enums.WrongPhoneNumberFormat,
		),
		fmt.Sprintf(
			"countIf(l.state IS NOT NULL AND l.state != %d AND last_care_reason_key = '%s') AS awaiting_confirmation_total",
			enums.CareState_Junk,
			enums.AwaitingConfirmation,
		),
		fmt.Sprintf(
			"countIf(l.state IS NOT NULL AND l.state != %d AND last_care_reason_key = '%s') AS wrong_owner_total",
			enums.CareState_Junk,
			enums.WrongOwner,
		),
		fmt.Sprintf(
			"countIf(l.state IS NOT NULL AND l.state != %d AND last_care_reason_key = '%s') AS price_factor_total",
			enums.CareState_Junk,
			enums.PriceFactor,
		),
		fmt.Sprintf(
			"countIf(l.state IS NOT NULL AND l.state != %d AND last_care_reason_key = '%s') AS call_back_later_total",
			enums.CareState_Junk,
			enums.CallBackLater,
		),
		fmt.Sprintf(
			"countIf(l.state IS NOT NULL AND l.state != %d AND last_care_reason_key = '%s') AS blank_total",
			enums.CareState_Junk,
			enums.Blank,
		),
	}

	for _, groupBy := range req.GroupBy {
		switch groupBy {
		case string(Overview):
			selectColumns = append(selectColumns, selectByLeadState...)
		case string(Status):
			selectColumns = append(selectColumns, []string{
				"order_status",
				"count(*) AS total_order",
				"sum(total_quantity) AS quantity_total",
				"sum(cod) AS revenue",
			}...)
		case string(Project):
			selectColumns = append(selectColumns, "project_id")
			if req.IsViewDetail {
				selectColumns = append(selectColumns, viewDetail...)
			}
			if req.IsViewDetailByCareReason {
				selectColumns = append(selectColumns, viewCareReasonDetail...)
			} else {
				selectColumns = append(selectColumns, selectByLeadState...)
				selectColumns = append(selectColumns, selectByRevenue...)
			}
		case string(CreatedAt):
			selectColumns = append(selectColumns, fmt.Sprintf("toDate(toTimeZone(%s, '%s')) AS date", groupBy, timezone))
			selectColumns = append(selectColumns, selectByLeadState...)
			selectColumns = append(selectColumns, selectByRevenue...)
			//selectColumns = append(selectColumns, countTotalSelectColumns...)
			if req.IsViewDetail {
				selectColumns = append(selectColumns, viewDetail...)
			}
			if req.IsViewDetailByCareReason {
				selectColumns = append(selectColumns, viewCareReasonDetail...)
			} else {
				selectColumns = append(selectColumns, selectByLeadState...)
				selectColumns = append(selectColumns, selectByRevenue...)
			}
		case string(ProductId):
			selectColumns = append(selectColumns, []string{
				"splitByChar(':', product_items)[1] AS product_id",
				fmt.Sprintf(
					"sumIf(toFloat64(splitByChar(':', product_items)[2]), order_status NOT IN (%d, %d)) AS total_quantity_without_draft_and_canceled",
					enums.Draft,
					enums.Canceled,
				),
				fmt.Sprintf(
					"sumIf(toFloat64(splitByChar(':', product_items)[2]), order_status IN (%d, %d, %d, %d, %d)) AS returned_total_quantity ",
					enums.AwaitingReturn,
					enums.InReturn,
					enums.ReturnedStocked,
					enums.ReturnedCompleted,
					enums.Canceled,
				),
				fmt.Sprintf(
					"sumIf(toFloat64(splitByChar(':', product_items)[2]), order_status IN (%d, %d)) AS delivered_total_quantity ",
					enums.Delivered,
					enums.DeliveredCompleted,
				),
			}...)
			if req.IsViewDetail {
				selectColumns = append(selectColumns, selectQuantityByOrderStatus...)
				selectColumns = append(selectColumns, viewDetail...)
			}
		case string(SaleRepId):
			selectColumns = append(selectColumns, "sale_id")
			if req.IsViewToday {
				selectColumns = append(selectColumns, TodayPerformanceBySaleReps...)
				selectColumns = append(selectColumns, selectByLeadState...)
			}
			if req.IsViewDetail {
				selectColumns = append(selectColumns, viewDetail...)
			}
			if req.IsViewDetailByCareReason {
				selectColumns = append(selectColumns, viewCareReasonDetail...)
			} else {
				selectColumns = append(selectColumns, CVRBySaleReps...)
			}
		case string(CareReason):
			selectColumns = append(selectColumns, "last_care_reason_key")
			selectColumns = append(selectColumns, selectByLeadState...)
			selectColumns = append(selectColumns, []string{
				fmt.Sprintf("count(*) AS total_lead_all_state"),
				fmt.Sprintf("countIf(l.state != %d) AS total_lead_without_junk_state", enums.CareState_Junk),
			}...)
		}
	}
	return selectColumns
}
