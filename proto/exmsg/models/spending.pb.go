// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: proto/models/spending.proto

package models

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Spending struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AdId          int64                  `protobuf:"varint,1,opt,name=ad_id,json=adId,proto3" json:"ad_id,omitempty"`
	DateStart     string                 `protobuf:"bytes,2,opt,name=date_start,json=dateStart,proto3" json:"date_start,omitempty"`
	HourlyStat    int32                  `protobuf:"varint,3,opt,name=hourly_stat,json=hourlyStat,proto3" json:"hourly_stat,omitempty"`
	RawNumeric    float64                `protobuf:"fixed64,4,opt,name=raw_numeric,json=rawNumeric,proto3" json:"raw_numeric,omitempty"`
	Cpm           float64                `protobuf:"fixed64,5,opt,name=cpm,proto3" json:"cpm,omitempty"`
	Cpc           float64                `protobuf:"fixed64,6,opt,name=cpc,proto3" json:"cpc,omitempty"`
	Ctr           float64                `protobuf:"fixed64,7,opt,name=ctr,proto3" json:"ctr,omitempty"`
	Clicks        float64                `protobuf:"fixed64,8,opt,name=clicks,proto3" json:"clicks,omitempty"`
	Frequency     float64                `protobuf:"fixed64,9,opt,name=frequency,proto3" json:"frequency,omitempty"`
	P25           float64                `protobuf:"fixed64,10,opt,name=p25,proto3" json:"p25,omitempty"`
	P50           float64                `protobuf:"fixed64,11,opt,name=p50,proto3" json:"p50,omitempty"`
	P75           float64                `protobuf:"fixed64,12,opt,name=p75,proto3" json:"p75,omitempty"`
	P95           float64                `protobuf:"fixed64,13,opt,name=p95,proto3" json:"p95,omitempty"`
	VideoAverage  float64                `protobuf:"fixed64,14,opt,name=video_average,json=videoAverage,proto3" json:"video_average,omitempty"`
	Impressions   float64                `protobuf:"fixed64,15,opt,name=impressions,proto3" json:"impressions,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Spending) Reset() {
	*x = Spending{}
	mi := &file_proto_models_spending_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Spending) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Spending) ProtoMessage() {}

func (x *Spending) ProtoReflect() protoreflect.Message {
	mi := &file_proto_models_spending_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Spending.ProtoReflect.Descriptor instead.
func (*Spending) Descriptor() ([]byte, []int) {
	return file_proto_models_spending_proto_rawDescGZIP(), []int{0}
}

func (x *Spending) GetAdId() int64 {
	if x != nil {
		return x.AdId
	}
	return 0
}

func (x *Spending) GetDateStart() string {
	if x != nil {
		return x.DateStart
	}
	return ""
}

func (x *Spending) GetHourlyStat() int32 {
	if x != nil {
		return x.HourlyStat
	}
	return 0
}

func (x *Spending) GetRawNumeric() float64 {
	if x != nil {
		return x.RawNumeric
	}
	return 0
}

func (x *Spending) GetCpm() float64 {
	if x != nil {
		return x.Cpm
	}
	return 0
}

func (x *Spending) GetCpc() float64 {
	if x != nil {
		return x.Cpc
	}
	return 0
}

func (x *Spending) GetCtr() float64 {
	if x != nil {
		return x.Ctr
	}
	return 0
}

func (x *Spending) GetClicks() float64 {
	if x != nil {
		return x.Clicks
	}
	return 0
}

func (x *Spending) GetFrequency() float64 {
	if x != nil {
		return x.Frequency
	}
	return 0
}

func (x *Spending) GetP25() float64 {
	if x != nil {
		return x.P25
	}
	return 0
}

func (x *Spending) GetP50() float64 {
	if x != nil {
		return x.P50
	}
	return 0
}

func (x *Spending) GetP75() float64 {
	if x != nil {
		return x.P75
	}
	return 0
}

func (x *Spending) GetP95() float64 {
	if x != nil {
		return x.P95
	}
	return 0
}

func (x *Spending) GetVideoAverage() float64 {
	if x != nil {
		return x.VideoAverage
	}
	return 0
}

func (x *Spending) GetImpressions() float64 {
	if x != nil {
		return x.Impressions
	}
	return 0
}

var File_proto_models_spending_proto protoreflect.FileDescriptor

var file_proto_models_spending_proto_rawDesc = string([]byte{
	0x0a, 0x1b, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x73,
	0x70, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c, 0x65,
	0x78, 0x6d, 0x73, 0x67, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x22, 0xfb, 0x02, 0x0a, 0x08,
	0x53, 0x70, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x13, 0x0a, 0x05, 0x61, 0x64, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x61, 0x64, 0x49, 0x64, 0x12, 0x1d, 0x0a,
	0x0a, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x72, 0x74, 0x12, 0x1f, 0x0a, 0x0b,
	0x68, 0x6f, 0x75, 0x72, 0x6c, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0a, 0x68, 0x6f, 0x75, 0x72, 0x6c, 0x79, 0x53, 0x74, 0x61, 0x74, 0x12, 0x1f, 0x0a,
	0x0b, 0x72, 0x61, 0x77, 0x5f, 0x6e, 0x75, 0x6d, 0x65, 0x72, 0x69, 0x63, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x0a, 0x72, 0x61, 0x77, 0x4e, 0x75, 0x6d, 0x65, 0x72, 0x69, 0x63, 0x12, 0x10,
	0x0a, 0x03, 0x63, 0x70, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x63, 0x70, 0x6d,
	0x12, 0x10, 0x0a, 0x03, 0x63, 0x70, 0x63, 0x18, 0x06, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x63,
	0x70, 0x63, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x74, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x03, 0x63, 0x74, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x73, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x73, 0x12, 0x1c, 0x0a, 0x09,
	0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x09, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x32,
	0x35, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x70, 0x32, 0x35, 0x12, 0x10, 0x0a, 0x03,
	0x70, 0x35, 0x30, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x70, 0x35, 0x30, 0x12, 0x10,
	0x0a, 0x03, 0x70, 0x37, 0x35, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x70, 0x37, 0x35,
	0x12, 0x10, 0x0a, 0x03, 0x70, 0x39, 0x35, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x70,
	0x39, 0x35, 0x12, 0x23, 0x0a, 0x0d, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x61, 0x76, 0x65, 0x72,
	0x61, 0x67, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x76, 0x69, 0x64, 0x65, 0x6f,
	0x41, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x69, 0x6d, 0x70, 0x72, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0b, 0x69, 0x6d,
	0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x42, 0x36, 0x5a, 0x34, 0x67, 0x69, 0x74,
	0x6c, 0x61, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x61, 0x37, 0x39, 0x32, 0x33, 0x2f, 0x61, 0x74,
	0x68, 0x65, 0x6e, 0x61, 0x2d, 0x67, 0x6f, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x65, 0x78,
	0x6d, 0x73, 0x67, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x3b, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_proto_models_spending_proto_rawDescOnce sync.Once
	file_proto_models_spending_proto_rawDescData []byte
)

func file_proto_models_spending_proto_rawDescGZIP() []byte {
	file_proto_models_spending_proto_rawDescOnce.Do(func() {
		file_proto_models_spending_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proto_models_spending_proto_rawDesc), len(file_proto_models_spending_proto_rawDesc)))
	})
	return file_proto_models_spending_proto_rawDescData
}

var file_proto_models_spending_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_proto_models_spending_proto_goTypes = []any{
	(*Spending)(nil), // 0: exmsg.models.Spending
}
var file_proto_models_spending_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_proto_models_spending_proto_init() }
func file_proto_models_spending_proto_init() {
	if File_proto_models_spending_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proto_models_spending_proto_rawDesc), len(file_proto_models_spending_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_proto_models_spending_proto_goTypes,
		DependencyIndexes: file_proto_models_spending_proto_depIdxs,
		MessageInfos:      file_proto_models_spending_proto_msgTypes,
	}.Build()
	File_proto_models_spending_proto = out.File
	file_proto_models_spending_proto_goTypes = nil
	file_proto_models_spending_proto_depIdxs = nil
}
