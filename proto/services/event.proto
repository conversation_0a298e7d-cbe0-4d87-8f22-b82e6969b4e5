syntax = "proto3";

package exmsg.services;

option go_package = "gitlab.com/a7923/athena-go/proto/exmsg/services;services";

import "models/event.proto";
import "models/common.proto";

service EventService {
  rpc GetEvents (EventRequest) returns (EventResponse);
  rpc CreateEvents (EventRequest) returns (EventResponse);
}

message EventRequest {
  exmsg.models.Query query = 1;
  int64 id = 2;
  repeated int64 ids = 3;
  repeated exmsg.models.Event events = 4;
}

message EventResponse {
  repeated exmsg.models.Event event = 1;
  exmsg.models.SQLResult exec_result = 2;
}

