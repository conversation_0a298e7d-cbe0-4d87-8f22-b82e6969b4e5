// Code generated by protoc-gen-micro. DO NOT EDIT.
// source: services/event.proto

package services

import (
	fmt "fmt"
	_ "gitlab.com/a7923/athena-go/proto/exmsg/models"
	proto "google.golang.org/protobuf/proto"
	math "math"
)

import (
	context "context"
	client "go-micro.dev/v5/client"
	server "go-micro.dev/v5/server"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ client.Option
var _ server.Option

// Client API for EventService service

type EventService interface {
	GetEvents(ctx context.Context, in *EventRequest, opts ...client.CallOption) (*EventResponse, error)
	CreateEvents(ctx context.Context, in *EventRequest, opts ...client.CallOption) (*EventResponse, error)
}

type eventService struct {
	c    client.Client
	name string
}

func NewEventService(name string, c client.Client) EventService {
	return &eventService{
		c:    c,
		name: name,
	}
}

func (c *eventService) GetEvents(ctx context.Context, in *EventRequest, opts ...client.CallOption) (*EventResponse, error) {
	req := c.c.NewRequest(c.name, "EventService.GetEvents", in)
	out := new(EventResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventService) CreateEvents(ctx context.Context, in *EventRequest, opts ...client.CallOption) (*EventResponse, error) {
	req := c.c.NewRequest(c.name, "EventService.CreateEvents", in)
	out := new(EventResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for EventService service

type EventServiceHandler interface {
	GetEvents(context.Context, *EventRequest, *EventResponse) error
	CreateEvents(context.Context, *EventRequest, *EventResponse) error
}

func RegisterEventServiceHandler(s server.Server, hdlr EventServiceHandler, opts ...server.HandlerOption) error {
	type eventService interface {
		GetEvents(ctx context.Context, in *EventRequest, out *EventResponse) error
		CreateEvents(ctx context.Context, in *EventRequest, out *EventResponse) error
	}
	type EventService struct {
		eventService
	}
	h := &eventServiceHandler{hdlr}
	return s.Handle(s.NewHandler(&EventService{h}, opts...))
}

type eventServiceHandler struct {
	EventServiceHandler
}

func (h *eventServiceHandler) GetEvents(ctx context.Context, in *EventRequest, out *EventResponse) error {
	return h.EventServiceHandler.GetEvents(ctx, in, out)
}

func (h *eventServiceHandler) CreateEvents(ctx context.Context, in *EventRequest, out *EventResponse) error {
	return h.EventServiceHandler.CreateEvents(ctx, in, out)
}
