package handlers

import (
	"context"
	"github.com/ThreeDotsLabs/watermill/message"
	"github.com/goccy/go-json"
	"github.com/golang/protobuf/ptypes/timestamp"
	"gitlab.com/a7923/athena-go/internal"
	"gitlab.com/a7923/athena-go/internal/enums"
	"gitlab.com/a7923/athena-go/pkg/app"
	"gitlab.com/a7923/athena-go/pkg/dbtool"
	"gitlab.com/a7923/athena-go/pkg/logger"
	"gitlab.com/a7923/athena-go/pkg/replication/pglogicalstream"
	"gitlab.com/a7923/athena-go/pkg/utils"
	"gitlab.com/a7923/athena-go/proto/exmsg/models"
	"gitlab.com/a7923/athena-go/proto/exmsg/services"
	"time"
)

const (
	TableFilter = "orders"
)

type OrderHandler struct {
	UserClient   services.UserService
	Publisher    app.PublisherInterface
	Name         string
	AGSaleClient services.AGSaleService
}

func (h *OrderHandler) GetName() string {
	return h.Name
}

func (h *OrderHandler) Init() error {
	h.UserClient = internal.CreateNewUserServiceClient(nil)
	h.AGSaleClient = internal.CreateAGSaleClient(nil)
	return nil
}

func (h *OrderHandler) HandleMessage(msg *message.Message) error {
	//start := time.Now()
	//defer func() {
	//	logger.AthenaLogger.Infof("done took %v", time.Since(start))
	//}()
	payload := pglogicalstream.Wal2JsonChanges{}
	_ = json.Unmarshal(msg.Payload, &payload)

	if len(payload.Changes) == 0 {
		return nil
	}

	for _, change := range payload.Changes {
		if change.Table != TableFilter {
			continue
		}

		err := h.handleOrderChanged(change)
		if err != nil {
			logger.AthenaLogger.Errorw("failed to handle order.proto change", "error", err, "id", msg.UUID)
			return err
		}
	}

	return nil
}

func (h *OrderHandler) SetPublisher(p app.PublisherInterface) {
	h.Publisher = p
}

func (h *OrderHandler) Close() {
}

func (h *OrderHandler) updateRevertStatus(ctx context.Context, updateLead *models.Lead, order *models.Order) error {
	// update lead status
	// update care lead status
	// update care lead item reason + notes
	if updateLead.State != int64(enums.CareState_Junk) {
		logger.AthenaLogger.Infow("Cancel lead", "lead", updateLead.Id)
		updateLead.State = int64(enums.CareState_Junk)
		updateFields := []string{"state"}
		_, err := h.AGSaleClient.UpdateLead(ctx, &services.LeadRequest{
			Lead:         updateLead,
			UpdateFields: updateFields,
			Id:           updateLead.Id,
		})
		if err != nil {
			logger.AthenaLogger.Errorw("Error updating lead", "error", err, "lead_id", updateLead.Id)
			return err
		}
	}

	orderUpdateFields := []string{"status"}
	order.Status = int64(enums.Canceled)
	_, err := h.AGSaleClient.UpdateOrder(ctx, &services.OrderRequest{
		Order:        order,
		UpdateFields: orderUpdateFields,
		Id:           order.Id,
	})
	_ = h.Publisher.PublishRouting("order-service", "after-order-update", utils.ToJSONByte(map[string]interface{}{
		"id": order.Id,
		"updatedAt": &timestamp.Timestamp{
			Seconds: time.Now().Unix(),
			Nanos:   int32(time.Now().Nanosecond()),
		},
		"updatedBy": enums.UpdatedBySystem,
	}))
	logger.AthenaLogger.Infow("Cancel order of lead", "order", order.Id)
	// create care
	leadCare := &models.LeadCare{
		UserId:    updateLead.UserId,
		LeadId:    updateLead.Id,
		State:     int64(enums.CareState_Junk),
		UpdatedBy: enums.UpdatedBySystem,
	}

	if leadCare.UserId < 1 {
		leadCare.UserId = enums.UpdatedBySystem
	}

	reason, err := h.AGSaleClient.GetCareReasons(ctx, &services.CareReasonRequest{
		CompanyId: order.CompanyId,
		ReasonKey: string(enums.CareReasonKey_OriginOrderStatusReverted),
		Query:     &models.Query{Limit: 1},
	}, dbtool.WithFieldSelect("id", "name"))
	if err != nil {
		logger.AthenaLogger.Errorw("Error getting care reasons", "error", err, "company_id", order.CompanyId, "reason_key", "origin_order_status_reverted")
		return err
	}

	defaultCareReason := &models.CareReason{
		Id:   1,
		Name: "Đơn gốc bị cập nhật ngược trạng thái",
	}

	if len(reason.GetCareReasons()) > 0 {
		defaultCareReason = reason.CareReasons[0]
	}

	careItem := &models.LeadCareItem{
		ReasonId: defaultCareReason.Id,
		//Note:     defaultCareReason.Name,
		LeadId: updateLead.Id,
	}

	careItemResp, err := h.AGSaleClient.CreateLeadCareAndItem(ctx, &services.LeadRequest{
		LeadCare:     leadCare,
		LeadCareItem: careItem,
	})

	if err != nil {
		logger.AthenaLogger.Errorw("Error creating care", "error", err, "lead_id", updateLead.Id)
		return err
	}
	logger.AthenaLogger.Infow("Created care", "care_id", careItemResp.ExecResult.LastInsertIds)

	// handle duplicate order
	_, dErr := h.AGSaleClient.RemoveDuplicateOrder(ctx, &services.OrderRequest{
		Order: order,
	})

	if dErr != nil {
		logger.AthenaLogger.Errorw("Error when remove duplicate order", "error", err, "order", order.Id)
		return err
	}

	// handle duplicate lead
	_, dLeadErr := h.AGSaleClient.RemoveDuplicateLead(ctx, &services.LeadRequest{
		Id: updateLead.Id,
	})
	if dLeadErr != nil {
		logger.AthenaLogger.Errorw("Error when remove duplicate lead", "error", err, "lead", updateLead.Id)
		return err
	}
	return nil
}
