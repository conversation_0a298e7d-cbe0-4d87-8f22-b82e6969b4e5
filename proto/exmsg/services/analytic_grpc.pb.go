// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v3.21.12
// source: services/analytic.proto

package services

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	AnalyticService_QueryReport_FullMethodName                    = "/exmsg.services.AnalyticService/QueryReport"
	AnalyticService_Query_FullMethodName                          = "/exmsg.services.AnalyticService/Query"
	AnalyticService_QueryOrderDashboardOverview_FullMethodName    = "/exmsg.services.AnalyticService/QueryOrderDashboardOverview"
	AnalyticService_QueryTelesaleDashboardOverview_FullMethodName = "/exmsg.services.AnalyticService/QueryTelesaleDashboardOverview"
)

// AnalyticServiceClient is the client API for AnalyticService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AnalyticServiceClient interface {
	QueryReport(ctx context.Context, in *AnalyticsRequest, opts ...grpc.CallOption) (*AnalyticsResponse, error)
	Query(ctx context.Context, in *AnalyticsRequest, opts ...grpc.CallOption) (*AnalyticsResponse, error)
	QueryOrderDashboardOverview(ctx context.Context, in *OrderDashboardRequest, opts ...grpc.CallOption) (*AnalyticsResponse, error)
	// Telesale dashboard
	QueryTelesaleDashboardOverview(ctx context.Context, in *TelesaleDashboardRequest, opts ...grpc.CallOption) (*AnalyticsResponse, error)
}

type analyticServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAnalyticServiceClient(cc grpc.ClientConnInterface) AnalyticServiceClient {
	return &analyticServiceClient{cc}
}

func (c *analyticServiceClient) QueryReport(ctx context.Context, in *AnalyticsRequest, opts ...grpc.CallOption) (*AnalyticsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AnalyticsResponse)
	err := c.cc.Invoke(ctx, AnalyticService_QueryReport_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *analyticServiceClient) Query(ctx context.Context, in *AnalyticsRequest, opts ...grpc.CallOption) (*AnalyticsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AnalyticsResponse)
	err := c.cc.Invoke(ctx, AnalyticService_Query_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *analyticServiceClient) QueryOrderDashboardOverview(ctx context.Context, in *OrderDashboardRequest, opts ...grpc.CallOption) (*AnalyticsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AnalyticsResponse)
	err := c.cc.Invoke(ctx, AnalyticService_QueryOrderDashboardOverview_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *analyticServiceClient) QueryTelesaleDashboardOverview(ctx context.Context, in *TelesaleDashboardRequest, opts ...grpc.CallOption) (*AnalyticsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AnalyticsResponse)
	err := c.cc.Invoke(ctx, AnalyticService_QueryTelesaleDashboardOverview_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AnalyticServiceServer is the server API for AnalyticService service.
// All implementations must embed UnimplementedAnalyticServiceServer
// for forward compatibility.
type AnalyticServiceServer interface {
	QueryReport(context.Context, *AnalyticsRequest) (*AnalyticsResponse, error)
	Query(context.Context, *AnalyticsRequest) (*AnalyticsResponse, error)
	QueryOrderDashboardOverview(context.Context, *OrderDashboardRequest) (*AnalyticsResponse, error)
	// Telesale dashboard
	QueryTelesaleDashboardOverview(context.Context, *TelesaleDashboardRequest) (*AnalyticsResponse, error)
	mustEmbedUnimplementedAnalyticServiceServer()
}

// UnimplementedAnalyticServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedAnalyticServiceServer struct{}

func (UnimplementedAnalyticServiceServer) QueryReport(context.Context, *AnalyticsRequest) (*AnalyticsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryReport not implemented")
}
func (UnimplementedAnalyticServiceServer) Query(context.Context, *AnalyticsRequest) (*AnalyticsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Query not implemented")
}
func (UnimplementedAnalyticServiceServer) QueryOrderDashboardOverview(context.Context, *OrderDashboardRequest) (*AnalyticsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryOrderDashboardOverview not implemented")
}
func (UnimplementedAnalyticServiceServer) QueryTelesaleDashboardOverview(context.Context, *TelesaleDashboardRequest) (*AnalyticsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryTelesaleDashboardOverview not implemented")
}
func (UnimplementedAnalyticServiceServer) mustEmbedUnimplementedAnalyticServiceServer() {}
func (UnimplementedAnalyticServiceServer) testEmbeddedByValue()                         {}

// UnsafeAnalyticServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AnalyticServiceServer will
// result in compilation errors.
type UnsafeAnalyticServiceServer interface {
	mustEmbedUnimplementedAnalyticServiceServer()
}

func RegisterAnalyticServiceServer(s grpc.ServiceRegistrar, srv AnalyticServiceServer) {
	// If the following call pancis, it indicates UnimplementedAnalyticServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&AnalyticService_ServiceDesc, srv)
}

func _AnalyticService_QueryReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AnalyticsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnalyticServiceServer).QueryReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AnalyticService_QueryReport_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnalyticServiceServer).QueryReport(ctx, req.(*AnalyticsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnalyticService_Query_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AnalyticsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnalyticServiceServer).Query(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AnalyticService_Query_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnalyticServiceServer).Query(ctx, req.(*AnalyticsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnalyticService_QueryOrderDashboardOverview_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OrderDashboardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnalyticServiceServer).QueryOrderDashboardOverview(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AnalyticService_QueryOrderDashboardOverview_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnalyticServiceServer).QueryOrderDashboardOverview(ctx, req.(*OrderDashboardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnalyticService_QueryTelesaleDashboardOverview_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TelesaleDashboardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnalyticServiceServer).QueryTelesaleDashboardOverview(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AnalyticService_QueryTelesaleDashboardOverview_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnalyticServiceServer).QueryTelesaleDashboardOverview(ctx, req.(*TelesaleDashboardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// AnalyticService_ServiceDesc is the grpc.ServiceDesc for AnalyticService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AnalyticService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "exmsg.services.AnalyticService",
	HandlerType: (*AnalyticServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "QueryReport",
			Handler:    _AnalyticService_QueryReport_Handler,
		},
		{
			MethodName: "Query",
			Handler:    _AnalyticService_Query_Handler,
		},
		{
			MethodName: "QueryOrderDashboardOverview",
			Handler:    _AnalyticService_QueryOrderDashboardOverview_Handler,
		},
		{
			MethodName: "QueryTelesaleDashboardOverview",
			Handler:    _AnalyticService_QueryTelesaleDashboardOverview_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "services/analytic.proto",
}
