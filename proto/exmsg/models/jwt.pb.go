// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v3.5.1
// source: jwt.proto

package models

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// UserID      int64     `json:"u"`
// Permissions []int64   `json:"p"`
// DataSets    []DataSet `json:"d"`
// SubUserIds  []int64   `json:"s_ids"`
// Signature   string    `json:"s"`
type JWTDataSet struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProjectId int64 `protobuf:"varint,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	CountryId int64 `protobuf:"varint,2,opt,name=country_id,json=countryId,proto3" json:"country_id,omitempty"`
}

func (x *JWTDataSet) Reset() {
	*x = JWTDataSet{}
	mi := &file_jwt_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JWTDataSet) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JWTDataSet) ProtoMessage() {}

func (x *JWTDataSet) ProtoReflect() protoreflect.Message {
	mi := &file_jwt_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JWTDataSet.ProtoReflect.Descriptor instead.
func (*JWTDataSet) Descriptor() ([]byte, []int) {
	return file_jwt_proto_rawDescGZIP(), []int{0}
}

func (x *JWTDataSet) GetProjectId() int64 {
	if x != nil {
		return x.ProjectId
	}
	return 0
}

func (x *JWTDataSet) GetCountryId() int64 {
	if x != nil {
		return x.CountryId
	}
	return 0
}

type JWTClaim struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId      int64         `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Permissions []int64       `protobuf:"varint,2,rep,packed,name=permissions,proto3" json:"permissions,omitempty"`
	DataSets    []*JWTDataSet `protobuf:"bytes,3,rep,name=data_sets,json=dataSets,proto3" json:"data_sets,omitempty"`
	SubUserIds  []int64       `protobuf:"varint,4,rep,packed,name=sub_user_ids,json=subUserIds,proto3" json:"sub_user_ids,omitempty"`
	Signature   string        `protobuf:"bytes,5,opt,name=signature,proto3" json:"signature,omitempty"`
}

func (x *JWTClaim) Reset() {
	*x = JWTClaim{}
	mi := &file_jwt_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JWTClaim) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JWTClaim) ProtoMessage() {}

func (x *JWTClaim) ProtoReflect() protoreflect.Message {
	mi := &file_jwt_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JWTClaim.ProtoReflect.Descriptor instead.
func (*JWTClaim) Descriptor() ([]byte, []int) {
	return file_jwt_proto_rawDescGZIP(), []int{1}
}

func (x *JWTClaim) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *JWTClaim) GetPermissions() []int64 {
	if x != nil {
		return x.Permissions
	}
	return nil
}

func (x *JWTClaim) GetDataSets() []*JWTDataSet {
	if x != nil {
		return x.DataSets
	}
	return nil
}

func (x *JWTClaim) GetSubUserIds() []int64 {
	if x != nil {
		return x.SubUserIds
	}
	return nil
}

func (x *JWTClaim) GetSignature() string {
	if x != nil {
		return x.Signature
	}
	return ""
}

var File_jwt_proto protoreflect.FileDescriptor

var file_jwt_proto_rawDesc = []byte{
	0x0a, 0x09, 0x6a, 0x77, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c, 0x65, 0x78, 0x6d,
	0x73, 0x67, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x22, 0x4a, 0x0a, 0x0a, 0x4a, 0x57, 0x54,
	0x44, 0x61, 0x74, 0x61, 0x53, 0x65, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72,
	0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x72, 0x79, 0x49, 0x64, 0x22, 0xbc, 0x01, 0x0a, 0x08, 0x4a, 0x57, 0x54, 0x43, 0x6c, 0x61,
	0x69, 0x6d, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x70,
	0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03,
	0x52, 0x0b, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x35, 0x0a,
	0x09, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x73, 0x65, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x4a, 0x57, 0x54, 0x44, 0x61, 0x74, 0x61, 0x53, 0x65, 0x74, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61,
	0x53, 0x65, 0x74, 0x73, 0x12, 0x20, 0x0a, 0x0c, 0x73, 0x75, 0x62, 0x5f, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0a, 0x73, 0x75, 0x62, 0x55,
	0x73, 0x65, 0x72, 0x49, 0x64, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74,
	0x75, 0x72, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x42, 0x36, 0x5a, 0x34, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x61, 0x37, 0x39, 0x32, 0x33, 0x2f, 0x61, 0x74, 0x68, 0x65, 0x6e, 0x61, 0x2d,
	0x67, 0x6f, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x3b, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_jwt_proto_rawDescOnce sync.Once
	file_jwt_proto_rawDescData = file_jwt_proto_rawDesc
)

func file_jwt_proto_rawDescGZIP() []byte {
	file_jwt_proto_rawDescOnce.Do(func() {
		file_jwt_proto_rawDescData = protoimpl.X.CompressGZIP(file_jwt_proto_rawDescData)
	})
	return file_jwt_proto_rawDescData
}

var file_jwt_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_jwt_proto_goTypes = []any{
	(*JWTDataSet)(nil), // 0: exmsg.models.JWTDataSet
	(*JWTClaim)(nil),   // 1: exmsg.models.JWTClaim
}
var file_jwt_proto_depIdxs = []int32{
	0, // 0: exmsg.models.JWTClaim.data_sets:type_name -> exmsg.models.JWTDataSet
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_jwt_proto_init() }
func file_jwt_proto_init() {
	if File_jwt_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_jwt_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_jwt_proto_goTypes,
		DependencyIndexes: file_jwt_proto_depIdxs,
		MessageInfos:      file_jwt_proto_msgTypes,
	}.Build()
	File_jwt_proto = out.File
	file_jwt_proto_rawDesc = nil
	file_jwt_proto_goTypes = nil
	file_jwt_proto_depIdxs = nil
}
