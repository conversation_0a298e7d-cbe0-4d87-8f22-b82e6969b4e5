package tables

import "gitlab.com/a7923/athena-go/pkg/dbtool"

func GetUserTable() *dbtool.Table {
	return &dbtool.Table{
		Name:      "users",
		AIColumns: []string{"id"},
		ColumnMapper: map[string]string{
			"saleChannel": `"saleChannel"`,
		},
		IgnoreColumns: []string{},
		DefaultAlias:  "u",
	}
}

func GetProfileTable() *dbtool.Table {
	return &dbtool.Table{
		Name:          "user_profiles",
		AIColumns:     []string{"id"},
		ColumnMapper:  map[string]string{},
		IgnoreColumns: []string{},
		DefaultAlias:  "p",
	}
}

func GetRoleTable() *dbtool.Table {
	return &dbtool.Table{
		Name:          "roles",
		AIColumns:     []string{"id"},
		ColumnMapper:  map[string]string{},
		IgnoreColumns: []string{},
		DefaultAlias:  "r",
	}
}

func GetDataSetTable() *dbtool.Table {
	return &dbtool.Table{
		Name:          "data_set_scopes",
		AIColumns:     []string{"id"},
		ColumnMapper:  map[string]string{},
		IgnoreColumns: []string{},
		DefaultAlias:  "r",
	}
}

func GetProjectTable() *dbtool.Table {
	return &dbtool.Table{
		Name:      "projects",
		AIColumns: []string{"id"},
		ColumnMapper: map[string]string{
			"productIds": `"productIds"`,
		},
		IgnoreColumns: []string{},
		DefaultAlias:  "p",
	}
}
