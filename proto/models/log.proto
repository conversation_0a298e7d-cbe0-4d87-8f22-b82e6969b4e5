syntax = "proto3";

package exmsg.models;

import "google/protobuf/timestamp.proto";

option go_package = "gitlab.com/a7923/athena-go/proto/exmsg/models;models";
message Log {
  // Timestamp when the log entry was created.
  google.protobuf.Timestamp created_at = 1;

  // Timestamp when the log entry was last updated.
  google.protobuf.Timestamp updated_at = 2;

  // Unique identifier for the log entry.
  int32 id = 3;

  // Action performed that resulted in this log entry.
  string action = 4;

  // List of changes made in this log entry.
  repeated string changes = 5;

  // List of before changes (previous state) in this log entry.
  repeated string before_changes = 6;

  // The name of the table where the changes occurred.
  string table_name = 7;

  // The identifier of the record that was changed.
  string record_id = 8;

  // Name of the parent table (if applicable).
  string parent_table_name = 9;

  // ID of the parent record (if applicable).
  string parent_id = 10;

  // ID of the creator of the log entry.
  string creator_id = 11;
}