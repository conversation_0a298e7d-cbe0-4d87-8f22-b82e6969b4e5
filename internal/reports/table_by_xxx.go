package reports

import (
	"fmt"
	"github.com/Masterminds/squirrel"
	"github.com/spf13/cast"
	"gitlab.com/a7923/athena-go/internal/enums"
	"gitlab.com/a7923/athena-go/pkg/config"
	"gitlab.com/a7923/athena-go/pkg/utils"
	"gitlab.com/a7923/athena-go/proto/exmsg/models"
	"gitlab.com/a7923/athena-go/proto/exmsg/services"
	"strings"
)

type TelesaleInsightsResponse struct {
	Insights Insights `json:"insights"`
}

type Insights struct {
	Revenue     float64 `json:"revenue"`
	OrderCount  int     `json:"order_count"`
	ReturnCount int     `json:"return_count"`
	LeadCount   int     `json:"lead_count"`
	Spent       float64 `json:"spent"`
	MessCount   int     `json:"mess_count"`
}

type TableByReport struct{}

func (t *TableByReport) ProcessRow(row map[string]interface{}, req *services.AnalyticsRequest) (*models.Row, error) {
	if row == nil {
		return nil, nil
	}

	if req.IsSummary {
		newRow := Insights{
			Revenue:     cast.ToFloat64(row["revenue"]),
			OrderCount:  cast.ToInt(row["total_orders"]),
			ReturnCount: cast.ToInt(row["return_count"]),
			LeadCount:   cast.ToInt(row["lead_count"]),
			Spent:       cast.ToFloat64(row["spent"]),
			MessCount:   cast.ToInt(row["mess_count"]),
		}
		return &models.Row{Values: utils.ToJSONByte(&TelesaleInsightsResponse{Insights: newRow})}, nil
	}

	row["close_rate"] = utils.ToPercentFixed2(row["total_orders"], row["mess_count"])
	if req.TeamInCharge == TeamInChargeTelesale {
		row["close_rate"] = utils.ToPercentFixed2(row["total_orders"], row["lead_count"])
	}
	row["spent_per_lead"] = utils.ToFixed2(row["spent"], row["lead_count"])
	row["ads_per_rev"] = utils.ToPercentFixed2(row["spent"], row["revenue"])
	row["ads_per_sale"] = utils.ToPercentFixed2(row["spent"], row["sale"])
	row["sale_per_order"] = utils.ToFixed2(row["sale"], row["total_orders"])
	row["spent_per_mess"] = utils.ToFixed2(row["spent"], row["mess_count"])
	row["name"] = row[req.GroupBy[0]]
	row["count"] = row["total_orders"]

	// remove unnecessary columns (nested columns)
	for k := range row {
		if strings.Contains(k, ".") {
			delete(row, k)
		}
		if row[k] == nil {
			row[k] = ""
		}
	}

	return &models.Row{Values: utils.ToJSONByte(row)}, nil
	//return telesaleProcessRowFunc
}

func (t *TableByReport) buildWithCTE(req *services.AnalyticsRequest) string {
	predefinedColumns := []string{
		fmt.Sprintf(rateCTE, req.CompanyId, req.CountryId),
		"1 AS group_by_all",
	}

	//predefinedColumns = append(predefinedColumns, nameColumns...)
	//predefinedColumns = append(predefinedColumns, spendingColumns...)
	predefinedColumns = append(predefinedColumns, revColumns...)
	predefinedColumns = append(predefinedColumns, primaryColumns...)

	return fmt.Sprintf("WITH %s", strings.Join(predefinedColumns, ",\n"))
}

func (t *TableByReport) GetQuery(req *services.AnalyticsRequest) (squirrel.SelectBuilder, error) {
	selectColumns := make([]string, 0)
	groupBy := req.GroupBy[0]
	groupKey := groupByColumn[groupBy]

	if req.TeamInCharge < 1 {
		req.TeamInCharge = 1
	}

	selectColumns = append(selectColumns, groupColumns[groupBy]...)
	selectColumns = append(selectColumns, rev_columns...)

	qb := squirrel.
		Select(selectColumns...).
		From("base_orders o")

	if groupBy == DimensionProduct {
		qb = qb.JoinClause(`ARRAY JOIN product_id`)
	}

	qb = qb.
		Where(squirrel.Eq{"company_id": req.CompanyId}).
		Where(squirrel.GtOrEq{"ts": req.StartTime}).
		Where(squirrel.Lt{"ts": req.EndTime}).
		Where(squirrel.Eq{"team_in_charge": req.TeamInCharge}).
		Where(squirrel.Eq{"country_id": req.CountryId})

	qb = buildDimensionWhere(req, qb, groupBy != DimensionProduct)

	qb = qb.
		GroupBy(groupColumns[groupBy][0]).
		OrderBy(req.OrderBy...).
		Limit(req.Limit).
		Offset(req.Offset)

	qb = qb.Prefix(t.buildWithCTE(req))

	// spending
	spendingQB := t.getSpendingQuery(req)
	spendingQuery, args, _ := spendingQB.ToSql()

	finalSelectColumns := []string{
		fmt.Sprintf("rev.%s AS %s", groupKey, groupKey),
		"*",
	}
	finalQb := squirrel.Select().
		FromSelect(qb, "rev").
		LeftJoin(fmt.Sprintf("(%s) sp ON sp.%s = rev.%s", spendingQuery, groupKey, groupKey), args...)

	// group by xxx_id
	if groupBy != DimensionDate {
		finalSelectColumns = []string{
			fmt.Sprintf("if (rev.%s > 0, rev.%s, sp.%s) AS %s", groupKey, groupKey, groupKey, groupKey),
			"*",
		}

		finalQb = squirrel.Select().
			FromSelect(qb, "rev").
			JoinClause(fmt.Sprintf("FULL JOIN (%s) sp ON sp.%s = rev.%s", spendingQuery, groupKey, groupKey), args...)
	}

	switch req.TeamInCharge {
	case TeamInChargeCarePage:
		qb = qb.Where(squirrel.NotEq{"order_status": []int{
			int(enums.Draft),
			int(enums.InReturn),
			int(enums.ReturnedStocked),
			int(enums.ReturnedCompleted),
			int(enums.Canceled),
		}})

		messQb := t.getMessageQuery(req)
		messQuery, margs, _ := messQb.ToSql()

		finalQb = finalQb.
			LeftJoin(fmt.Sprintf("(%v) mess ON mess.%s = rev.%s", messQuery, groupKey, groupKey), margs...)
	}

	finalQb = finalQb.Suffix("SETTINGS join_use_nulls = 0")
	env := config.ViperGetStringWithDefault("env", "prod")
	switch groupBy {
	case DimensionMarketer:
		finalQb = finalQb.LeftJoin(fmt.Sprintf("(SELECT id, name FROM identity_%v.users) u ON u.id = marketer_id", env))
		finalQb = finalQb.Where(squirrel.Gt{"marketer_id": 0})
		finalSelectColumns = append(finalSelectColumns, "u.name AS marketer")
	case DimensionProduct:
		finalQb = finalQb.LeftJoin(fmt.Sprintf("(SELECT id, name FROM catalog_%v.product) cp ON cp.id = product_id", env))
		finalQb = finalQb.Where(squirrel.Gt{"product_id": 0})
		finalSelectColumns = append(finalSelectColumns, "cp.name AS product")
	case DimensionProject:
		finalQb = finalQb.LeftJoin(fmt.Sprintf("(SELECT id, name FROM identity_%v.projects) p ON p.id = project_id", env))
		finalQb = finalQb.Where(squirrel.Gt{"project_id": 0})
		finalSelectColumns = append(finalSelectColumns, "p.name AS project")
	case DimensionCampaign, DimensionAdset, DimensionAdaccount:
		finalQb = finalQb.Where(squirrel.Gt{groupKey: 0})
	}

	finalQb = finalQb.Columns(finalSelectColumns...)

	return finalQb, nil
}

func (t *TableByReport) getSpendingQuery(req *services.AnalyticsRequest) squirrel.SelectBuilder {
	groupBy := req.GroupBy[0]
	groupByKey := groupByColumn[groupBy]

	selectColumns := []string{
		fmt.Sprintf("%s AS %s", messGroupColumns[groupBy], groupByKey),
	}
	qb := squirrel.Select(
		append(selectColumns, spendingColumns...)...,
	).From("base_ads a").
		Where(squirrel.Eq{"company_id": req.CompanyId}).
		Where(squirrel.GtOrEq{"ts": req.StartTime}).
		Where(squirrel.Lt{"ts": req.EndTime}).
		Where(squirrel.Eq{"country_id": req.CountryId}).
		Where(squirrel.Eq{"team_in_charge": req.TeamInCharge})

	qb = buildDimensionWhere(req, qb, false)
	qb = qb.GroupBy(groupByKey)

	return qb
}

func buildDimensionWhere(req *services.AnalyticsRequest, qb squirrel.SelectBuilder, productIdAsArray bool) squirrel.SelectBuilder {
	if len(req.ProjectIds) > 0 {
		qb = qb.Where(squirrel.Eq{"project_id": req.ProjectIds})
	}

	if len(req.ProductIds) > 0 {
		if productIdAsArray {
			qb = qb.Where(squirrel.Expr(`hasAny(product_id, ?)`, req.ProductIds))
		} else {
			qb = qb.Where(squirrel.Eq{"product_id": req.ProductIds})
		}
	}
	if len(req.MarketerIds) > 0 {
		qb = qb.Where(squirrel.Eq{"marketer_id": req.MarketerIds})
	}
	if len(req.AdaccountIds) > 0 {
		qb = qb.Where(squirrel.Eq{"adaccount_id": req.AdaccountIds})
	}
	if len(req.CampaignIds) > 0 {
		qb = qb.Where(squirrel.Eq{"campaign_id": req.CampaignIds})
	}
	if len(req.AdsetIds) > 0 {
		qb = qb.Where(squirrel.Eq{"adset_id": req.AdsetIds})
	}
	return qb
}

func (t *TableByReport) getMessageQuery(req *services.AnalyticsRequest) squirrel.SelectBuilder {
	groupBy := req.GroupBy[0]
	groupByKey := groupByColumn[groupBy]
	qb := squirrel.Select(
		fmt.Sprintf("%s AS %s", messGroupColumns[groupBy], groupByKey), // date, product_id, marketer_id, project_id, adaccount_id, campaign_id, adset_id, group_by_all
		"uniqExactMerge(unique_users) AS mess_count",
		"uniqExactMerge(handled_users) AS handled_message",
	).From("base_messages m").
		Where(squirrel.Eq{"company_id": req.CompanyId}).
		Where(squirrel.GtOrEq{"ts": req.StartTime}).
		Where(squirrel.Lt{"ts": req.EndTime}).
		Where(squirrel.Eq{"country_id": req.CountryId})

	qb = buildDimensionWhere(req, qb, false)

	qb = qb.GroupBy(groupByKey)

	return qb
}
