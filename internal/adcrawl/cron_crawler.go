package adcrawl

import (
	"context"
	"github.com/goccy/go-json"
	"github.com/spf13/cast"
	"github.com/spf13/viper"
	"gitlab.com/a7923/athena-go/pkg/logger"
	"gitlab.com/a7923/athena-go/pkg/utils"
	"strings"
	"sync"
	"time"

	"github.com/hibiken/asynq"
	"golang.org/x/exp/rand"
)

const (
	InsertQueueName         = "insert"
	FetchQueueName          = "fetch"
	CriticalInsertQueueName = "critical_insert"
	CriticalFetchQueueName  = "critical_fetch"
)

type Config struct {
	NumberOfWorkers int
	RetryBaseWait   map[string]time.Duration
	Priority        map[string]int
}

type CronCrawler struct {
	srv           *asynq.Server
	insertHandler *InsertTaskHandler
	fetchHandler  *FetchTaskHandler
	seedHandler   *SeedTaskHandler
	updateHandler *UpdateTaskHandler
}

func NewCronCrawler(redisAddr string, ih *Insert<PERSON><PERSON>Hand<PERSON>, fh *Fetch<PERSON>askHand<PERSON>, sh *Seed<PERSON><PERSON><PERSON>and<PERSON>, uh *UpdateTaskHandler, cfg Config) *CronCrawler {
	c := &CronCrawler{
		srv: asynq.NewServer(asynq.RedisClientOpt{Addr: redisAddr}, asynq.Config{
			Concurrency: cfg.NumberOfWorkers,
			RetryDelayFunc: func(n int, e error, t *asynq.Task) time.Duration {
				taskType := t.Type()
				// since InsertTask has nothing to do with API ratelimit
				// set it retry time for every base wait time set
				if strings.HasPrefix(taskType, "insert") {
					return cfg.RetryBaseWait["insert"]
				} else if strings.HasPrefix(taskType, "fetch") {
					// FetchTask has to deal with API ratelimit
					// exponential backoff with jitter
					upperWait := 2 * time.Duration(n+1) * cfg.RetryBaseWait["fetch"]
					lowerWait := upperWait / 2
					return lowerWait + time.Duration(rand.Int63n(int64(upperWait-lowerWait)))
				} else {
					return cfg.RetryBaseWait["seed"]
				}
			},
			Queues: map[string]int{
				"insert":          cfg.Priority["insert"],
				"fetch":           cfg.Priority["fetch"],
				"critical_fetch":  cfg.Priority["critical_fetch"],
				"critical_insert": cfg.Priority["critical_insert"],
				"seed":            cfg.Priority["seed"],
				"update":          cfg.Priority["update"],
			},
		}),
		insertHandler: ih,
		fetchHandler:  fh,
		seedHandler:   sh,
		updateHandler: uh,
	}
	return c
}

func (c *CronCrawler) Start() {
	mux := asynq.NewServeMux()

	if viper.GetBool("crawl.debug") {
		mux.Use(loggingMiddleware)
	}

	{
		mux.HandleFunc(string(TypeFetchAdAccountIDs), c.fetchHandler.HandleFetchAdAccountIdsTask)
		mux.HandleFunc(string(TypeFetchAdAccounts), c.fetchHandler.HandleFetchAdAccountsTask)
		mux.HandleFunc(string(TypeFetchCampaigns), c.fetchHandler.HandleFetchCampaignsTask)
		mux.HandleFunc(string(TypeFetchAdSets), c.fetchHandler.HandleFetchAdSetsTask)
		mux.HandleFunc(string(TypeFetchAds), c.fetchHandler.HandleFetchAdsTask)
		mux.HandleFunc(string(TypeFetchSpendings), c.fetchHandler.HandleFetchSpendingsTask)
	}

	{
		mux.HandleFunc(string(TypeInsertAdAccounts), c.insertHandler.HandleInsertAdAccountsTask)
		mux.HandleFunc(string(TypeInsertCampaigns), c.insertHandler.HandleInsertCampaignsTask)
		mux.HandleFunc(string(TypeInsertAdSets), c.insertHandler.HandleInsertAdSetsTask)
		mux.HandleFunc(string(TypeInsertAds), c.insertHandler.HandleInsertAdsTask)
		mux.HandleFunc(string(TypeInsertSpendings), c.insertHandler.HandleInsertSpendingsTask)
	}

	{
		mux.HandleFunc(string(TypeSeedFetchSpendings), c.seedHandler.HandleSeedFetchSpendingsTask)
	}

	{
		mux.HandleFunc(string(TypeUpdateMarketerID), c.updateHandler.HandleUpdateMarketerIDTask)
		mux.HandleFunc(string(TypeUpdateCountryID), c.updateHandler.HandleUpdateCountryIDTask)
	}

	var wg sync.WaitGroup

	wg.Add(1)
	go func() {
		defer wg.Done()
		if err := c.srv.Run(mux); err != nil {
			panic(err)
		}
	}()
	wg.Wait()
}

func loggingMiddleware(h asynq.Handler) asynq.Handler {
	return asynq.HandlerFunc(func(ctx context.Context, t *asynq.Task) error {
		start := time.Now()
		payload := map[string]interface{}{}
		_ = json.Unmarshal(t.Payload(), &payload)
		// trim value too long
		for k, v := range payload {
			if len(cast.ToString(v)) > 100 {
				payload[k] = utils.SubNString(v.(string), 100)
			}
		}

		err := h.ProcessTask(ctx, t)
		if err != nil {
			logger.AthenaLogger.Errorw("Failed to process task",
				"error", err.Error(),
				"url", t.Type(),
				"payload", utils.ToJSONString(payload),
				"took", time.Since(start).Milliseconds())
			return err
		}

		if strings.HasPrefix(t.Type(), "insert:") {
			return nil
		}

		logger.AthenaLogger.Debugw(
			"Processed crawl request",
			"url", t.Type(),
			"payload", utils.ToJSONString(payload),
			"took", time.Since(start).Milliseconds())
		return nil
	})
}
