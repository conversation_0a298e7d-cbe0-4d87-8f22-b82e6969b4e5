package selector

import (
	"math/rand"
	"sync"
	"time"

	"go-micro.dev/v5/registry"
)

func init() {
	rand.Seed(time.Now().UnixNano())
}

// Random is a random strategy algorithm for node selection.
func Random(services []*registry.Service) Next {
	nodes := make([]*registry.Node, 0, len(services))
	for _, service := range services {
		nodes = append(nodes, service.Nodes...)
	}

	return func() (*registry.Node, *registry.Service, error) {
		if len(nodes) == 0 {
			return nil, nil, ErrNoneAvailable
		}

		i := rand.Int() % len(nodes)
		return nodes[i], services[0], nil
	}
}

// RoundRobin is a roundrobin strategy algorithm for node selection.
func RoundRobin(services []*registry.Service) Next {
	nodes := make([]*registry.Node, 0, len(services))

	for _, service := range services {
		nodes = append(nodes, service.Nodes...)
	}

	var i = rand.Int()
	var mtx sync.Mutex

	return func() (*registry.Node, *registry.Service, error) {
		if len(nodes) == 0 {
			return nil, nil, ErrNoneAvailable
		}

		mtx.Lock()
		node := nodes[i%len(nodes)]
		i++
		mtx.Unlock()

		return node, services[0], nil
	}
}
