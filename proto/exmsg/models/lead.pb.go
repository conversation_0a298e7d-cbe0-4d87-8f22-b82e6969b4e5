// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v3.5.1
// source: lead.proto

package models

import (
	timestamp "github.com/golang/protobuf/ptypes/timestamp"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Lead represents a lead in the system
type Lead struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Basic fields
	Id        int64                `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	CreatedAt *timestamp.Timestamp `protobuf:"bytes,2,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt *timestamp.Timestamp `protobuf:"bytes,3,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// Required fields
	OrderId int64 `protobuf:"varint,4,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	State   int64 `protobuf:"varint,5,opt,name=state,proto3" json:"state,omitempty"`
	// fields
	UserId                 int64                `protobuf:"varint,6,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	CurrentCareId          int64                `protobuf:"varint,7,opt,name=current_care_id,json=currentCareId,proto3" json:"current_care_id,omitempty"`
	LastUpdatedState       *timestamp.Timestamp `protobuf:"bytes,8,opt,name=last_updated_state,json=lastUpdatedState,proto3" json:"last_updated_state,omitempty"`
	UpdatedBy              int64                `protobuf:"varint,9,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`
	IgnoreDuplicateWarning bool                 `protobuf:"varint,10,opt,name=ignore_duplicate_warning,json=ignoreDuplicateWarning,proto3" json:"ignore_duplicate_warning,omitempty"`
	FormCapturedAt         *timestamp.Timestamp `protobuf:"bytes,11,opt,name=form_captured_at,json=formCapturedAt,proto3" json:"form_captured_at,omitempty"`
	// UTM tracking fields
	UtmSource   string `protobuf:"bytes,12,opt,name=utm_source,json=utmSource,proto3" json:"utm_source,omitempty"`       // Example: facebook
	UtmMedium   string `protobuf:"bytes,13,opt,name=utm_medium,json=utmMedium,proto3" json:"utm_medium,omitempty"`       // ad.id
	UtmCampaign string `protobuf:"bytes,14,opt,name=utm_campaign,json=utmCampaign,proto3" json:"utm_campaign,omitempty"` // campaign.id
	UtmTerm     string `protobuf:"bytes,15,opt,name=utm_term,json=utmTerm,proto3" json:"utm_term,omitempty"`             // adset.id
	Link        string `protobuf:"bytes,16,opt,name=link,proto3" json:"link,omitempty"`
	// Care related fields
	LastCareId       int64 `protobuf:"varint,17,opt,name=last_care_id,json=lastCareId,proto3" json:"last_care_id,omitempty"`
	LastCareItemId   int64 `protobuf:"varint,18,opt,name=last_care_item_id,json=lastCareItemId,proto3" json:"last_care_item_id,omitempty"`
	LastCareReasonId int64 `protobuf:"varint,19,opt,name=last_care_reason_id,json=lastCareReasonId,proto3" json:"last_care_reason_id,omitempty"`
	LeadType         int64 `protobuf:"varint,20,opt,name=lead_type,json=leadType,proto3" json:"lead_type,omitempty"`
	CountryId        int64 `protobuf:"varint,21,opt,name=country_id,json=countryId,proto3" json:"country_id,omitempty"`
}

func (x *Lead) Reset() {
	*x = Lead{}
	mi := &file_lead_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Lead) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Lead) ProtoMessage() {}

func (x *Lead) ProtoReflect() protoreflect.Message {
	mi := &file_lead_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Lead.ProtoReflect.Descriptor instead.
func (*Lead) Descriptor() ([]byte, []int) {
	return file_lead_proto_rawDescGZIP(), []int{0}
}

func (x *Lead) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Lead) GetCreatedAt() *timestamp.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Lead) GetUpdatedAt() *timestamp.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *Lead) GetOrderId() int64 {
	if x != nil {
		return x.OrderId
	}
	return 0
}

func (x *Lead) GetState() int64 {
	if x != nil {
		return x.State
	}
	return 0
}

func (x *Lead) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *Lead) GetCurrentCareId() int64 {
	if x != nil {
		return x.CurrentCareId
	}
	return 0
}

func (x *Lead) GetLastUpdatedState() *timestamp.Timestamp {
	if x != nil {
		return x.LastUpdatedState
	}
	return nil
}

func (x *Lead) GetUpdatedBy() int64 {
	if x != nil {
		return x.UpdatedBy
	}
	return 0
}

func (x *Lead) GetIgnoreDuplicateWarning() bool {
	if x != nil {
		return x.IgnoreDuplicateWarning
	}
	return false
}

func (x *Lead) GetFormCapturedAt() *timestamp.Timestamp {
	if x != nil {
		return x.FormCapturedAt
	}
	return nil
}

func (x *Lead) GetUtmSource() string {
	if x != nil {
		return x.UtmSource
	}
	return ""
}

func (x *Lead) GetUtmMedium() string {
	if x != nil {
		return x.UtmMedium
	}
	return ""
}

func (x *Lead) GetUtmCampaign() string {
	if x != nil {
		return x.UtmCampaign
	}
	return ""
}

func (x *Lead) GetUtmTerm() string {
	if x != nil {
		return x.UtmTerm
	}
	return ""
}

func (x *Lead) GetLink() string {
	if x != nil {
		return x.Link
	}
	return ""
}

func (x *Lead) GetLastCareId() int64 {
	if x != nil {
		return x.LastCareId
	}
	return 0
}

func (x *Lead) GetLastCareItemId() int64 {
	if x != nil {
		return x.LastCareItemId
	}
	return 0
}

func (x *Lead) GetLastCareReasonId() int64 {
	if x != nil {
		return x.LastCareReasonId
	}
	return 0
}

func (x *Lead) GetLeadType() int64 {
	if x != nil {
		return x.LeadType
	}
	return 0
}

func (x *Lead) GetCountryId() int64 {
	if x != nil {
		return x.CountryId
	}
	return 0
}

type LeadCare struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Timestamp when the lead care entry was created.
	CreatedAt *timestamp.Timestamp `protobuf:"bytes,1,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// Timestamp when the lead care entry was last updated.
	UpdatedAt *timestamp.Timestamp `protobuf:"bytes,2,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// Unique identifier for the lead care entry.
	Id int64 `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty"`
	// ID of the user associated with this lead care entry.
	UserId int64 `protobuf:"varint,4,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// ID of the lead associated with this lead care entry.
	LeadId int64 `protobuf:"varint,5,opt,name=lead_id,json=leadId,proto3" json:"lead_id,omitempty"`
	// ID of the shift associated with this lead care entry (nullable).
	ShiftId int64 `protobuf:"varint,6,opt,name=shift_id,json=shiftId,proto3" json:"shift_id,omitempty"`
	// State of the lead care entry (e.g., 3 by default).
	State int64 `protobuf:"varint,7,opt,name=state,proto3" json:"state,omitempty"`
	// ID of the user who updated this entry.
	UpdatedBy int64 `protobuf:"varint,8,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`
}

func (x *LeadCare) Reset() {
	*x = LeadCare{}
	mi := &file_lead_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LeadCare) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LeadCare) ProtoMessage() {}

func (x *LeadCare) ProtoReflect() protoreflect.Message {
	mi := &file_lead_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LeadCare.ProtoReflect.Descriptor instead.
func (*LeadCare) Descriptor() ([]byte, []int) {
	return file_lead_proto_rawDescGZIP(), []int{1}
}

func (x *LeadCare) GetCreatedAt() *timestamp.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *LeadCare) GetUpdatedAt() *timestamp.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *LeadCare) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *LeadCare) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *LeadCare) GetLeadId() int64 {
	if x != nil {
		return x.LeadId
	}
	return 0
}

func (x *LeadCare) GetShiftId() int64 {
	if x != nil {
		return x.ShiftId
	}
	return 0
}

func (x *LeadCare) GetState() int64 {
	if x != nil {
		return x.State
	}
	return 0
}

func (x *LeadCare) GetUpdatedBy() int64 {
	if x != nil {
		return x.UpdatedBy
	}
	return 0
}

type LeadCareItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Timestamp when the lead care item was created.
	CreatedAt *timestamp.Timestamp `protobuf:"bytes,1,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// Timestamp when the lead care item was last updated.
	UpdatedAt *timestamp.Timestamp `protobuf:"bytes,2,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// Unique identifier for the lead care item.
	Id int64 `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty"`
	// ID of the associated lead care entry.
	LeadCareId int64 `protobuf:"varint,4,opt,name=lead_care_id,json=leadCareId,proto3" json:"lead_care_id,omitempty"`
	// ID of the associated care reason (nullable).
	ReasonId int64 `protobuf:"varint,5,opt,name=reason_id,json=reasonId,proto3" json:"reason_id,omitempty"`
	// Number of times the reason is repeated (nullable).
	TimesRepeatReason int64 `protobuf:"varint,6,opt,name=times_repeat_reason,json=timesRepeatReason,proto3" json:"times_repeat_reason,omitempty"`
	// Optional note related to the lead care item.
	Note string `protobuf:"bytes,7,opt,name=note,proto3" json:"note,omitempty"`
	// ID of the creator of this lead care item.
	CreatorId int64 `protobuf:"varint,8,opt,name=creator_id,json=creatorId,proto3" json:"creator_id,omitempty"`
	// ID of the associated lead.
	LeadId int64 `protobuf:"varint,9,opt,name=lead_id,json=leadId,proto3" json:"lead_id,omitempty"`
}

func (x *LeadCareItem) Reset() {
	*x = LeadCareItem{}
	mi := &file_lead_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LeadCareItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LeadCareItem) ProtoMessage() {}

func (x *LeadCareItem) ProtoReflect() protoreflect.Message {
	mi := &file_lead_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LeadCareItem.ProtoReflect.Descriptor instead.
func (*LeadCareItem) Descriptor() ([]byte, []int) {
	return file_lead_proto_rawDescGZIP(), []int{2}
}

func (x *LeadCareItem) GetCreatedAt() *timestamp.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *LeadCareItem) GetUpdatedAt() *timestamp.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *LeadCareItem) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *LeadCareItem) GetLeadCareId() int64 {
	if x != nil {
		return x.LeadCareId
	}
	return 0
}

func (x *LeadCareItem) GetReasonId() int64 {
	if x != nil {
		return x.ReasonId
	}
	return 0
}

func (x *LeadCareItem) GetTimesRepeatReason() int64 {
	if x != nil {
		return x.TimesRepeatReason
	}
	return 0
}

func (x *LeadCareItem) GetNote() string {
	if x != nil {
		return x.Note
	}
	return ""
}

func (x *LeadCareItem) GetCreatorId() int64 {
	if x != nil {
		return x.CreatorId
	}
	return 0
}

func (x *LeadCareItem) GetLeadId() int64 {
	if x != nil {
		return x.LeadId
	}
	return 0
}

type CareReason struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Timestamp when the care reason was created.
	CreatedAt *timestamp.Timestamp `protobuf:"bytes,1,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// Timestamp when the care reason was last updated.
	UpdatedAt *timestamp.Timestamp `protobuf:"bytes,2,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// Unique identifier for the care reason.
	Id int64 `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty"`
	// Name of the care reason.
	Name string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	// ID of the associated country (nullable).
	CountryId int64 `protobuf:"varint,5,opt,name=country_id,json=countryId,proto3" json:"country_id,omitempty"`
	// ID of the associated company (nullable).
	CompanyId int64 `protobuf:"varint,6,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// ID of the creator of this care reason.
	CreatorId int64 `protobuf:"varint,7,opt,name=creator_id,json=creatorId,proto3" json:"creator_id,omitempty"`
	// ID of the user who last updated this care reason.
	UpdatedBy int64 `protobuf:"varint,8,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`
	// Timestamp when the care reason was deleted (nullable).
	DeletedAt *timestamp.Timestamp `protobuf:"bytes,9,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
	// ID of the user who deleted this care reason (nullable).
	DeletedBy int64 `protobuf:"varint,10,opt,name=deleted_by,json=deletedBy,proto3" json:"deleted_by,omitempty"`
	// A unique key for the reason (nullable).
	ReasonKey string `protobuf:"bytes,11,opt,name=reason_key,json=reasonKey,proto3" json:"reason_key,omitempty"`
	// State of the care reason (nullable).
	State int64 `protobuf:"varint,12,opt,name=state,proto3" json:"state,omitempty"`
	// Flag indicating if the report should ignore this care reason.
	ReportIgnore bool `protobuf:"varint,13,opt,name=report_ignore,json=reportIgnore,proto3" json:"report_ignore,omitempty"`
	// Sort order number for this care reason.
	SortNo int64 `protobuf:"varint,14,opt,name=sort_no,json=sortNo,proto3" json:"sort_no,omitempty"`
}

func (x *CareReason) Reset() {
	*x = CareReason{}
	mi := &file_lead_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CareReason) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CareReason) ProtoMessage() {}

func (x *CareReason) ProtoReflect() protoreflect.Message {
	mi := &file_lead_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CareReason.ProtoReflect.Descriptor instead.
func (*CareReason) Descriptor() ([]byte, []int) {
	return file_lead_proto_rawDescGZIP(), []int{3}
}

func (x *CareReason) GetCreatedAt() *timestamp.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *CareReason) GetUpdatedAt() *timestamp.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *CareReason) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CareReason) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CareReason) GetCountryId() int64 {
	if x != nil {
		return x.CountryId
	}
	return 0
}

func (x *CareReason) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *CareReason) GetCreatorId() int64 {
	if x != nil {
		return x.CreatorId
	}
	return 0
}

func (x *CareReason) GetUpdatedBy() int64 {
	if x != nil {
		return x.UpdatedBy
	}
	return 0
}

func (x *CareReason) GetDeletedAt() *timestamp.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

func (x *CareReason) GetDeletedBy() int64 {
	if x != nil {
		return x.DeletedBy
	}
	return 0
}

func (x *CareReason) GetReasonKey() string {
	if x != nil {
		return x.ReasonKey
	}
	return ""
}

func (x *CareReason) GetState() int64 {
	if x != nil {
		return x.State
	}
	return 0
}

func (x *CareReason) GetReportIgnore() bool {
	if x != nil {
		return x.ReportIgnore
	}
	return false
}

func (x *CareReason) GetSortNo() int64 {
	if x != nil {
		return x.SortNo
	}
	return 0
}

var File_lead_proto protoreflect.FileDescriptor

var file_lead_proto_rawDesc = []byte{
	0x0a, 0x0a, 0x6c, 0x65, 0x61, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c, 0x65, 0x78,
	0x6d, 0x73, 0x67, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xaf, 0x06, 0x0a, 0x04,
	0x4c, 0x65, 0x61, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12,
	0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f,
	0x63, 0x61, 0x72, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x63,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x72, 0x65, 0x49, 0x64, 0x12, 0x48, 0x0a, 0x12,
	0x6c, 0x61, 0x73, 0x74, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x10, 0x6c, 0x61, 0x73, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x62, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x38, 0x0a, 0x18, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x5f,
	0x64, 0x75, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x65, 0x5f, 0x77, 0x61, 0x72, 0x6e, 0x69, 0x6e,
	0x67, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x16, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x44,
	0x75, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x65, 0x57, 0x61, 0x72, 0x6e, 0x69, 0x6e, 0x67, 0x12,
	0x44, 0x0a, 0x10, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x63, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0e, 0x66, 0x6f, 0x72, 0x6d, 0x43, 0x61, 0x70, 0x74, 0x75,
	0x72, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x74, 0x6d, 0x5f, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x74, 0x6d, 0x53, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x74, 0x6d, 0x5f, 0x6d, 0x65, 0x64, 0x69,
	0x75, 0x6d, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x74, 0x6d, 0x4d, 0x65, 0x64,
	0x69, 0x75, 0x6d, 0x12, 0x21, 0x0a, 0x0c, 0x75, 0x74, 0x6d, 0x5f, 0x63, 0x61, 0x6d, 0x70, 0x61,
	0x69, 0x67, 0x6e, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x75, 0x74, 0x6d, 0x43, 0x61,
	0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x12, 0x19, 0x0a, 0x08, 0x75, 0x74, 0x6d, 0x5f, 0x74, 0x65,
	0x72, 0x6d, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x75, 0x74, 0x6d, 0x54, 0x65, 0x72,
	0x6d, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x20, 0x0a, 0x0c, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x63, 0x61,
	0x72, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x11, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x6c, 0x61, 0x73,
	0x74, 0x43, 0x61, 0x72, 0x65, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x11, 0x6c, 0x61, 0x73, 0x74, 0x5f,
	0x63, 0x61, 0x72, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x12, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0e, 0x6c, 0x61, 0x73, 0x74, 0x43, 0x61, 0x72, 0x65, 0x49, 0x74, 0x65, 0x6d,
	0x49, 0x64, 0x12, 0x2d, 0x0a, 0x13, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x63, 0x61, 0x72, 0x65, 0x5f,
	0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x13, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x10, 0x6c, 0x61, 0x73, 0x74, 0x43, 0x61, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x49,
	0x64, 0x12, 0x1b, 0x0a, 0x09, 0x6c, 0x65, 0x61, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x14,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x6c, 0x65, 0x61, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d,
	0x0a, 0x0a, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x15, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x49, 0x64, 0x22, 0x92, 0x02,
	0x0a, 0x08, 0x4c, 0x65, 0x61, 0x64, 0x43, 0x61, 0x72, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x6c, 0x65, 0x61,
	0x64, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x6c, 0x65, 0x61, 0x64,
	0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x68, 0x69, 0x66, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x73, 0x68, 0x69, 0x66, 0x74, 0x49, 0x64, 0x12, 0x14, 0x0a,
	0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62,
	0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x42, 0x79, 0x22, 0xcf, 0x02, 0x0a, 0x0c, 0x4c, 0x65, 0x61, 0x64, 0x43, 0x61, 0x72, 0x65, 0x49,
	0x74, 0x65, 0x6d, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39,
	0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x20, 0x0a, 0x0c, 0x6c, 0x65, 0x61,
	0x64, 0x5f, 0x63, 0x61, 0x72, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0a, 0x6c, 0x65, 0x61, 0x64, 0x43, 0x61, 0x72, 0x65, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x72,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08,
	0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x13, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x5f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x11, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x52, 0x65, 0x70, 0x65,
	0x61, 0x74, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x6f, 0x74, 0x65,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x6f, 0x74, 0x65, 0x12, 0x1d, 0x0a, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x6c,
	0x65, 0x61, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x6c, 0x65,
	0x61, 0x64, 0x49, 0x64, 0x22, 0xef, 0x03, 0x0a, 0x0a, 0x43, 0x61, 0x72, 0x65, 0x52, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39,
	0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a,
	0x0a, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x09, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a,
	0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x39, 0x0a, 0x0a, 0x64, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f,
	0x62, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x5f, 0x6b, 0x65,
	0x79, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x4b,
	0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x5f, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0c, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x12, 0x17, 0x0a,
	0x07, 0x73, 0x6f, 0x72, 0x74, 0x5f, 0x6e, 0x6f, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06,
	0x73, 0x6f, 0x72, 0x74, 0x4e, 0x6f, 0x42, 0x36, 0x5a, 0x34, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x61, 0x37, 0x39, 0x32, 0x33, 0x2f, 0x61, 0x74, 0x68, 0x65, 0x6e,
	0x61, 0x2d, 0x67, 0x6f, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x65, 0x78, 0x6d, 0x73, 0x67,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x3b, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_lead_proto_rawDescOnce sync.Once
	file_lead_proto_rawDescData = file_lead_proto_rawDesc
)

func file_lead_proto_rawDescGZIP() []byte {
	file_lead_proto_rawDescOnce.Do(func() {
		file_lead_proto_rawDescData = protoimpl.X.CompressGZIP(file_lead_proto_rawDescData)
	})
	return file_lead_proto_rawDescData
}

var file_lead_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_lead_proto_goTypes = []any{
	(*Lead)(nil),                // 0: exmsg.models.Lead
	(*LeadCare)(nil),            // 1: exmsg.models.LeadCare
	(*LeadCareItem)(nil),        // 2: exmsg.models.LeadCareItem
	(*CareReason)(nil),          // 3: exmsg.models.CareReason
	(*timestamp.Timestamp)(nil), // 4: google.protobuf.Timestamp
}
var file_lead_proto_depIdxs = []int32{
	4,  // 0: exmsg.models.Lead.created_at:type_name -> google.protobuf.Timestamp
	4,  // 1: exmsg.models.Lead.updated_at:type_name -> google.protobuf.Timestamp
	4,  // 2: exmsg.models.Lead.last_updated_state:type_name -> google.protobuf.Timestamp
	4,  // 3: exmsg.models.Lead.form_captured_at:type_name -> google.protobuf.Timestamp
	4,  // 4: exmsg.models.LeadCare.created_at:type_name -> google.protobuf.Timestamp
	4,  // 5: exmsg.models.LeadCare.updated_at:type_name -> google.protobuf.Timestamp
	4,  // 6: exmsg.models.LeadCareItem.created_at:type_name -> google.protobuf.Timestamp
	4,  // 7: exmsg.models.LeadCareItem.updated_at:type_name -> google.protobuf.Timestamp
	4,  // 8: exmsg.models.CareReason.created_at:type_name -> google.protobuf.Timestamp
	4,  // 9: exmsg.models.CareReason.updated_at:type_name -> google.protobuf.Timestamp
	4,  // 10: exmsg.models.CareReason.deleted_at:type_name -> google.protobuf.Timestamp
	11, // [11:11] is the sub-list for method output_type
	11, // [11:11] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_lead_proto_init() }
func file_lead_proto_init() {
	if File_lead_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_lead_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_lead_proto_goTypes,
		DependencyIndexes: file_lead_proto_depIdxs,
		MessageInfos:      file_lead_proto_msgTypes,
	}.Build()
	File_lead_proto = out.File
	file_lead_proto_rawDesc = nil
	file_lead_proto_goTypes = nil
	file_lead_proto_depIdxs = nil
}
