package handlers

import (
	"context"
	"errors"
	"github.com/Masterminds/squirrel"
	"github.com/go-redis/redis/v8"
	"github.com/spf13/cast"
	"gitlab.com/a7923/athena-go/cmd/services/agsale-service/tables"
	"gitlab.com/a7923/athena-go/pkg/dbtool"
	"gitlab.com/a7923/athena-go/pkg/logger"
	"gitlab.com/a7923/athena-go/pkg/utils"
	"gitlab.com/a7923/athena-go/proto/exmsg/models"
	"gitlab.com/a7923/athena-go/proto/exmsg/services"
)

func (a *AGSaleHandler) GetCareReasons(ctx context.Context, request *services.CareReasonRequest, response *services.CareReasonResponse) error {
	sqlTool := dbtool.NewSelect(ctx, a.db.GetConnection(), tables.GetCareReasonTable(), &models.CareReason{})

	limit := dbtool.GetQueryLimit(request.Query.GetLimit(), 50, 250)
	offset := dbtool.GetQueryOffset(limit, request.Query.GetPage())

	qb := squirrel.
		Select(sqlTool.GetQueryColumnList("")...).
		From(sqlTool.GetTable("")).
		Limit(limit).
		Offset(offset)

	if request.Id > 0 {
		qb = qb.Where(squirrel.Eq{"id": request.Id})
	} else {
		// todo handle select all
		if request.CountryId > 0 {
			qb = qb.Where(squirrel.Eq{"country_id": request.CountryId})
		}

		if request.CompanyId > 0 {
			qb = qb.Where(squirrel.Eq{"company_id": request.CompanyId})
		}

		if len(request.ReasonKey) > 0 {
			qb = qb.Where(squirrel.Eq{"reason_key": request.ReasonKey})
		}

		if request.State > 0 { // -> move this field to pointer
			qb = qb.Where(squirrel.Eq{"state": request.State})
		}
	}

	response.CareReasons = make([]*models.CareReason, 0)
	err := sqlTool.Select(ctx, &response.CareReasons, qb)
	if err != nil {
		logger.AthenaLogger.Errorw("Failed to scan row", "error", err)
		return err
	}
	return nil
}

func (a *AGSaleHandler) CreateLeadCareAndItem(ctx context.Context, request *services.LeadRequest, response *services.LeadResponse) (err error) {
	if request.LeadCare == nil || request.LeadCareItem == nil {
		return errors.New("lead care or item is required")
	}
	sqlTool, err := dbtool.NewTransaction(ctx, a.db.GetConnection())
	if err != nil {
		logger.AthenaLogger.Errorw("Failed to start transaction", "error", err)
		return err
	}

	defer func() {
		if err != nil {
			rerr := sqlTool.RollbackTransactions()
			if rerr != nil {
				logger.AthenaLogger.Errorw("Failed to rollback transaction", "error", rerr, "order_id", request.GetLead().OrderId)
			}
		}
	}()
	// create lead care and item
	sqlTool.PrepareInsert(ctx, tables.GetLeadCareTable(), request.LeadCare)
	qb := squirrel.
		Insert(sqlTool.GetTable("")).
		Columns(sqlTool.GetQueryColumnList("")...).
		Values(sqlTool.GetFilledValues(request.LeadCare)...)

	res, err := sqlTool.Insert(ctx, qb)
	if err != nil {
		logger.AthenaLogger.Errorw("Failed to insert lead care", "error", err)
		return err
	}

	leadCareId := res.LastInsertIds[0]
	request.LeadCareItem.LeadCareId = leadCareId
	sqlTool.PrepareInsert(ctx, tables.GetLeadCareItemTable(), request.LeadCareItem)
	qb = squirrel.Insert(sqlTool.GetTable("")).
		Columns(sqlTool.GetQueryColumnList("")...).
		Values(sqlTool.GetFilledValues(request.LeadCareItem)...)
	res, err = sqlTool.Insert(ctx, qb)
	if err != nil {
		logger.AthenaLogger.Errorw("Failed to insert lead care item", "error", err)
		return err
	}
	// insert log
	sqlTool.PrepareInsert(ctx, tables.GetLogTable(), &models.Log{})
	qb = squirrel.Insert(sqlTool.GetTable("")).
		Columns(sqlTool.GetQueryColumnList("")...).
		Values(sqlTool.GetFilledValues(models.Log{
			Action:    "INSERT",
			TableName: "lead_care_items",
			RecordId:  cast.ToString(res.LastInsertIds[0]),
			//CreatorId: "-3",
			Changes: utils.StructToArray(request.LeadCareItem)})...)
	res, err = sqlTool.Insert(ctx, qb)
	if err != nil {
		logger.AthenaLogger.Errorw("Failed to insert lead care item log", "error", err)
		return err
	}
	err = sqlTool.CommitTransactions()
	if err != nil {
		logger.AthenaLogger.Errorw("Failed to commit transaction", "error", err)
		return err
	}

	response.ExecResult = &models.SQLResult{
		LastInsertIds: []int64{request.LeadCareItem.LeadCareId},
		RowsAffected:  0,
	}

	return nil
}
func NewAGSaleHandler(db *dbtool.ConnectionManager, redis *redis.Client) *AGSaleHandler {
	return &AGSaleHandler{db: db, redis: redis}
}
