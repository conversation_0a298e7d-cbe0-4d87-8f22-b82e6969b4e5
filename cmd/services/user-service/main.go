package main

import (
	"gitlab.com/a7923/athena-go/cmd/services/user-service/handlers"
	"gitlab.com/a7923/athena-go/pkg/app"
	"gitlab.com/a7923/athena-go/pkg/common"
	"gitlab.com/a7923/athena-go/pkg/dbtool"
	"gitlab.com/a7923/athena-go/pkg/logger"
	"gitlab.com/a7923/athena-go/proto/exmsg/services"
)

func main() {
	svc := app.NewGRPCService(common.ServiceNameUser)
	psql, err := dbtool.NewConnectionManager(dbtool.DBTypePostgreSQL, nil)
	if err != nil {
		logger.AthenaLogger.Fatal("Failed to connect to database: ", err)
	}

	redis, err := dbtool.CreateRedisConnection(nil)
	if err != nil {
		logger.AthenaLogger.Fatal("Failed to connect to Redis: ", err)
	}

	grpcSvc := handlers.NewUserHandler(psql, redis)
	err = services.RegisterUserServiceHandler(svc.Server(), grpcSvc)
	if err != nil {
		logger.AthenaLogger.Fatal(err)
	}

	err = svc.Run()
	if err != nil {
		logger.AthenaLogger.Fatal(err)
	}
}
