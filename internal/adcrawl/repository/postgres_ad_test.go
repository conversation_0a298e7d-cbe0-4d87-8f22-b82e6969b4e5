package repository

import (
	"context"
	"os"
	"testing"

	"github.com/jmoiron/sqlx"
	"gitlab.com/a7923/athena-go/internal/adcrawl/domain"
)

func TestAdBatchInsertAfterDelete(t *testing.T) {
	db, err := sqlx.ConnectContext(context.Background(), "postgres", os.Getenv("AD_DB_URL"))
	if err != nil {
		t.Fatal(err)
	}
	defer db.Close()
	postgresAdRepository := NewPostgresAdRepository(db)
	ads := []domain.Ad{
		{
			ID:              int64(120216849497570081),
			Name:            "Video người dùng 2",
			Status:          "PAUSED",
			EffectiveStatus: "PAUSED",
			AdSetID:         int64(120213785046200279),
			PageID:          "492860973913172",
			ObjectStoryID:   nil,
			ObjectStorySpec: nil,
			ImageURL:        nil,
		},
		{
			ID:              int64(120213521813760128),
			Name:            "KMA 17 - Content mới",
			Status:          "ACTIVE",
			EffectiveStatus: "DISAPPROVED",
			AdSetID:         int64(120213785046200279),
			PageID:          "398887416641229",
			ObjectStoryID:   nil,
			ObjectStorySpec: nil,
			ImageURL:        nil,
		},
	}

	withElapsedTime(t, func() {
		t.Run("trivial", func(t *testing.T) {
			if err := postgresAdRepository.BatchInsert(context.Background(), ads); err != nil {
				t.Fatal(err)
			}
		})
	})
}
