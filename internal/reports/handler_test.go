package reports

import (
	"fmt"
	sq "github.com/Masterminds/squirrel"
	"strings"
	"testing"
)

var dimensionMap = map[string]string{
	"group_by_all": "1 AS group_by_all",
	"date":         "toDate(o.ts) AS date",
	"adset":        "any(o.adset) AS adset",
	"campaign":     "any(o.campaign) AS campaign",
	"adaccount":    "any(o.adaccount) AS adaccount",
	"marketer":     "any(users.name) AS marketer",
	"project":      "any(projects.name) AS project",
	"product":      "any(JSONExtractString(o.product_detail, 'name')) AS product",
}

// WITH clause for derived metrics and labels
func buildWithClause() string {
	return `
WITH 
  1 AS group_by_all,
  any(o.adset) AS adset,
  any(o.campaign) AS campaign,
  any(o.adaccount) AS adaccount,
  any(users.name) AS marketer,
  any(projects.name) AS project,
  any(JSONExtractString(o.product_detail, 'name')) AS product,
  uniqExact(o.lead_id) AS lead_count,
  uniqExactIf(o.order_id, o.order_status IN (11, 12, 13, 14)) AS return_count,
  uniqExactIf(o.order_id, o.order_status NOT IN (-2, 12, 13, 14, 17)) AS total_orders,
  uniqExactIf(o.order_id, o.order_status NOT IN (-2, 17) AND toDate(o.created_at) = toDate(o.first_update_status_at)) AS new_orders,
  uniqExactIf(o.order_id, o.order_status NOT IN (-2, 17) AND toDate(o.created_at) != toDate(o.first_update_status_at)) AS old_orders,
  uniqExactIf(o.lead_id, o.last_cared_at IS NOT NULL) AS handled_lead,
  SUM(o.sale) * rate AS sale,
  SUM(o.revenue) * rate AS revenue
`
}

// Metrics plus dynamic dimension selections
func buildSelectClause(groupBys []string) []string {
	selects := []string{
		"sum(a.clicks) AS clicks",
		"sum(a.cpm) AS cpm",
		"sum(a.ctr) AS ctr",
		"sum(a.cpc) AS cpc",
		"sum(a.impressions) AS impressions",
		"sum(a.spent) AS spent",
		"lead_count",
		"return_count",
		"new_orders",
		"old_orders",
		"total_orders",
		"handled_lead",
		"sale",
		"revenue",
	}
	for _, dim := range groupBys {
		if def, ok := dimensionMap[dim]; ok {
			selects = append([]string{def}, selects...)
		}
	}
	return selects
}

// Build join keys dynamically based on group by dimensions
func buildJoinKeysFromGroupBys(groupBys []string) []string {
	joinKeys := []string{"ad_id"} // Always join by ad_id
	for _, dim := range groupBys {
		switch dim {
		case "date":
			joinKeys = append(joinKeys, "ts") // date alignment
		case "project":
			joinKeys = append(joinKeys, "project_id")
		case "product":
			joinKeys = append(joinKeys, "product_id")
		case "marketer":
			joinKeys = append(joinKeys, "marketer_id")
		case "adaccount":
			joinKeys = append(joinKeys, "adaccount_id")
		case "campaign":
			joinKeys = append(joinKeys, "campaign_id")
		case "adset":
			joinKeys = append(joinKeys, "adset_id")
		}
	}
	return joinKeys
}

// Build SQL join ON clause from join keys
func buildJoinCondition(joinKeys []string) string {
	var parts []string
	for _, key := range joinKeys {
		if key == "ts" {
			parts = append(parts, "toDate(o.ts) = toDate(a.ts)")
		} else {
			parts = append(parts, fmt.Sprintf("o.%s = a.%s", key, key))
		}
	}
	return strings.Join(parts, " AND ")
}

// Build the full dynamic query
func buildQuery(groupBys []string, filters map[string]interface{}) (string, []interface{}, error) {
	joinKeys := buildJoinKeysFromGroupBys(groupBys)
	joinCondition := buildJoinCondition(joinKeys)

	sb := sq.Select(buildSelectClause(groupBys)...).
		From("analytics.base_orders o").
		LeftJoin(fmt.Sprintf("analytics.base_ads a ON %s", joinCondition)).
		Prefix(strings.TrimSpace(buildWithClause()))

	// Apply filter conditions
	if v, ok := filters["date_from"]; ok {
		sb = sb.Where(sq.GtOrEq{"toDate(o.ts)": v})
	}
	if v, ok := filters["date_to"]; ok {
		sb = sb.Where(sq.LtOrEq{"toDate(o.ts)": v})
	}
	for _, f := range []string{"project_id", "marketer_id", "product_id", "adaccount_id", "campaign_id", "adset_id"} {
		if v, ok := filters[f]; ok {
			sb = sb.Where(sq.Eq{fmt.Sprintf("o.%s", f): v})
		}
	}

	// Group by extracted column aliases
	groupCols := []string{}
	for _, dim := range groupBys {
		if col, ok := dimensionMap[dim]; ok {
			colAlias := strings.Split(col, " AS ")[1]
			groupCols = append(groupCols, colAlias)
		}
	}
	if len(groupCols) > 0 {
		sb = sb.GroupBy(groupCols...)
	}

	sb = sb.OrderBy("date ASC")

	return sb.ToSql()
}

func main() {
	groupBys := []string{"date", "project", "adaccount", "campaign"}

	filters := map[string]interface{}{
		"date_from":    "2025-05-01",
		"date_to":      "2025-05-07",
		"project_id":   123,
		"marketer_id":  456,
		"adaccount_id": 789,
	}

	query, args, err := buildQuery(groupBys, filters)
	if err != nil {
		panic(err)
	}

	fmt.Println("Generated SQL Query:\n", query)
	fmt.Println("\nArguments:\n", args)
}

func TestName(t *testing.T) {
	main()
}
