package handlers

import (
	"context"
	"errors"
	"fmt"
	"github.com/spf13/cast"
	"github.com/spf13/viper"
	"gitlab.com/a7923/athena-go/internal/enums"
	"gitlab.com/a7923/athena-go/pkg/common"
	"gitlab.com/a7923/athena-go/pkg/config"
	"gitlab.com/a7923/athena-go/pkg/logger"
	"gitlab.com/a7923/athena-go/pkg/replication/pglogicalstream"
	"gitlab.com/a7923/athena-go/pkg/utils/pg_converter"
	"gitlab.com/a7923/athena-go/proto/exmsg/models"
	"gitlab.com/a7923/athena-go/proto/exmsg/services"
	"go-micro.dev/v5/client"
	"google.golang.org/protobuf/types/known/timestamppb"
	"time"
)

func (h *OrderHandler) handleOrderChanged(change pglogicalstream.Wal2JsonChange) error {
	oldVal, newVal := change.GetValue("status")
	if oldVal == nil && newVal == nil {
		return nil
	}

	// current status equal delivered and previous not equal
	isStatusUpdated := cast.ToInt(oldVal) != cast.ToInt(newVal)

	if !isStatusUpdated {
		return nil
	}

	isStatusTransitionToDelivered := enums.Delivered.EqualNumber(cast.ToInt(newVal))                                      // 7 -> 8
	isRevertStatus := enums.Delivered.EqualNumber(cast.ToInt(oldVal)) && enums.InDelivery.EqualNumber(cast.ToInt(newVal)) // 8 -> 7
	if !isStatusTransitionToDelivered && !isRevertStatus {
		return nil
	}

	order := &models.Order{}
	protoConverter := pg_converter.NewProtoConverter(order, time.UTC)
	err := protoConverter.ConvertToStruct(
		change.ColumnNames,
		change.ColumnTypes,
		change.ColumnValues,
		order,
	)

	if err != nil {
		logger.AthenaLogger.Errorw("Failed to convert value to struct", "error", err.Error())
		return err
	}

	if isStatusTransitionToDelivered {
		logger.AthenaLogger.Infow("Processing gen lead aftersales of order", "order_id", order.Id)
	}

	ctx := context.Background()
	projects, err := h.UserClient.GetProjects(ctx, &services.ProjectRequest{
		ProjectIds: []int64{order.ProjectId},
	})

	if err != nil {
		logger.AthenaLogger.Errorw("Failed to get projects by order id", "project_id", order.ProjectId, "error", err.Error())
		return err
	}

	if len(projects.GetProjects()) == 0 {
		logger.AthenaLogger.Infow("Projects not found by order id. drop message", "order_id", order.ProjectId)
		return nil
	}

	enableAfterSales := false
	settings := projects.Projects[0].GetSettings()

	enableAfterSales = settings != nil && cast.ToBool(settings.AsMap()["enable_after_sale"])
	if !enableAfterSales {
		logger.AthenaLogger.Infow("Enable after sale is not enabled. drop message", "project_id", order.ProjectId)
		return nil
	}

	orderId := order.Id
	// get leads by order id
	originalLead, err := h.AGSaleClient.GetLeads(ctx, &services.LeadRequest{
		Query: &models.Query{
			Limit: 1,
			Page:  0,
		},
		OrderId: orderId,
	})
	if err != nil {
		if common.IsNotFoundError(err) {
			logger.AthenaLogger.Infow("Not found lead of this order, processing to generate lead from another source", "order_id", orderId)
		}
		logger.AthenaLogger.Errorw("Failed to get leads by order id", "order_id", orderId, "error", err.Error())
		return err
	}
	winStatus := []int64{
		int64(enums.CareState_Temp),
		int64(enums.CareState_AwaitingStock),
		int64(enums.CareState_Reconfirm),
		int64(enums.CareState_Confirmed),
		int64(enums.CareState_Lost),
		int64(enums.CareState_Junk),
	}
	if originalLead == nil || len(originalLead.Leads) < 1 {
		logger.AthenaLogger.Infow("Not found lead of this order, processing to generate lead from another source", "order_id", orderId)
	}
	if isRevertStatus {
		logger.AthenaLogger.Infow("Process handle cancel lead, order aftersales when revert origin order")
		// handle logic cancel lead, order aftersales when revert origin order status
		afterSaleOrder, err := h.AGSaleClient.GetOrders(ctx, &services.OrderRequest{
			Query: &models.Query{
				Limit: 1,
				Page:  0,
			},
			SourceId:       orderId,
			NotEqualStatus: int64(enums.Canceled),
		})
		if err != nil {
			logger.AthenaLogger.Errorw("Failed to get order aftersales generate from this order", "order_id", orderId, "error", err.Error())
			return err
		}
		if afterSaleOrder == nil || len(afterSaleOrder.Orders) < 1 {
			logger.AthenaLogger.Errorw("Failed to get order aftersales generate from this order", "order_id", orderId)
			return nil
		}

		afterSaleLead, err := h.AGSaleClient.GetLeads(ctx, &services.LeadRequest{
			Query: &models.Query{
				Limit: 1,
				Page:  0,
			},
			OrderId:     afterSaleOrder.Orders[0].Id,
			LeadType:    int64(enums.LeadAfterSale),
			NotInStatus: winStatus,
		})
		if err != nil {
			logger.AthenaLogger.Errorw("Failed to get lead aftersales generate from this order", "order_id", orderId, "error", err.Error())
			return err
		}
		if afterSaleLead == nil || len(afterSaleLead.Leads) < 1 {
			logger.AthenaLogger.Errorw("There isn't lead, need to cancel when revert origin order", "order_id", orderId)
			return nil
		}
		err = h.updateRevertStatus(ctx, afterSaleLead.Leads[0], afterSaleOrder.Orders[0])
		if err != nil {
			logger.AthenaLogger.Errorw("Failed to update revert status", "order_id", orderId, "error", err.Error())
			return err
		}
		return nil
	}

	lead := &models.Lead{}

	if originalLead != nil && len(originalLead.Leads) >= 1 {
		lead = originalLead.Leads[0]
	}

	//// get order products
	//orderProducts, err := h.AGSaleClient.GetOrderProducts(ctx, &services.OrderRequest{
	//	OrderId: orderId,
	//})
	//if err != nil {
	//	logger.AthenaLogger.Errorw("Failed to get order products by order id", "order_id", orderId, "error", err.Error())
	//	return err
	//}
	//
	//if len(orderProducts.OrderProducts) < 1 {
	//	logger.AthenaLogger.Infow("Order products not found by order id. drop message", "order_id", orderId)
	//	return nil
	//}

	// create lead after delivery
	lead.Id = 0
	lead.State = 0
	lead.LeadType = int64(enums.LeadAfterSale)
	lead.LastCareId = 0
	lead.LastCareReasonId = 0
	lead.LastCareItemId = 0
	lead.CurrentCareId = 0
	lead.CreatedAt = timestamppb.New(time.Now())
	lead.UpdatedAt = timestamppb.New(time.Now())
	lead.CountryId = order.CountryId
	lead.UserId = 0

	retryGetOrderDisplayId := 0
	orderDisplayId := ""
	for retryGetOrderDisplayId < config.ViperGetIntWithDefault("retry_get_order_display_id", 5) {
		logger.AthenaLogger.Infow("Retrying get order display id gen lead from order", "order_id", order.Id, "retry", retryGetOrderDisplayId)
		orderRes, err := h.AGSaleClient.GetOrderDisplayId(ctx, &services.OrderDisplayIdRequest{
			ProjectId: order.ProjectId,
		})
		if err != nil || orderRes == nil {
			logger.AthenaLogger.Errorw("Failed to get order count", "project_id", order.ProjectId, "error", err)
			return err
		}
		t, err := time.Parse(time.RFC3339, orderRes.OrderDisplayIdCount.Date)
		if err != nil {
			fmt.Println("Error parsing date:", err)
			return err
		}
		dateStr := t.Format("20060102")

		orderDisplayId = fmt.Sprintf("ASL-%v%v%v%v", order.CompanyId, order.ProjectId, dateStr, orderRes.OrderDisplayIdCount.Count)
		logger.AthenaLogger.Infow("Result get order display id for gen lead aftersales", "order_id", order.Id, "order_display_id", orderDisplayId)
		orderCheckDisplayId, err := h.AGSaleClient.GetOrders(ctx, &services.OrderRequest{
			DisplayId: orderDisplayId,
			CompanyId: order.CompanyId,
		})
		if err != nil {
			fmt.Println("Fail to get order to check exist display_id:", err)
			return err
		}
		if len(orderCheckDisplayId.GetOrders()) == 0 {
			break
		}
		retryGetOrderDisplayId++
		time.Sleep(time.Millisecond * 500)
	}
	if retryGetOrderDisplayId == config.ViperGetIntWithDefault("retry_get_order_display_id", 5) {
		logger.AthenaLogger.Errorw("Failed to get order display id to gen lead aftersales of order", "order_id", order.Id)
		return errors.New("failed to get order display id")
	}

	newOrder := &models.Order{
		Status:            int64(enums.Draft),
		SourceId:          order.Id,
		DisplayId:         orderDisplayId,
		CreatorId:         viper.GetInt64("system.user_id"),
		CreatedAt:         timestamppb.New(time.Now()),
		UpdatedAt:         timestamppb.New(time.Now()),
		CountryId:         order.CountryId,
		CustomerName:      order.CustomerName,
		CustomerPhone:     order.CustomerPhone,
		AddressText:       order.AddressText,
		AddressWard:       order.AddressWard,
		AddressDistrict:   order.AddressDistrict,
		AddressProvince:   order.AddressProvince,
		AddressWardId:     order.AddressWardId,
		AddressDistrictId: order.AddressDistrictId,
		AddressProvinceId: order.AddressProvinceId,
		CompanyId:         order.CompanyId,
		TeamInCharge:      order.TeamInCharge,
		CrossCare:         order.CrossCare,
		FbScopedUserId:    order.FbScopedUserId,
		SourceProjectId:   order.ProjectId,
		MarketerId:        order.MarketerId,
		Type:              order.Type,
		PostCode:          order.PostCode,
	}
	//order.Status = int64(enums.New)
	//order.SourceId = order.Id
	//order.Id = 0
	//order.ShippingFee = 0
	//order.DisplayId = fmt.Sprintf("%v-%v", order.DisplayId, strings.ToUpper(strconv.FormatInt(time.Now().Unix(), 36)))
	//order.CreatorId = viper.GetInt64("system.user_id")
	//order.CreatedAt = timestamppb.New(time.Now())
	//order.UpdatedAt = timestamppb.New(time.Now())
	//order.Note = ""
	//order.ExternalSourceId = 0
	//order.FfmDisplayId = ""
	//order.SaleId = 0
	//order.ProjectId = 0
	//order.MarketerId = 0

	leadRequest := services.LeadRequest{
		Order:            newOrder,
		Lead:             lead,
		UsingTransaction: true,
	}

	leadAndOrder, err := h.AGSaleClient.CreateLead(ctx, &leadRequest, client.WithRetries(3))

	if err != nil {
		logger.AthenaLogger.Errorw("Failed to create lead after delivery", "order_id", orderId, "error", err.Error())
		return err
	}

	logger.AthenaLogger.Infow("Created lead after delivery",
		"from_order_id", orderId,
		"lead_id", leadAndOrder.ExecResult.LastInsertIds,
	)
	return nil
}
