package main

import (
	"gitlab.com/a7923/athena-go/cmd/consumers/data-replica/binlog-consumer/handlers"
	"gitlab.com/a7923/athena-go/pkg/app"
	"gitlab.com/a7923/athena-go/pkg/logger"
)

func main() {
	handler := &handlers.BinlogHandler{
		Name: "binlog_consumer",
	}

	err := app.StartDataReplica(handler)
	if err != nil {
		logger.AthenaLogger.Fatal("Failed to start data replica consumer: ", err)
	}
}
