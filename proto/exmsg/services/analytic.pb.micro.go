// Code generated by protoc-gen-micro. DO NOT EDIT.
// source: services/analytic.proto

package services

import (
	fmt "fmt"
	_ "gitlab.com/a7923/athena-go/proto/exmsg/models"
	proto "google.golang.org/protobuf/proto"
	math "math"
)

import (
	context "context"
	client "go-micro.dev/v5/client"
	server "go-micro.dev/v5/server"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ client.Option
var _ server.Option

// Client API for AnalyticService service

type AnalyticService interface {
	QueryReport(ctx context.Context, in *AnalyticsRequest, opts ...client.CallOption) (*AnalyticsResponse, error)
	Query(ctx context.Context, in *AnalyticsRequest, opts ...client.CallOption) (*AnalyticsResponse, error)
	QueryOrderDashboardOverview(ctx context.Context, in *OrderDashboardRequest, opts ...client.CallOption) (*AnalyticsResponse, error)
	// Telesale dashboard
	QueryTelesaleDashboardOverview(ctx context.Context, in *TelesaleDashboardRequest, opts ...client.CallOption) (*AnalyticsResponse, error)
}

type analyticService struct {
	c    client.Client
	name string
}

func NewAnalyticService(name string, c client.Client) AnalyticService {
	return &analyticService{
		c:    c,
		name: name,
	}
}

func (c *analyticService) QueryReport(ctx context.Context, in *AnalyticsRequest, opts ...client.CallOption) (*AnalyticsResponse, error) {
	req := c.c.NewRequest(c.name, "AnalyticService.QueryReport", in)
	out := new(AnalyticsResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *analyticService) Query(ctx context.Context, in *AnalyticsRequest, opts ...client.CallOption) (*AnalyticsResponse, error) {
	req := c.c.NewRequest(c.name, "AnalyticService.Query", in)
	out := new(AnalyticsResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *analyticService) QueryOrderDashboardOverview(ctx context.Context, in *OrderDashboardRequest, opts ...client.CallOption) (*AnalyticsResponse, error) {
	req := c.c.NewRequest(c.name, "AnalyticService.QueryOrderDashboardOverview", in)
	out := new(AnalyticsResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *analyticService) QueryTelesaleDashboardOverview(ctx context.Context, in *TelesaleDashboardRequest, opts ...client.CallOption) (*AnalyticsResponse, error) {
	req := c.c.NewRequest(c.name, "AnalyticService.QueryTelesaleDashboardOverview", in)
	out := new(AnalyticsResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for AnalyticService service

type AnalyticServiceHandler interface {
	QueryReport(context.Context, *AnalyticsRequest, *AnalyticsResponse) error
	Query(context.Context, *AnalyticsRequest, *AnalyticsResponse) error
	QueryOrderDashboardOverview(context.Context, *OrderDashboardRequest, *AnalyticsResponse) error
	// Telesale dashboard
	QueryTelesaleDashboardOverview(context.Context, *TelesaleDashboardRequest, *AnalyticsResponse) error
}

func RegisterAnalyticServiceHandler(s server.Server, hdlr AnalyticServiceHandler, opts ...server.HandlerOption) error {
	type analyticService interface {
		QueryReport(ctx context.Context, in *AnalyticsRequest, out *AnalyticsResponse) error
		Query(ctx context.Context, in *AnalyticsRequest, out *AnalyticsResponse) error
		QueryOrderDashboardOverview(ctx context.Context, in *OrderDashboardRequest, out *AnalyticsResponse) error
		QueryTelesaleDashboardOverview(ctx context.Context, in *TelesaleDashboardRequest, out *AnalyticsResponse) error
	}
	type AnalyticService struct {
		analyticService
	}
	h := &analyticServiceHandler{hdlr}
	return s.Handle(s.NewHandler(&AnalyticService{h}, opts...))
}

type analyticServiceHandler struct {
	AnalyticServiceHandler
}

func (h *analyticServiceHandler) QueryReport(ctx context.Context, in *AnalyticsRequest, out *AnalyticsResponse) error {
	return h.AnalyticServiceHandler.QueryReport(ctx, in, out)
}

func (h *analyticServiceHandler) Query(ctx context.Context, in *AnalyticsRequest, out *AnalyticsResponse) error {
	return h.AnalyticServiceHandler.Query(ctx, in, out)
}

func (h *analyticServiceHandler) QueryOrderDashboardOverview(ctx context.Context, in *OrderDashboardRequest, out *AnalyticsResponse) error {
	return h.AnalyticServiceHandler.QueryOrderDashboardOverview(ctx, in, out)
}

func (h *analyticServiceHandler) QueryTelesaleDashboardOverview(ctx context.Context, in *TelesaleDashboardRequest, out *AnalyticsResponse) error {
	return h.AnalyticServiceHandler.QueryTelesaleDashboardOverview(ctx, in, out)
}
