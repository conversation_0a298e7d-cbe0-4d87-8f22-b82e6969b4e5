package handlers

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"github.com/Masterminds/squirrel"
	"github.com/go-redis/redis/v8"
	"gitlab.com/a7923/athena-go/cmd/services/user-service/tables"
	"gitlab.com/a7923/athena-go/pkg/common"
	"gitlab.com/a7923/athena-go/pkg/dbtool"
	"gitlab.com/a7923/athena-go/pkg/logger"
	"gitlab.com/a7923/athena-go/pkg/utils"
	"gitlab.com/a7923/athena-go/proto/exmsg/models"
	"gitlab.com/a7923/athena-go/proto/exmsg/services"
)

type UserHandler struct {
	db    *dbtool.ConnectionManager
	redis *redis.Client
}

func (u *UserHandler) GetProjects(ctx context.Context, request *services.ProjectRequest, response *services.ProjectResponse) error {
	sqlTool := dbtool.NewSelect(ctx, u.db.GetConnection(), tables.GetProjectTable(), &models.Project{})
	limit := dbtool.GetQueryLimit(request.Query.GetLimit(), 50, 250)
	offset := dbtool.GetQueryOffset(limit, request.Query.GetPage())

	qb := squirrel.
		Select(sqlTool.GetQueryColumnList("")...).
		From(sqlTool.GetTable("")).
		Limit(limit).
		Offset(offset)

	qb = qb.Where(squirrel.Eq{"id": request.ProjectIds}) // add more filter here

	response.Projects = make([]*models.Project, 0)
	err := sqlTool.Select(ctx, &response.Projects, qb)
	if err != nil {
		logger.AthenaLogger.Errorw("Failed to scan row", "error", err)
		return err
	}
	return nil
}

func (u *UserHandler) GetProfiles(ctx context.Context, request *services.ProfileRequest, response *services.ProfileResponse) error {
	sqlTool := dbtool.NewSelect(ctx, u.db.GetConnection(), tables.GetProfileTable(), &models.UserProfile{})

	limit := dbtool.GetQueryLimit(request.Query.GetLimit(), 50, 250)
	offset := dbtool.GetQueryOffset(limit, request.Query.GetPage())

	qb := squirrel.
		Select(sqlTool.GetQueryColumnList("u")...).
		From(sqlTool.GetTable("u")).
		Limit(limit).
		Offset(offset)

	if len(request.AncestorDepartmentIds) > 0 {
		subQb := squirrel.Select("id_descendant").
			From("departments_closure").
			Where(squirrel.Eq{"id_ancestor": request.AncestorDepartmentIds})

		qb = qb.Where(squirrel.Expr("department_id IN (?)", subQb))
	} else {
		qb = qb.Where(squirrel.Eq{"user_id": request.UserId})
	}

	response.Profiles = make([]*models.UserProfile, 0)
	err := sqlTool.Select(ctx, &response.Profiles, qb)
	if err != nil {
		logger.AthenaLogger.Errorw("Failed to scan row", "error", err)
		return err
	}
	return nil
}

func (u *UserHandler) GetDepartments(ctx context.Context, request *services.DepartmentRequest, response *services.DepartmentResponse) error {
	//TODO implement me
	panic("implement me")
}

func (u *UserHandler) GetRoles(ctx context.Context, request *services.RoleRequest, response *services.RoleResponse) error {
	sqlTool := dbtool.NewSelect(ctx, u.db.GetConnection(), tables.GetRoleTable(), &models.Role{})

	limit := dbtool.GetQueryLimit(request.Query.GetLimit(), 50, 250)
	offset := dbtool.GetQueryOffset(limit, request.Query.GetPage())

	qb := squirrel.
		Select(sqlTool.GetQueryColumnList("r")...).
		From(sqlTool.GetTable("r")).
		//Where(squirrel.Eq{"id": request.r}).
		Limit(limit).
		Offset(offset)

	if len(request.RoleIds) > 0 {
		qb = qb.Where(squirrel.Eq{"id": request.RoleIds})
	} else {
		qb = qb.Where(squirrel.Eq{"id": request.RoleId})
	}

	response.Roles = make([]*models.Role, 0)
	err := sqlTool.Select(ctx, &response.Roles, qb)
	if err != nil {
		logger.AthenaLogger.Errorw("Failed to scan row", "error", err)
		return err
	}
	return nil
}

func (u *UserHandler) GetDataSetScopes(ctx context.Context, request *services.DataSetScopeRequest, response *services.DataSetScopeResponse) error {
	sqlTool := dbtool.NewSelect(ctx, u.db.GetConnection(), tables.GetDataSetTable(), &models.DataSetScope{})

	limit := dbtool.GetQueryLimit(request.Query.GetLimit(), 50, 250)
	offset := dbtool.GetQueryOffset(limit, request.Query.GetPage())

	qb := squirrel.
		Select(sqlTool.GetQueryColumnList("u")...).
		From(sqlTool.GetTable("u")).
		//Where(squirrel.Eq{"user_id": request.UserId}).
		Limit(limit).
		Offset(offset)

	if len(request.DataSetIds) > 0 {
		qb = qb.Where(squirrel.Eq{"data_set_id": request.DataSetIds})
	} else if len(request.DepartmentIds) > 0 {
		//data_set_id
		//SELECT data_set_id FROM departments WHERE departments.id in (249)

		subQb := squirrel.Select("data_set_id").
			From("departments").
			Where(squirrel.Eq{"id": request.DepartmentIds}).PlaceholderFormat(squirrel.Dollar)

		qb = qb.Where(squirrel.Expr("data_set_id IN (?)", subQb))
	} else {
		qb = qb.Where(squirrel.Eq{"data_set_id": request.DataSetId})
	}

	response.Scopes = make([]*models.DataSetScope, 0)
	err := sqlTool.Select(ctx, &response.Scopes, qb)
	if err != nil {
		logger.AthenaLogger.Errorw("Failed to scan row", "error", err)
		return err
	}
	return nil
}

func (u *UserHandler) GetUserByID(ctx context.Context, request *services.UserRequest, response *services.UserResponse) error {
	sqlTool := dbtool.NewSelect(ctx, u.db.GetConnection(), tables.GetUserTable(), &models.User{})

	qb := squirrel.
		Select(sqlTool.GetQueryColumnList("u")...).
		From(sqlTool.GetTable("u")).
		Where(squirrel.Eq{"id": request.UserId}).
		Limit(1)

	response.User = &models.User{}
	err := sqlTool.Get(ctx, response.User, qb)
	if err != nil {
		logger.AthenaLogger.Errorw("Failed to scan row", "error", err)
		return err
	}
	return nil
}

func (u *UserHandler) GetUsersByOptions(ctx context.Context, request *services.GetUsersByOptionsRequest, response *services.GetUsersByOptionsResponse) error {
	sqlTool := dbtool.NewSelect(ctx, u.db.GetConnection(), tables.GetUserTable(), &models.User{})

	sortBy := request.SortField + " " + common.BoolToOrder(request.IsAsc)
	qb := squirrel.
		Select(sqlTool.GetQueryColumnList("u")...).
		From(sqlTool.GetTable("u")).
		Where(squirrel.Eq{"company_id": request.CompanyId}).
		OrderBy(sortBy)

	if len(request.Ids) > 0 {
		qb = qb.Where(squirrel.Eq{"id": request.Ids})
	}

	response.Users = make([]*models.User, 0)
	err := sqlTool.Select(ctx, &response.Users, qb)
	if err != nil {
		logger.AthenaLogger.Errorw("Failed to scan row", "error", err)
		return err
	}

	return nil
}

type UserPermission struct {
	ModuleInCharge  []int64
	DataAccessLevel int
}

func (u *UserHandler) GetUserPermission(ctx context.Context, request *services.GetUserPermissionRequest, response *services.GetUserPermissionResponse) error {
	sqlTool := dbtool.NewSelect(ctx, u.db.GetConnection(), tables.GetRoleTable(), &models.Role{})

	qb := squirrel.Select("module_in_charge", "data_access_level").
		From(sqlTool.GetTable("r")).
		Where(squirrel.Expr("id IN (SELECT role_id FROM user_profiles WHERE user_id = ? AND status=1)", request.UserId))

	dbQueryResult := make([]*UserPermission, 0)
	err := sqlTool.Select(ctx, &dbQueryResult, qb)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			response.HasFullPermission = true
			return nil
		}
		logger.AthenaLogger.Errorw("Failed to scan row", "error", err)
		return err
	}

	response.HasFullPermission = true
	for _, perm := range dbQueryResult {
		if len(perm.ModuleInCharge) == 0 {
			response.HasFullPermission = true
			break
		}

		if perm.DataAccessLevel == 0 {
			response.HasFullPermission = false
		} else {
			response.AllowAccessDescendants = true
		}
	}
	return nil
}

func (u *UserHandler) GetUserProfile(ctx context.Context, request *services.GetUserPermissionRequest, response *services.GetUserProfileResponse) error {
	sqlTool := dbtool.NewSelect(ctx, u.db.GetConnection(), tables.GetRoleTable(), &models.Role{})

	qb := squirrel.Select("module_in_charge", "data_access_level").
		From(sqlTool.GetTable("r")).
		Where(squirrel.Expr("id IN (SELECT role_id FROM user_profiles WHERE user_id = ? AND status=1)", request.UserId))

	dbQueryResult := make([]*UserPermission, 0)
	err := sqlTool.Select(ctx, &dbQueryResult, qb)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			response.Profiles = []string{}
			return nil
		}
		logger.AthenaLogger.Errorw("Failed to scan row", "error", err)
		return err
	}
	profiles := make([]string, 0)
	for _, perm := range dbQueryResult {
		profiles = append(profiles, fmt.Sprintf("%v:%v", perm.DataAccessLevel, utils.IntSliceToString(perm.ModuleInCharge, ",")))
	}
	response.Profiles = profiles

	return nil
}

func NewUserHandler(db *dbtool.ConnectionManager, redis *redis.Client) *UserHandler {
	return &UserHandler{db: db, redis: redis}
}
