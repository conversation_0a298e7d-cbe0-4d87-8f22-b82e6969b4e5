package handlers

import (
	"encoding/json"
	"fmt"
	"google.golang.org/protobuf/types/known/structpb"
	"net/http"
	"time"

	"github.com/spf13/cast"
	"gitlab.com/a7923/athena-go/pkg/common"
	"gitlab.com/a7923/athena-go/pkg/logger"
	transhttp "gitlab.com/a7923/athena-go/pkg/transport"
	"gitlab.com/a7923/athena-go/proto/exmsg/services"
)

type AdvancedCrawlHandler struct {
	CrawlClient services.CrawlService
}

type AdvancedCrawlRequest struct {
	Token     string
	Ids       []string
	InitType  string     `json:"init_type"`
	TimeRange *TimeRange `json:"time_range"`
	// Depth    int
}

type TimeRange struct {
	Since string `json:"since"`
	Until string `json:"until"`
}

type AdvancedCrawlResponse struct {
	success bool
}

func (h *AdvancedCrawlHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	defer func() {
		logger.AthenaLogger.Debugw("Processed request", "url", r.URL.Path, "took", time.Since(start).Milliseconds())
	}()

	// Decodes the request body
	httpReq := AdvancedCrawlRequest{}
	if err := json.NewDecoder(r.Body).Decode(&httpReq); err != nil {
		transhttp.RespondError(w, http.StatusBadRequest, err.Error())
		return
	}

	var userID int64
	if err := common.ExtractHeader(r, "X-Gw-Sub", &userID); err != nil {
		transhttp.RespondError(w, http.StatusBadRequest, fmt.Sprintf("failed to extract userID from request header: %v", err))
		return
	}
	grpcReq := &services.AdvancedCrawlRequest{
		InitType: httpReq.InitType,
		// Depth:    int32(httpReq.Depth),
		MarketerId: userID,
	}
	for _, id := range httpReq.Ids {
		task := &services.TaskCore{
			Id:    cast.ToInt64(id),
			Token: httpReq.Token,
		}

		meta := make(map[string]interface{})
		if httpReq.TimeRange != nil {
			meta["since"] = httpReq.TimeRange.Since
			meta["until"] = httpReq.TimeRange.Until
		}

		// other params
		task.Metadata, _ = structpb.NewStruct(meta)
		grpcReq.Tasks = append(grpcReq.Tasks, task)
	}

	_, err := h.CrawlClient.AdvancedCrawl(r.Context(), grpcReq)
	if err != nil {
		transhttp.RespondError(w, http.StatusInternalServerError, err.Error())
		return
	}

	transhttp.RespondJSON(w, http.StatusOK, AdvancedCrawlResponse{
		success: true,
	})
}
