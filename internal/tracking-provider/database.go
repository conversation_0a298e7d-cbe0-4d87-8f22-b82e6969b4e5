package tracking_provider

import (
	"context"
	"gitlab.com/a7923/athena-go/proto/exmsg/models"
	"gitlab.com/a7923/athena-go/proto/exmsg/services"
)

type Database struct {
	EventServiceClient services.EventServiceClient
}

func (d Database) SendTracking(ctx context.Context, tracking *models.Event) error {
	_, err := d.EventServiceClient.CreateEvents(ctx, &services.EventRequest{
		Events: []*models.Event{
			tracking,
		},
	})

	return err
}
