syntax = "proto3";

package exmsg.models;

option go_package = "gitlab.com/a7923/athena-go/proto/exmsg/models;models";

import "google/protobuf/timestamp.proto";

message AdAccount {
    int64 id = 1;
    string name = 2;
    string status = 3;
    string currency = 4;
    float timezone_offset_hours_utc = 5;
    int64 analytic_account_id = 6;
    optional string analytic_account_name = 7;
    optional int32 country_id = 8;
    optional bool is_hidden = 9;
    optional int64 marketer_id = 10;
    optional google.protobuf.Timestamp updated_at = 11;
    optional int64 updated_by_id = 12;
}