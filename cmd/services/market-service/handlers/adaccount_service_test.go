package handlers

import (
	"context"
	"sync"
	"testing"

	"gitlab.com/a7923/athena-go/proto/exmsg/services"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
)

// Must run gRPC server before running this test

var (
	client     services.MarketServiceClient
	clientOnce sync.Once
)

func TestListAdAccounts(t *testing.T) {
	client = getClient()

	companyID := int64(123)
	req := &services.ListAdAccountsRequest{
		CompanyId: companyID,
		Page:      1,
		Limit:     2,
	}
	resp, err := client.ListAdAccounts(context.Background(), req)
	if err != nil {
		t.Fatalf("Failed to list ad accounts: %v", err)
	}

	for _, adAccount := range resp.Adaccounts {
		t.Logf("AdAccount: %v", adAccount)
	}
}

func TestUpdateAdAccount(t *testing.T) {

}

func TestDeleteAdAccount(t *testing.T) {

}

func getClient() services.MarketServiceClient {
	clientOnce.Do(func() {
		conn, err := grpc.NewClient("localhost:40000", grpc.WithTransportCredentials(insecure.NewCredentials()))
		if err != nil {
			panic(err)
		}
		client = services.NewMarketServiceClient(conn)
	})
	return client
}
