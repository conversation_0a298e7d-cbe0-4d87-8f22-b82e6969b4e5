package repository

import (
	"context"
	"os"
	"testing"

	"github.com/jmoiron/sqlx"
	_ "github.com/lib/pq"
	"gitlab.com/a7923/athena-go/internal/adcrawl/domain"
)

func TestCampaignBatchInsertAfterDelete(t *testing.T) {
	db, err := sqlx.ConnectContext(context.Background(), "postgres", os.Getenv("AD_DB_URL"))
	if err != nil {
		t.Fatal(err)
	}
	defer db.Close()
	postgresCampaignRepository := NewPostgresCampaignRepository(db)
	campaigns := []domain.Campaign{
		{
			ID:              int64(120223168765980116),
			Name:            "TL - CHÍNH - HADES - SANDERMA - 08-04-2025 - Bản sao",
			Status:          "PAUSED",
			EffectiveStatus: "PAUSED",
			AdAccountID:     int64(****************),
			Objective:       "OUTCOME_ENGAGEMENT",
			MarketerID:      int64(118),
		},
		{
			ID:              int64(120223122814810116),
			Name:            "SANder",
			Status:          "ACTIVE",
			EffectiveStatus: "ACTIVE",
			AdAccountID:     int64(****************),
			Objective:       "OUTCOME_ENGAGEMENT",
			MarketerID:      int64(118),
		},
	}

	withElapsedTime(t, func() {
		t.Run("trivial", func(t *testing.T) {
			if err := postgresCampaignRepository.BatchInsert(context.Background(), campaigns, false); err != nil {
				t.Fatal(err)
			}
		})
	})
}

func TestUpdateCountryIDForCampaigns(t *testing.T) {
	db, err := sqlx.ConnectContext(context.Background(), "postgres", os.Getenv("AD_DB_URL"))
	if err != nil {
		t.Fatal(err)
	}
	defer db.Close()
	postgresCampaignRepository := NewPostgresCampaignRepository(db)
	if err := postgresCampaignRepository.BulkUpdateCountryID(context.Background()); err != nil {
		t.Fatal(err)
	}
}
