syntax = "proto3";

package exmsg.models;

import "google/protobuf/timestamp.proto";

option go_package = "gitlab.com/a7923/athena-go/proto/exmsg/models;models";

// Lead represents a lead in the system
message Lead {
  // Basic fields
  int64 id = 1;
  google.protobuf.Timestamp created_at = 2;
  google.protobuf.Timestamp updated_at = 3;

  // Required fields
  int64 order_id = 4;
  int64 state = 5;

  // fields
  int64 user_id = 6;
  int64 current_care_id = 7;
  google.protobuf.Timestamp last_updated_state = 8;
  int64 updated_by = 9;
  bool ignore_duplicate_warning = 10;
  google.protobuf.Timestamp form_captured_at = 11;

  // UTM tracking fields
  string utm_source = 12;  // Example: facebook
  string utm_medium = 13;  // ad.id
  string utm_campaign = 14;  // campaign.id
  string utm_term = 15;  // adset.id
  string link = 16;

  // Care related fields
  int64 last_care_id = 17;
  int64 last_care_item_id = 18;
  int64 last_care_reason_id = 19;

  int64 lead_type = 20;

  int64 country_id = 21;
}

message LeadCare {
  // Timestamp when the lead care entry was created.
  google.protobuf.Timestamp created_at = 1;

  // Timestamp when the lead care entry was last updated.
  google.protobuf.Timestamp updated_at = 2;

  // Unique identifier for the lead care entry.
  int64 id = 3;

  // ID of the user associated with this lead care entry.
  int64 user_id = 4;

  // ID of the lead associated with this lead care entry.
  int64 lead_id = 5;

  // ID of the shift associated with this lead care entry (nullable).
  int64 shift_id = 6;

  // State of the lead care entry (e.g., 3 by default).
  int64 state = 7;

  // ID of the user who updated this entry.
  int64 updated_by = 8;
}

message LeadCareItem {
  // Timestamp when the lead care item was created.
  google.protobuf.Timestamp created_at = 1;

  // Timestamp when the lead care item was last updated.
  google.protobuf.Timestamp updated_at = 2;

  // Unique identifier for the lead care item.
  int64 id = 3;

  // ID of the associated lead care entry.
  int64 lead_care_id = 4;

  // ID of the associated care reason (nullable).
  int64 reason_id = 5;

  // Number of times the reason is repeated (nullable).
  int64 times_repeat_reason = 6;

  // Optional note related to the lead care item.
  string note = 7;

  // ID of the creator of this lead care item.
  int64 creator_id = 8;

  // ID of the associated lead.
  int64 lead_id = 9;
}

message CareReason {
  // Timestamp when the care reason was created.
  google.protobuf.Timestamp created_at = 1;

  // Timestamp when the care reason was last updated.
  google.protobuf.Timestamp updated_at = 2;

  // Unique identifier for the care reason.
  int64 id = 3;

  // Name of the care reason.
  string name = 4;

  // ID of the associated country (nullable).
  int64 country_id = 5;

  // ID of the associated company (nullable).
  int64 company_id = 6;

  // ID of the creator of this care reason.
  int64 creator_id = 7;

  // ID of the user who last updated this care reason.
  int64 updated_by = 8;

  // Timestamp when the care reason was deleted (nullable).
  google.protobuf.Timestamp deleted_at = 9;

  // ID of the user who deleted this care reason (nullable).
  int64 deleted_by = 10;

  // A unique key for the reason (nullable).
  string reason_key = 11;

  // State of the care reason (nullable).
  int64 state = 12;

  // Flag indicating if the report should ignore this care reason.
  bool report_ignore = 13;

  // Sort order number for this care reason.
  int64 sort_no = 14;
}