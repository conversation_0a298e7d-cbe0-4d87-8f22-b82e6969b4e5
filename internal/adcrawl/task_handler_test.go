package adcrawl

import (
	"os"
	"testing"
	"time"

	"github.com/hibiken/asynq"
)

func TestForFun(t *testing.T) {
	url := os.Getenv("SPENDING_FETCH_URL")
	accessToken := os.Getenv("SPENDING_FETCH_ACCESS_TOKEN")
	client := asynq.NewClient(asynq.RedisClientOpt{Addr: "localhost:16379"})
	defer client.Close()
	extra := map[string]interface{}{}
	t.Logf("url: %s", url)
	if err := enqueueNextTask(client, string(TypeFetchSpendings), url, accessToken, extra,
		asynq.Queue("fetch"), asynq.ProcessIn(30*time.Second),
		asynq.MaxRetry(3)); err != nil {
		t.Fatalf("failed to enqueue task: %v", err)
	}
	time.Sleep(1 * time.Hour)
}
