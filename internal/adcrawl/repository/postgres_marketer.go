package repository

import (
	"context"
	"fmt"

	"github.com/Masterminds/squirrel"
	"github.com/jmoiron/sqlx"
	"gitlab.com/a7923/athena-go/internal/adcrawl/domain"
	"gitlab.com/a7923/athena-go/pkg/logger"
)

var _ domain.MarketerRepository = (*PostgresMarketerRepository)(nil)

type PostgresMarketerRepository struct {
	db           *sqlx.DB
	queryBuilder squirrel.StatementBuilderType
}

func NewPostgresMarketerRepository(db *sqlx.DB) *PostgresMarketerRepository {
	return &PostgresMarketerRepository{
		db:           db,
		queryBuilder: squirrel.StatementBuilder.PlaceholderFormat(squirrel.Dollar),
	}
}

func (r *PostgresMarketerRepository) List(ctx context.Context, companyID int64, sb domain.SortByParam) (*domain.ListMarketerResponse, error) {
	q := r.queryBuilder.Select("distinct marketer_id as id, marketer_name as name").
		From("fb.analytic_accounts").
		Where(squirrel.Eq{"company_id": companyID}).
		OrderBy(fmt.Sprintf("%s %s", sb.Field, sb.Order))

	if sb.Field != "id" {
		q = q.OrderBy("id asc") // additional sort by id for stable sort
	}
	stmt, args, err := q.ToSql()
	if err != nil {
		return nil, err
	}

	var marketers []domain.Marketer
	if err = r.db.SelectContext(ctx, &marketers, stmt, args...); err != nil {
		logger.AthenaLogger.Errorf("failed to list marketers: %v", err)
		return nil, err
	}

	return &domain.ListMarketerResponse{
		Marketers: marketers,
		Total:     int64(len(marketers)),
	}, nil
}

func (r *PostgresMarketerRepository) ListByAdAccountIDs(ctx context.Context, companyID int64, adAccountIDs []int64) (*domain.ListMarketerByAdAccountIDsResponse, error) {
	q := r.queryBuilder.Select("id as ad_account_id, marketer_id").
		From("fb.adaccounts").
		Where(squirrel.And{
			squirrel.Eq{"company_id": companyID},
			squirrel.Eq{"id": adAccountIDs},
		})
	stmt, args, err := q.ToSql()
	if err != nil {
		return nil, err
	}

	rows, err := r.db.QueryContext(ctx, stmt, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var pairs []domain.AdAccountMarketerPair
	var pair domain.AdAccountMarketerPair
	for rows.Next() {
		if err := rows.Scan(&pair.AdAccountID, &pair.MarketerID); err != nil {
			return nil, err
		}
		pairs = append(pairs, pair)
	}

	return &domain.ListMarketerByAdAccountIDsResponse{
		Pairs: pairs,
	}, nil
}
