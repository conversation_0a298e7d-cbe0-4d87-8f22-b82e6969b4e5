package handlers

import (
	"github.com/goccy/go-json"
	"gitlab.com/a7923/athena-go/cmd/apis/analytics-api/request"
	"gitlab.com/a7923/athena-go/pkg/app"
	"gitlab.com/a7923/athena-go/pkg/common"
	"gitlab.com/a7923/athena-go/pkg/logger"
	transhttp "gitlab.com/a7923/athena-go/pkg/transport"
	"gitlab.com/a7923/athena-go/proto/exmsg/services"
	"net/http"
	"time"
)

type TelesaleDashboardHandler struct {
	Producer        app.PublisherInterface
	AnalyticsClient services.AnalyticService
}

func (h *TelesaleDashboardHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	defer func() {
		logger.AthenaLogger.Debugw("Processed request", "url", r.URL.Path, "took", time.Since(start).Milliseconds())
	}()
	req, err := request.ParseTelesaleDashboardRequest(r)
	if err != nil {
		transhttp.RespondJSONFull(w, http.StatusBadRequest, common.NewErrorHTTPResponse(err.Error()))
		return
	}

	ctx := r.Context()
	dbResponse, err := h.AnalyticsClient.QueryTelesaleDashboardOverview(ctx, req)
	if err != nil {
		transhttp.RespondJSONFull(w, http.StatusBadRequest, common.NewErrorHTTPResponse(err.Error()))
		return
	}

	out := make([]map[string]interface{}, 0)
	for _, row := range dbResponse.Data {
		m := make(map[string]interface{})
		_ = json.Unmarshal(row.Values, &m)
		out = append(out, m)
	}

	transhttp.RespondJSON(w, http.StatusOK, map[string]interface{}{
		"success": true,
		"data":    out,
	})
}
