// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: proto/models/ad.proto

package models

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Ad struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Id              int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name            string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Status          string                 `protobuf:"bytes,3,opt,name=status,proto3" json:"status,omitempty"`
	EffectiveStatus string                 `protobuf:"bytes,4,opt,name=effective_status,json=effectiveStatus,proto3" json:"effective_status,omitempty"`
	PageId          string                 `protobuf:"bytes,5,opt,name=page_id,json=pageId,proto3" json:"page_id,omitempty"`
	ObjectStoryId   *string                `protobuf:"bytes,6,opt,name=object_story_id,json=objectStoryId,proto3,oneof" json:"object_story_id,omitempty"`
	ObjectStorySpec []byte                 `protobuf:"bytes,7,opt,name=object_story_spec,json=objectStorySpec,proto3,oneof" json:"object_story_spec,omitempty"`
	ImageUrl        *string                `protobuf:"bytes,8,opt,name=image_url,json=imageUrl,proto3,oneof" json:"image_url,omitempty"`
	AdsetId         int64                  `protobuf:"varint,9,opt,name=adset_id,json=adsetId,proto3" json:"adset_id,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *Ad) Reset() {
	*x = Ad{}
	mi := &file_proto_models_ad_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Ad) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Ad) ProtoMessage() {}

func (x *Ad) ProtoReflect() protoreflect.Message {
	mi := &file_proto_models_ad_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Ad.ProtoReflect.Descriptor instead.
func (*Ad) Descriptor() ([]byte, []int) {
	return file_proto_models_ad_proto_rawDescGZIP(), []int{0}
}

func (x *Ad) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Ad) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Ad) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *Ad) GetEffectiveStatus() string {
	if x != nil {
		return x.EffectiveStatus
	}
	return ""
}

func (x *Ad) GetPageId() string {
	if x != nil {
		return x.PageId
	}
	return ""
}

func (x *Ad) GetObjectStoryId() string {
	if x != nil && x.ObjectStoryId != nil {
		return *x.ObjectStoryId
	}
	return ""
}

func (x *Ad) GetObjectStorySpec() []byte {
	if x != nil {
		return x.ObjectStorySpec
	}
	return nil
}

func (x *Ad) GetImageUrl() string {
	if x != nil && x.ImageUrl != nil {
		return *x.ImageUrl
	}
	return ""
}

func (x *Ad) GetAdsetId() int64 {
	if x != nil {
		return x.AdsetId
	}
	return 0
}

var File_proto_models_ad_proto protoreflect.FileDescriptor

var file_proto_models_ad_proto_rawDesc = string([]byte{
	0x0a, 0x15, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61,
	0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x22, 0xd7, 0x02, 0x0a, 0x02, 0x41, 0x64, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x29, 0x0a, 0x10, 0x65, 0x66, 0x66, 0x65,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0f, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x61, 0x67, 0x65, 0x49, 0x64, 0x12, 0x2b, 0x0a, 0x0f,
	0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0d, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x53,
	0x74, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x2f, 0x0a, 0x11, 0x6f, 0x62, 0x6a,
	0x65, 0x63, 0x74, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0c, 0x48, 0x01, 0x52, 0x0f, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x53, 0x74,
	0x6f, 0x72, 0x79, 0x53, 0x70, 0x65, 0x63, 0x88, 0x01, 0x01, 0x12, 0x20, 0x0a, 0x09, 0x69, 0x6d,
	0x61, 0x67, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x48, 0x02, 0x52,
	0x08, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x55, 0x72, 0x6c, 0x88, 0x01, 0x01, 0x12, 0x19, 0x0a, 0x08,
	0x61, 0x64, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07,
	0x61, 0x64, 0x73, 0x65, 0x74, 0x49, 0x64, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x6f, 0x62, 0x6a, 0x65,
	0x63, 0x74, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x42, 0x14, 0x0a, 0x12, 0x5f,
	0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x73, 0x70, 0x65,
	0x63, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x42,
	0x36, 0x5a, 0x34, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x61, 0x37,
	0x39, 0x32, 0x33, 0x2f, 0x61, 0x74, 0x68, 0x65, 0x6e, 0x61, 0x2d, 0x67, 0x6f, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2f, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x3b, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_proto_models_ad_proto_rawDescOnce sync.Once
	file_proto_models_ad_proto_rawDescData []byte
)

func file_proto_models_ad_proto_rawDescGZIP() []byte {
	file_proto_models_ad_proto_rawDescOnce.Do(func() {
		file_proto_models_ad_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proto_models_ad_proto_rawDesc), len(file_proto_models_ad_proto_rawDesc)))
	})
	return file_proto_models_ad_proto_rawDescData
}

var file_proto_models_ad_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_proto_models_ad_proto_goTypes = []any{
	(*Ad)(nil), // 0: exmsg.models.Ad
}
var file_proto_models_ad_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_proto_models_ad_proto_init() }
func file_proto_models_ad_proto_init() {
	if File_proto_models_ad_proto != nil {
		return
	}
	file_proto_models_ad_proto_msgTypes[0].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proto_models_ad_proto_rawDesc), len(file_proto_models_ad_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_proto_models_ad_proto_goTypes,
		DependencyIndexes: file_proto_models_ad_proto_depIdxs,
		MessageInfos:      file_proto_models_ad_proto_msgTypes,
	}.Build()
	File_proto_models_ad_proto = out.File
	file_proto_models_ad_proto_goTypes = nil
	file_proto_models_ad_proto_depIdxs = nil
}
