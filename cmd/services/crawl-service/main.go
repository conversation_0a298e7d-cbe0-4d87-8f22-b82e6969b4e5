package main

import (
	"fmt"
	"net/http"
	"time"

	"github.com/hibiken/asynq"
	"github.com/hibiken/asynq/x/metrics"
	_ "github.com/lib/pq"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/spf13/viper"
	"gitlab.com/a7923/athena-go/cmd/services/crawl-service/handlers"
	"gitlab.com/a7923/athena-go/internal/adcrawl"
	"gitlab.com/a7923/athena-go/internal/adcrawl/domain"
	"gitlab.com/a7923/athena-go/internal/adcrawl/repository"
	"gitlab.com/a7923/athena-go/pkg/app"
	"gitlab.com/a7923/athena-go/pkg/common"
	"gitlab.com/a7923/athena-go/pkg/dbtool"
	"gitlab.com/a7923/athena-go/pkg/logger"
	"gitlab.com/a7923/athena-go/proto/exmsg/services"
)

func main() {
	svc := app.NewGRPCService(common.ServiceNameCrawl)

	redisAddr := fmt.Sprintf("%s:%s", viper.GetString("redis.host"), viper.GetString("redis.port"))
	taskClient := asynq.NewClient(asynq.RedisClientOpt{Addr: redisAddr})
	scheduler := asynq.NewScheduler(asynq.RedisClientOpt{Addr: redisAddr}, nil)

	connMng, err := dbtool.NewConnectionManager(dbtool.DBTypePostgreSQL, nil)
	if err != nil {
		logger.AthenaLogger.Fatalf("could not connect to db: %v", err)
	}
	db := connMng.GetConnection()
	var (
		accRepo      domain.AdAccountRepository = repository.NewPostgresAdAccountRepository(db)
		camRepo      domain.CampaignRepository  = repository.NewPostgresCampaignRepository(db)
		adsetRepo    domain.AdSetRepository     = repository.NewPostgresAdsetRepository(db)
		adRepo       domain.AdRepository        = repository.NewPostgresAdRepository(db)
		spendRepo    domain.SpendingRepository  = repository.NewPostgresSpendingRepository(db)
		tokenRepo    domain.TokenRepository     = repository.NewPostgresTokenRepository(db)
		marketerRepo domain.MarketerRepository  = repository.NewPostgresMarketerRepository(db)
	)

	fetcher := adcrawl.NewFetcher(adcrawl.FetcherConfig{
		RequestTimeout: time.Duration(viper.GetInt("crawl.request_timeout")) * time.Second,
	})

	insertTaskCfg := adcrawl.TaskConfig{
		TaskTimeout: time.Duration(viper.GetInt("crawl.insert.task_timeout")) * time.Second,
		MaxRetry:    viper.GetInt("crawl.insert.max_retry"),
	}
	ih := adcrawl.NewInsertTaskHandler(taskClient, accRepo, camRepo, adsetRepo, adRepo, spendRepo, marketerRepo, insertTaskCfg)

	fetchTaskCfg := adcrawl.TaskConfig{
		TaskTimeout: time.Duration(viper.GetInt("crawl.fetch.task_timeout")) * time.Second,
		MaxRetry:    viper.GetInt("crawl.fetch.max_retry"),
	}
	fh := adcrawl.NewFetchTaskHandler(fetcher, taskClient, fetchTaskCfg, tokenRepo)

	seedTaskCfg := adcrawl.TaskConfig{
		TaskTimeout: time.Duration(viper.GetInt("crawl.seed.task_timeout")) * time.Second,
		MaxRetry:    viper.GetInt("crawl.seed.max_retry"),
	}
	sh := adcrawl.NewSeedTaskHandler(taskClient, seedTaskCfg, accRepo, tokenRepo)

	updateTaskCfg := adcrawl.TaskConfig{
		TaskTimeout: time.Duration(viper.GetInt("crawl.update.task_timeout")) * time.Second,
		MaxRetry:    viper.GetInt("crawl.update.max_retry"),
	}
	uh := adcrawl.NewUpdateTaskHandler(taskClient, updateTaskCfg, spendRepo, camRepo)

	crawler := adcrawl.NewCronCrawler(redisAddr, ih, fh, sh, uh, adcrawl.Config{
		NumberOfWorkers: viper.GetInt("crawl.number_of_workers"),
		RetryBaseWait: map[string]time.Duration{
			// "critical_insert": time.Duration(viper.GetInt("crawl.insert.critical_retry_base_wait")) * time.Second,
			// "critical_fetch":  time.Duration(viper.GetInt("crawl.fetch.critical_retry_base_wait")) * time.Second,
			"insert": time.Duration(viper.GetInt("crawl.insert.retry_base_wait")) * time.Second,
			"fetch":  time.Duration(viper.GetInt("crawl.fetch.retry_base_wait")) * time.Second,
			"seed":   time.Duration(viper.GetInt("crawl.seed.retry_base_wait")) * time.Second,
			"update": time.Duration(viper.GetInt("crawl.update.retry_base_wait")) * time.Second,
		},
		Priority: map[string]int{
			"critical_insert": viper.GetInt("crawl.insert.critical_priority"),
			"critical_fetch":  viper.GetInt("crawl.fetch.critical_priority"),
			"insert":          viper.GetInt("crawl.insert.priority"),
			"fetch":           viper.GetInt("crawl.fetch.priority"),
			"seed":            viper.GetInt("crawl.seed.priority"),
			"update":          viper.GetInt("crawl.update.priority"),
		},
	})

	go crawler.Start()

	scheduledInterval := viper.GetInt("crawl.scheduled_interval")
	grpcSvc := handlers.NewCrawlHandler(taskClient, scheduler, fetchTaskCfg, scheduledInterval, tokenRepo)

	if viper.GetBool("crawl.register_when_start") {
		if err := grpcSvc.RegisterWhenStart(db); err != nil {
			logger.AthenaLogger.Fatal(err)
		}
	}

	if err = services.RegisterCrawlServiceHandler(svc.Server(), grpcSvc); err != nil {
		logger.AthenaLogger.Fatal(err)
	}

	// Export asynq metrics
	inspector := asynq.NewInspector(asynq.RedisClientOpt{
		Addr: redisAddr,
	})
	collector := metrics.NewQueueMetricsCollector(inspector)
	prometheus.MustRegister(collector)
	http.Handle("/metrics", promhttp.Handler())
	go func() {
		if err := http.ListenAndServe(":9000", nil); err != nil {
			logger.AthenaLogger.Fatal("failed to start metrics server: ", err)
		}
	}()

	if err = svc.Run(); err != nil {
		logger.AthenaLogger.Fatal(err)
	}
}
