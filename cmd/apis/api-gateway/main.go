// gateway.go
package main

import (
	"gitlab.com/a7923/athena-go/cmd/apis/api-gateway/internal"
	"gitlab.com/a7923/athena-go/pkg/app"
	"gitlab.com/a7923/athena-go/pkg/config"
	"gitlab.com/a7923/athena-go/pkg/dbtool"
	transhttp "gitlab.com/a7923/athena-go/pkg/transport"
	"log"
	"net/http"
)

func main() {
	service := app.NewAPI(nil)

	redis, err := dbtool.CreateRedisConnection(nil)
	if err != nil {
		log.Fatal("Failed to create redis connection: ", err)
	}

	proxy := internal.NewReverseProxy(redis, service.Options().Registry, nil)
	maxIdleConns := config.ViperGetIntWithDefault("http.max_idle_conns", 120)
	http.DefaultTransport.(*http.Transport).MaxIdleConnsPerHost = maxIdleConns

	// Set up routes with authentication middleware
	httpHandler := http.NewServeMux()
	httpHandler.Handle("/", proxy)

	// Register handler
	service.Handle("/", httpHandler)
	service.Handle("/health-check", new(transhttp.HealthCheckHandler))

	// Run the service
	if err := service.Run(); err != nil {
		log.Fatal(err)
	}
}
