-- migrate:up
CREATE TABLE IF NOT EXISTS fb.analytic_accounts (
    id BIGINT NOT NULL,
    company_id BIGINT NOT NULL,

    username TEXT,
    profile_pic_url TEXT,
    token TEXT,
    marketer_id BIGINT,
    marketer_name TEXT,
    available boolean DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ,
    deleted_at TIMESTAMPTZ
);
CREATE INDEX IF NOT EXISTS idx_analytic_accounts_username ON fb.analytic_accounts (username);
CREATE INDEX IF NOT EXISTS idx_analytic_accounts_company_id_id ON fb.analytic_accounts (company_id, id);
ALTER TABLE fb.analytic_accounts ADD CONSTRAINT unique_company_id_id UNIQUE (company_id, id);


-- migrate:down
DROP INDEX IF EXISTS fb.idx_analytic_accounts_company_id_id;
DROP INDEX IF EXISTS fb.idx_analytic_accounts_username;
ALTER TABLE fb.analytic_accounts DROP CONSTRAINT IF EXISTS unique_company_id_id;
DROP TABLE IF EXISTS fb.analytic_accounts;
