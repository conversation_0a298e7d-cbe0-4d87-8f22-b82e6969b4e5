package domain

import (
	"context"
	"time"

	"github.com/spf13/cast"
)

type Campaign struct {
	ID                  int64     `json:"id"`
	CompanyID           *int64    `json:"company_id"`
	Name                string    `json:"name"`
	Status              string    `json:"status"`
	EffectiveStatus     string    `json:"effective_status"`
	Objective           string    `json:"objective"`
	Landing             string    `json:"landing"`
	MarketerID          int64     `json:"marketer_id"`
	AdAccountID         int64     `json:"account_id"`
	AdAccountName       string    `json:"-"`
	AnalyticAccountID   int64     `json:"-"`
	AnalyticAccountName string    `json:"-"`
	UpdatedAt           time.Time `json:"-"`
	UpdatedByID         int64     `json:"-"`
}

type CampaignRepository interface {
	BatchInsert(ctx context.Context, campaigns []Campaign, scheduled bool) error
	Update(ctx context.Context, campaign *Campaign, updatedFields []string) error
	BulkUpdate(ctx context.Context, campaignIDs []int64, companyID int64, updateFields []string, updateValues []interface{}) (time.Time, error)
	BulkUpdateCountryID(ctx context.Context) error
}

type ListCampaignsResponse struct {
	Campaigns     []Campaign `json:"campaigns"`
	Total         int64      `json:"total"`
	CountNotInBiz int64      `json:"count_not_in_biz"`
}

type CampaignFilter struct {
	UserID             *int64 // In case user doesn't have permission to see all campaigns (only see campaigns assigned to that user)
	SearchName         *string
	Status             *string
	AnalyticAccountIDs []int64
	AdAccountIDs       []int64
	MarketerIDs        []int64
}

type DummyCampaign struct {
	ID              string `json:"id"`
	Name            string `json:"name"`
	Status          string `json:"status"`
	EffectiveStatus string `json:"effective_status"`
	AdAccountID     string `json:"account_id"`
	Objective       string `json:"objective"`
}

func (c DummyCampaign) ToCampaign() Campaign {
	return Campaign{
		ID:              cast.ToInt64(c.ID),
		Name:            c.Name,
		Status:          c.Status,
		EffectiveStatus: c.EffectiveStatus,
		AdAccountID:     cast.ToInt64(c.AdAccountID),
		Objective:       c.Objective,
	}
}

type LandingPage struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}
